package workflows

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"

	"github.com/cvleap/platform/temporal/activities"
)

// JobSubmissionWorkflow represents the main job application workflow
type JobSubmissionWorkflow struct {
	JobID       string            `json:"job_id"`
	UserID      string            `json:"user_id"`
	JobSiteURL  string            `json:"job_site_url"`
	JobSiteDomain string          `json:"job_site_domain"`
	Runtime     string            `json:"runtime"`
	Credentials map[string]string `json:"credentials"`
	Selectors   map[string]string `json:"selectors"`
	UserTier    string            `json:"user_tier"`
}

// JobSubmissionResult contains the workflow execution result
type JobSubmissionResult struct {
	Success       bool              `json:"success"`
	ApplicationID string            `json:"application_id,omitempty"`
	Screenshots   []string          `json:"screenshots,omitempty"`
	ErrorMessage  string            `json:"error_message,omitempty"`
	Cost          float64           `json:"cost"`
	Duration      time.Duration     `json:"duration"`
	Metrics       map[string]interface{} `json:"metrics"`
}

// JobSubmissionWorkflowName is the workflow name for registration
const JobSubmissionWorkflowName = "JobSubmissionWorkflow"

// JobSubmissionWorkflowImpl implements the job submission workflow
func JobSubmissionWorkflowImpl(ctx workflow.Context, input JobSubmissionWorkflow) (*JobSubmissionResult, error) {
	logger := workflow.GetLogger(ctx)
	logger.Info("Starting job submission workflow", "jobId", input.JobID, "userId", input.UserID)

	// Initialize result
	result := &JobSubmissionResult{
		Metrics: make(map[string]interface{}),
	}
	startTime := workflow.Now(ctx)

	// Set up activity options with retry policy
	activityOptions := workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 10,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        time.Second,
			BackoffCoefficient:     2.0,
			MaximumInterval:        time.Minute,
			MaximumAttempts:        5,
			NonRetryableErrorTypes: []string{"ValidationError", "AuthenticationError"},
		},
	}
	ctx = workflow.WithActivityOptions(ctx, activityOptions)

	// Step 1: Validate job site and user permissions
	var validationResult activities.ValidationResult
	err := workflow.ExecuteActivity(ctx, activities.ValidateJobSiteActivity, activities.ValidationInput{
		JobSiteURL:    input.JobSiteURL,
		JobSiteDomain: input.JobSiteDomain,
		UserID:        input.UserID,
		UserTier:      input.UserTier,
	}).Get(ctx, &validationResult)

	if err != nil {
		logger.Error("Job site validation failed", "error", err)
		result.ErrorMessage = fmt.Sprintf("Validation failed: %v", err)
		return result, nil // Don't fail workflow, return result with error
	}

	// Step 2: Select optimal runtime
	var runtimeResult activities.RuntimeSelectionResult
	err = workflow.ExecuteActivity(ctx, activities.SelectRuntimeActivity, activities.RuntimeSelectionInput{
		PreferredRuntime: input.Runtime,
		UserTier:        input.UserTier,
		JobSiteDomain:   input.JobSiteDomain,
		ResourceRequirements: activities.ResourceRequirements{
			CPU:     "500m",
			Memory:  "256Mi",
			Timeout: "300s",
		},
	}).Get(ctx, &runtimeResult)

	if err != nil {
		logger.Error("Runtime selection failed", "error", err)
		result.ErrorMessage = fmt.Sprintf("Runtime selection failed: %v", err)
		return result, nil
	}

	logger.Info("Selected runtime", "runtime", runtimeResult.SelectedRuntime)
	result.Metrics["selected_runtime"] = runtimeResult.SelectedRuntime

	// Step 3: Provision sandbox with compensation
	var sandboxResult activities.SandboxProvisionResult
	compensationCtx := workflow.WithActivityOptions(ctx, workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 2,
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:    time.Second,
			BackoffCoefficient: 1.5,
			MaximumInterval:    time.Second * 30,
			MaximumAttempts:    3,
		},
	})

	err = workflow.ExecuteActivity(compensationCtx, activities.ProvisionSandboxActivity, activities.SandboxProvisionInput{
		JobID:         input.JobID,
		UserID:        input.UserID,
		Runtime:       runtimeResult.SelectedRuntime,
		JobSiteDomain: input.JobSiteDomain,
		Resources:     runtimeResult.AllocatedResources,
	}).Get(ctx, &sandboxResult)

	if err != nil {
		logger.Error("Sandbox provisioning failed", "error", err)
		result.ErrorMessage = fmt.Sprintf("Sandbox provisioning failed: %v", err)
		return result, nil
	}

	// Register compensation for cleanup
	defer func() {
		// Always cleanup sandbox resources
		compensationCtx := workflow.WithActivityOptions(workflow.NewDisconnectedContext(ctx), workflow.ActivityOptions{
			StartToCloseTimeout: time.Minute * 2,
			RetryPolicy: &temporal.RetryPolicy{
				InitialInterval: time.Second,
				MaximumAttempts: 3,
			},
		})

		var cleanupResult activities.CleanupResult
		cleanupErr := workflow.ExecuteActivity(compensationCtx, activities.CleanupSandboxActivity, activities.CleanupInput{
			SandboxID: sandboxResult.SandboxID,
			Runtime:   runtimeResult.SelectedRuntime,
		}).Get(compensationCtx, &cleanupResult)

		if cleanupErr != nil {
			logger.Error("Cleanup failed", "error", cleanupErr)
		} else {
			logger.Info("Sandbox cleanup completed", "cost", cleanupResult.Cost)
			result.Cost += cleanupResult.Cost
		}
	}()

	logger.Info("Sandbox provisioned", "sandboxId", sandboxResult.SandboxID)
	result.Metrics["sandbox_id"] = sandboxResult.SandboxID
	result.Metrics["provision_time"] = sandboxResult.ProvisionTime

	// Step 4: Inject credentials securely
	var credentialsResult activities.CredentialsResult
	err = workflow.ExecuteActivity(ctx, activities.InjectCredentialsActivity, activities.CredentialsInput{
		SandboxID:   sandboxResult.SandboxID,
		Credentials: input.Credentials,
		TTL:         time.Hour, // 1 hour TTL
	}).Get(ctx, &credentialsResult)

	if err != nil {
		logger.Error("Credentials injection failed", "error", err)
		result.ErrorMessage = fmt.Sprintf("Credentials injection failed: %v", err)
		return result, nil
	}

	// Step 5: Execute job application with progress tracking
	var applicationResult activities.ApplicationResult
	applicationCtx := workflow.WithActivityOptions(ctx, workflow.ActivityOptions{
		StartToCloseTimeout: time.Minute * 15, // Longer timeout for application
		HeartbeatTimeout:    time.Second * 30, // Heartbeat for progress
		RetryPolicy: &temporal.RetryPolicy{
			InitialInterval:        time.Second * 5,
			BackoffCoefficient:     1.5,
			MaximumInterval:        time.Minute * 2,
			MaximumAttempts:        3,
			NonRetryableErrorTypes: []string{"ApplicationRejected", "SiteUnavailable"},
		},
	})

	err = workflow.ExecuteActivity(applicationCtx, activities.ExecuteApplicationActivity, activities.ApplicationInput{
		SandboxID:     sandboxResult.SandboxID,
		JobSiteURL:    input.JobSiteURL,
		Selectors:     input.Selectors,
		EnableTerminal: true,
	}).Get(ctx, &applicationResult)

	if err != nil {
		logger.Error("Job application failed", "error", err)
		result.ErrorMessage = fmt.Sprintf("Application execution failed: %v", err)
		return result, nil
	}

	// Step 6: Capture results and artifacts
	var captureResult activities.CaptureResult
	err = workflow.ExecuteActivity(ctx, activities.CaptureResultsActivity, activities.CaptureInput{
		SandboxID:     sandboxResult.SandboxID,
		ApplicationID: applicationResult.ApplicationID,
	}).Get(ctx, &captureResult)

	if err != nil {
		logger.Warn("Results capture failed", "error", err)
		// Don't fail workflow for capture errors
	} else {
		result.Screenshots = captureResult.Screenshots
		result.Metrics["artifacts"] = captureResult.Artifacts
	}

	// Calculate final results
	result.Success = applicationResult.Success
	result.ApplicationID = applicationResult.ApplicationID
	result.Duration = workflow.Now(ctx).Sub(startTime)
	result.Cost += sandboxResult.Cost + applicationResult.Cost

	// Add final metrics
	result.Metrics["total_duration"] = result.Duration.Seconds()
	result.Metrics["steps_completed"] = len(applicationResult.Steps)
	result.Metrics["success_rate"] = map[string]interface{}{
		"current": result.Success,
		"job_site": input.JobSiteDomain,
	}

	logger.Info("Job submission workflow completed",
		"success", result.Success,
		"duration", result.Duration,
		"cost", result.Cost,
		"applicationId", result.ApplicationID)

	return result, nil
}

// JobSubmissionSignal defines signals that can be sent to the workflow
type JobSubmissionSignal struct {
	Action string                 `json:"action"` // "pause", "resume", "cancel"
	Data   map[string]interface{} `json:"data,omitempty"`
}

// HandleJobSubmissionSignal processes signals sent to the workflow
func HandleJobSubmissionSignal(ctx workflow.Context, signal JobSubmissionSignal) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("Received signal", "action", signal.Action)

	switch signal.Action {
	case "pause":
		// Implement pause logic
		return workflow.NewContinueAsNewError(ctx, JobSubmissionWorkflowName, signal.Data)
	case "resume":
		// Implement resume logic
		logger.Info("Resuming workflow")
		return nil
	case "cancel":
		// Implement cancellation logic
		return temporal.NewCanceledError("Workflow canceled by user")
	default:
		return fmt.Errorf("unknown signal action: %s", signal.Action)
	}
}

// JobSubmissionQuery defines queries that can be made to the workflow
type JobSubmissionQuery struct {
	QueryType string `json:"query_type"`
}

// HandleJobSubmissionQuery processes queries sent to the workflow
func HandleJobSubmissionQuery(ctx workflow.Context, query JobSubmissionQuery) (interface{}, error) {
	switch query.QueryType {
	case "status":
		return map[string]interface{}{
			"status":    "running",
			"timestamp": workflow.Now(ctx),
		}, nil
	case "progress":
		// Return current progress information
		return map[string]interface{}{
			"current_step": "executing_application",
			"progress":     0.75,
		}, nil
	default:
		return nil, fmt.Errorf("unknown query type: %s", query.QueryType)
	}
}
