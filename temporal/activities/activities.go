package activities

import (
	"context"
	"fmt"
	"time"

	"go.temporal.io/sdk/activity"
)

// ValidationInput contains input for job site validation
type ValidationInput struct {
	JobSiteURL    string `json:"job_site_url"`
	JobSiteDomain string `json:"job_site_domain"`
	UserID        string `json:"user_id"`
	UserTier      string `json:"user_tier"`
}

// ValidationResult contains validation results
type ValidationResult struct {
	Valid        bool              `json:"valid"`
	AllowedDomains []string        `json:"allowed_domains"`
	RateLimits   map[string]int    `json:"rate_limits"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// ValidateJobSiteActivity validates job site and user permissions
func ValidateJobSiteActivity(ctx context.Context, input ValidationInput) (*ValidationResult, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("Validating job site", "domain", input.JobSiteDomain, "user", input.UserID)

	// Heartbeat for long-running validation
	activity.RecordHeartbeat(ctx, "validating_domain")

	// Simulate validation logic
	allowedDomains := []string{
		"workday.com", "lever.co", "greenhouse.io", "bamboohr.com",
		"linkedin.com", "indeed.com", "glassdoor.com", "monster.com",
	}

	valid := false
	for _, domain := range allowedDomains {
		if input.JobSiteDomain == domain {
			valid = true
			break
		}
	}

	if !valid {
		return nil, fmt.Errorf("domain %s not in allowed list", input.JobSiteDomain)
	}

	// Check user tier permissions
	rateLimits := map[string]int{
		"basic":    10,  // 10 applications per day
		"premium":  50,  // 50 applications per day
		"enterprise": 200, // 200 applications per day
	}

	limit, exists := rateLimits[input.UserTier]
	if !exists {
		limit = 5 // Default limit
	}

	return &ValidationResult{
		Valid:          true,
		AllowedDomains: allowedDomains,
		RateLimits:     map[string]int{input.UserTier: limit},
		Metadata: map[string]interface{}{
			"validation_time": time.Now(),
			"user_tier":      input.UserTier,
		},
	}, nil
}

// RuntimeSelectionInput contains input for runtime selection
type RuntimeSelectionInput struct {
	PreferredRuntime     string               `json:"preferred_runtime"`
	UserTier            string               `json:"user_tier"`
	JobSiteDomain       string               `json:"job_site_domain"`
	ResourceRequirements ResourceRequirements `json:"resource_requirements"`
}

// ResourceRequirements defines resource needs
type ResourceRequirements struct {
	CPU     string `json:"cpu"`
	Memory  string `json:"memory"`
	Timeout string `json:"timeout"`
}

// RuntimeSelectionResult contains runtime selection results
type RuntimeSelectionResult struct {
	SelectedRuntime     string               `json:"selected_runtime"`
	AllocatedResources  ResourceRequirements `json:"allocated_resources"`
	EstimatedCost      float64              `json:"estimated_cost"`
	AvailabilityZone   string               `json:"availability_zone"`
}

// SelectRuntimeActivity selects the optimal runtime for execution
func SelectRuntimeActivity(ctx context.Context, input RuntimeSelectionInput) (*RuntimeSelectionResult, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("Selecting runtime", "preferred", input.PreferredRuntime, "tier", input.UserTier)

	activity.RecordHeartbeat(ctx, "selecting_runtime")

	// Runtime selection logic
	selectedRuntime := input.PreferredRuntime
	if selectedRuntime == "" {
		selectedRuntime = "docker-in-pod" // Default
	}

	// Upgrade to premium runtime for premium users
	if input.UserTier == "premium" && selectedRuntime == "docker-in-pod" {
		selectedRuntime = "firecracker"
	}

	// Cost estimation based on runtime
	costPerMinute := map[string]float64{
		"docker-in-pod": 0.001,  // $0.001 per minute
		"firecracker":   0.0015, // $0.0015 per minute
		"kubevirt":      0.002,  // $0.002 per minute
	}

	estimatedCost := costPerMinute[selectedRuntime] * 5 // Assume 5 minutes average

	return &RuntimeSelectionResult{
		SelectedRuntime:    selectedRuntime,
		AllocatedResources: input.ResourceRequirements,
		EstimatedCost:     estimatedCost,
		AvailabilityZone:  "us-east-1a",
	}, nil
}

// SandboxProvisionInput contains input for sandbox provisioning
type SandboxProvisionInput struct {
	JobID         string               `json:"job_id"`
	UserID        string               `json:"user_id"`
	Runtime       string               `json:"runtime"`
	JobSiteDomain string               `json:"job_site_domain"`
	Resources     ResourceRequirements `json:"resources"`
}

// SandboxProvisionResult contains provisioning results
type SandboxProvisionResult struct {
	SandboxID     string        `json:"sandbox_id"`
	PodName       string        `json:"pod_name,omitempty"`
	VMName        string        `json:"vm_name,omitempty"`
	IPAddress     string        `json:"ip_address"`
	ProvisionTime time.Duration `json:"provision_time"`
	Cost          float64       `json:"cost"`
}

// ProvisionSandboxActivity provisions a sandbox environment
func ProvisionSandboxActivity(ctx context.Context, input SandboxProvisionInput) (*SandboxProvisionResult, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("Provisioning sandbox", "runtime", input.Runtime, "jobId", input.JobID)

	startTime := time.Now()
	activity.RecordHeartbeat(ctx, "provisioning_sandbox")

	// Simulate provisioning time based on runtime
	provisionTime := map[string]time.Duration{
		"docker-in-pod": time.Millisecond * 800,  // 800ms
		"firecracker":   time.Millisecond * 600,  // 600ms
		"kubevirt":      time.Second * 2,         // 2s
	}

	time.Sleep(provisionTime[input.Runtime])

	sandboxID := fmt.Sprintf("sandbox-%s-%d", input.JobID, time.Now().Unix())
	
	result := &SandboxProvisionResult{
		SandboxID:     sandboxID,
		IPAddress:     "**********", // Simulated IP
		ProvisionTime: time.Since(startTime),
		Cost:          0.001, // Base provisioning cost
	}

	switch input.Runtime {
	case "docker-in-pod":
		result.PodName = fmt.Sprintf("pod-%s", sandboxID)
	case "firecracker", "kubevirt":
		result.VMName = fmt.Sprintf("vm-%s", sandboxID)
	}

	logger.Info("Sandbox provisioned", "sandboxId", sandboxID, "duration", result.ProvisionTime)
	return result, nil
}

// CredentialsInput contains input for credentials injection
type CredentialsInput struct {
	SandboxID   string            `json:"sandbox_id"`
	Credentials map[string]string `json:"credentials"`
	TTL         time.Duration     `json:"ttl"`
}

// CredentialsResult contains credentials injection results
type CredentialsResult struct {
	SecretName string    `json:"secret_name"`
	ExpiresAt  time.Time `json:"expires_at"`
	MountPath  string    `json:"mount_path"`
}

// InjectCredentialsActivity securely injects credentials into sandbox
func InjectCredentialsActivity(ctx context.Context, input CredentialsInput) (*CredentialsResult, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("Injecting credentials", "sandboxId", input.SandboxID)

	activity.RecordHeartbeat(ctx, "injecting_credentials")

	// Simulate credential injection
	secretName := fmt.Sprintf("creds-%s", input.SandboxID)
	expiresAt := time.Now().Add(input.TTL)

	return &CredentialsResult{
		SecretName: secretName,
		ExpiresAt:  expiresAt,
		MountPath:  "/tmp/credentials",
	}, nil
}

// ApplicationInput contains input for job application execution
type ApplicationInput struct {
	SandboxID      string            `json:"sandbox_id"`
	JobSiteURL     string            `json:"job_site_url"`
	Selectors      map[string]string `json:"selectors"`
	EnableTerminal bool              `json:"enable_terminal"`
}

// ApplicationResult contains application execution results
type ApplicationResult struct {
	Success       bool                   `json:"success"`
	ApplicationID string                 `json:"application_id,omitempty"`
	Steps         []ApplicationStep      `json:"steps"`
	Cost          float64                `json:"cost"`
	Duration      time.Duration          `json:"duration"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// ApplicationStep represents a step in the application process
type ApplicationStep struct {
	Name      string        `json:"name"`
	Status    string        `json:"status"`
	Duration  time.Duration `json:"duration"`
	Error     string        `json:"error,omitempty"`
}

// ExecuteApplicationActivity executes the job application
func ExecuteApplicationActivity(ctx context.Context, input ApplicationInput) (*ApplicationResult, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("Executing job application", "sandboxId", input.SandboxID, "url", input.JobSiteURL)

	startTime := time.Now()
	steps := []ApplicationStep{
		{Name: "navigate_to_site", Status: "pending"},
		{Name: "fill_application", Status: "pending"},
		{Name: "upload_resume", Status: "pending"},
		{Name: "submit_application", Status: "pending"},
	}

	// Simulate application steps with heartbeats
	for i := range steps {
		activity.RecordHeartbeat(ctx, fmt.Sprintf("executing_step_%s", steps[i].Name))
		
		stepStart := time.Now()
		// Simulate step execution
		time.Sleep(time.Second * 2)
		
		steps[i].Status = "completed"
		steps[i].Duration = time.Since(stepStart)
		
		logger.Info("Completed step", "step", steps[i].Name, "duration", steps[i].Duration)
	}

	// Simulate success/failure
	success := true
	applicationID := fmt.Sprintf("app-%d", time.Now().Unix())

	return &ApplicationResult{
		Success:       success,
		ApplicationID: applicationID,
		Steps:         steps,
		Cost:          0.005, // Application execution cost
		Duration:      time.Since(startTime),
		Metadata: map[string]interface{}{
			"job_site_url": input.JobSiteURL,
			"sandbox_id":   input.SandboxID,
		},
	}, nil
}

// CaptureInput contains input for results capture
type CaptureInput struct {
	SandboxID     string `json:"sandbox_id"`
	ApplicationID string `json:"application_id"`
}

// CaptureResult contains capture results
type CaptureResult struct {
	Screenshots []string               `json:"screenshots"`
	Artifacts   map[string]interface{} `json:"artifacts"`
	LogBundle   string                 `json:"log_bundle"`
}

// CaptureResultsActivity captures application results and artifacts
func CaptureResultsActivity(ctx context.Context, input CaptureInput) (*CaptureResult, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("Capturing results", "sandboxId", input.SandboxID)

	activity.RecordHeartbeat(ctx, "capturing_results")

	// Simulate result capture
	screenshots := []string{
		fmt.Sprintf("screenshot-1-%s.png", input.ApplicationID),
		fmt.Sprintf("screenshot-2-%s.png", input.ApplicationID),
	}

	return &CaptureResult{
		Screenshots: screenshots,
		Artifacts: map[string]interface{}{
			"application_id": input.ApplicationID,
			"timestamp":     time.Now(),
		},
		LogBundle: fmt.Sprintf("logs-%s.tar.gz", input.SandboxID),
	}, nil
}

// CleanupInput contains input for cleanup
type CleanupInput struct {
	SandboxID string `json:"sandbox_id"`
	Runtime   string `json:"runtime"`
}

// CleanupResult contains cleanup results
type CleanupResult struct {
	Success bool    `json:"success"`
	Cost    float64 `json:"cost"`
}

// CleanupSandboxActivity cleans up sandbox resources
func CleanupSandboxActivity(ctx context.Context, input CleanupInput) (*CleanupResult, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("Cleaning up sandbox", "sandboxId", input.SandboxID, "runtime", input.Runtime)

	activity.RecordHeartbeat(ctx, "cleaning_up")

	// Simulate cleanup
	time.Sleep(time.Second)

	return &CleanupResult{
		Success: true,
		Cost:    0.0001, // Cleanup cost
	}, nil
}
