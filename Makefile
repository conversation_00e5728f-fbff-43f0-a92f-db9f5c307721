# CVLeap Sandbox Terminal - Build and Test Automation

.PHONY: help build test clean docker-build docker-push helm-install helm-uninstall

# Configuration
DOCKER_REGISTRY ?= cvleap
IMAGE_NAME ?= sandbox
IMAGE_TAG ?= latest
NAMESPACE ?= cvleap
HELM_RELEASE ?= cvleap-sandbox

# Go configuration
GO_VERSION ?= 1.21
GOOS ?= linux
GOARCH ?= amd64

help: ## Show this help message
	@echo "CVLeap Sandbox Terminal - Available commands:"
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

##@ Development

build: ## Build terminald binary
	@echo "Building terminald..."
	cd services/terminald && \
	CGO_ENABLED=0 GOOS=$(GOOS) GOARCH=$(GOARCH) \
	go build -a -installsuffix cgo -ldflags '-w -s' -o terminald ./cmd/terminald
	@echo "✅ terminald built successfully"

test: ## Run all tests
	@echo "Running Go tests..."
	cd services/terminald && go test -v -race -coverprofile=coverage.out ./...
	@echo "Running Python tests..."
	cd apps && python -m pytest tests/ -v
	@echo "✅ All tests passed"

test-unit: ## Run unit tests only
	@echo "Running unit tests..."
	cd services/terminald && go test -v -short ./...

test-integration: ## Run integration tests
	@echo "Running integration tests..."
	cd services/terminald && go test -v -run Integration ./...

test-e2e: ## Run end-to-end tests
	@echo "Running E2E tests..."
	npx playwright test tests/e2e/

lint: ## Run linters
	@echo "Running Go linter..."
	cd services/terminald && golangci-lint run
	@echo "Running Python linter..."
	cd apps && flake8 . && black --check .
	@echo "Running TypeScript linter..."
	cd apps/web && npm run lint

fmt: ## Format code
	@echo "Formatting Go code..."
	cd services/terminald && go fmt ./...
	@echo "Formatting Python code..."
	cd apps && black .
	@echo "Formatting TypeScript code..."
	cd apps/web && npm run format

clean: ## Clean build artifacts
	@echo "Cleaning build artifacts..."
	rm -f services/terminald/terminald
	rm -f services/terminald/coverage.out
	cd services/terminald && go clean -cache -testcache
	@echo "✅ Clean completed"

##@ Docker

docker-build: build ## Build Docker image
	@echo "Building Docker image..."
	docker build -f infra/docker/Dockerfile -t $(DOCKER_REGISTRY)/$(IMAGE_NAME):$(IMAGE_TAG) .
	@echo "✅ Docker image built: $(DOCKER_REGISTRY)/$(IMAGE_NAME):$(IMAGE_TAG)"

docker-push: docker-build ## Push Docker image to registry
	@echo "Pushing Docker image..."
	docker push $(DOCKER_REGISTRY)/$(IMAGE_NAME):$(IMAGE_TAG)
	@echo "✅ Docker image pushed"

docker-run: docker-build ## Run Docker container locally
	@echo "Running Docker container..."
	docker run --rm -p 7000:7000 -p 8000:8000 \
		-e ENABLE_TERMINAL=true \
		-e JWT_SECRET=test-secret \
		$(DOCKER_REGISTRY)/$(IMAGE_NAME):$(IMAGE_TAG)

##@ Kubernetes

helm-install: ## Install Helm chart
	@echo "Installing Helm chart..."
	helm upgrade --install $(HELM_RELEASE) infra/helm/ \
		--namespace $(NAMESPACE) \
		--create-namespace \
		--set enableTerminal=true \
		--set image.repository=$(DOCKER_REGISTRY)/$(IMAGE_NAME) \
		--set image.tag=$(IMAGE_TAG)
	@echo "✅ Helm chart installed"

helm-uninstall: ## Uninstall Helm chart
	@echo "Uninstalling Helm chart..."
	helm uninstall $(HELM_RELEASE) --namespace $(NAMESPACE)
	@echo "✅ Helm chart uninstalled"

helm-template: ## Generate Kubernetes manifests
	@echo "Generating Kubernetes manifests..."
	helm template $(HELM_RELEASE) infra/helm/ \
		--set enableTerminal=true \
		--set image.repository=$(DOCKER_REGISTRY)/$(IMAGE_NAME) \
		--set image.tag=$(IMAGE_TAG) \
		> manifests.yaml
	@echo "✅ Manifests generated: manifests.yaml"

##@ CI/CD

ci-test: ## Run CI test suite
	@echo "Running CI test suite..."
	make lint
	make test-unit
	make test-integration
	@echo "✅ CI tests completed"

ci-build: ## Build for CI/CD
	@echo "Building for CI/CD..."
	make build
	make docker-build
	@echo "✅ CI build completed"

cost-check: ## Check resource usage and cost estimates
	@echo "Checking resource usage..."
	@echo "Terminald binary size:"
	@ls -lh services/terminald/terminald 2>/dev/null || echo "Binary not built"
	@echo "Docker image size:"
	@docker images $(DOCKER_REGISTRY)/$(IMAGE_NAME):$(IMAGE_TAG) --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" 2>/dev/null || echo "Image not built"
	@echo "Estimated cost per application: ≤ $0.005 (target)"

##@ Development Environment

dev-setup: ## Set up development environment
	@echo "Setting up development environment..."
	@echo "Installing Go dependencies..."
	cd services/terminald && go mod download
	@echo "Installing Python dependencies..."
	cd apps && pip install -r requirements.txt
	@echo "Installing Node.js dependencies..."
	cd apps/web && npm install
	@echo "✅ Development environment ready"

dev-run: ## Run development environment
	@echo "Starting development environment..."
	docker-compose -f docker-compose.dev.yml up --build

dev-stop: ## Stop development environment
	@echo "Stopping development environment..."
	docker-compose -f docker-compose.dev.yml down

##@ Security

security-scan: ## Run security scans
	@echo "Running security scans..."
	@echo "Scanning Go dependencies..."
	cd services/terminald && go list -json -deps ./... | nancy sleuth
	@echo "Scanning Docker image..."
	docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
		aquasec/trivy image $(DOCKER_REGISTRY)/$(IMAGE_NAME):$(IMAGE_TAG)
	@echo "✅ Security scan completed"

##@ Monitoring

logs: ## View application logs
	kubectl logs -f deployment/$(HELM_RELEASE) -n $(NAMESPACE) -c terminald

metrics: ## View metrics
	kubectl port-forward svc/$(HELM_RELEASE) 8000:8000 -n $(NAMESPACE) &
	@echo "Metrics available at: http://localhost:8000/metrics"

##@ Utilities

generate-jwt: ## Generate test JWT token
	@echo "Generating test JWT token..."
	@cd services/terminald && go run -ldflags "-X main.jwtSecret=test-secret" \
		./cmd/generate-jwt --user-id=test-user --session-id=test-session --role=user

benchmark: ## Run performance benchmarks
	@echo "Running benchmarks..."
	cd services/terminald && go test -bench=. -benchmem ./...

profile: ## Run with profiling enabled
	@echo "Starting with profiling..."
	cd services/terminald && go run -tags=profile ./cmd/terminald

version: ## Show version information
	@echo "CVLeap Sandbox Terminal"
	@echo "Go version: $(shell go version)"
	@echo "Docker version: $(shell docker --version)"
	@echo "Helm version: $(shell helm version --short)"
	@echo "Kubectl version: $(shell kubectl version --client --short)"
