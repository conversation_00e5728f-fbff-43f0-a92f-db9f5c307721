#!/usr/bin/env python3
"""
FastAPI sidecar for CVLeap sandbox with terminal support.
Provides status endpoints and job application progress tracking.
"""

import asyncio
import json
import os
import time
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import uvicorn


class JobStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"


class ApplicationStep(BaseModel):
    name: str
    status: JobStatus
    progress: float  # 0.0 to 1.0
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error: Optional[str] = None


class JobProgress(BaseModel):
    session_id: str
    job_id: str
    company: str
    position: str
    status: JobStatus
    overall_progress: float
    steps: List[ApplicationStep]
    started_at: datetime
    updated_at: datetime
    estimated_completion: Optional[datetime] = None


class SignalRequest(BaseModel):
    action: str  # "pause", "resume", "stop"
    reason: Optional[str] = None


# Global state
app = FastAPI(
    title="CVLeap Sandbox Sidecar",
    description="Status and control endpoints for sandbox job applications",
    version="1.0.0"
)

# In-memory storage for demo (use Redis in production)
job_progress: Dict[str, JobProgress] = {}
application_steps = [
    "initialize_browser",
    "navigate_to_job_site", 
    "login_or_authenticate",
    "search_and_find_job",
    "fill_application_form",
    "upload_resume",
    "submit_application",
    "capture_confirmation"
]


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "timestamp": datetime.utcnow()}


@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint."""
    # Check if terminald is available when terminal is enabled
    terminal_enabled = os.getenv("ENABLE_TERMINAL", "false").lower() == "true"
    
    if terminal_enabled:
        try:
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:7000/health", timeout=2.0)
                terminal_ready = response.status_code == 200
        except Exception:
            terminal_ready = False
    else:
        terminal_ready = True
    
    return {
        "status": "ready" if terminal_ready else "not_ready",
        "terminal_enabled": terminal_enabled,
        "terminal_ready": terminal_ready,
        "timestamp": datetime.utcnow()
    }


@app.get("/rpc/status/{session_id}")
async def get_job_status(session_id: str):
    """Get current job application status."""
    if session_id not in job_progress:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return job_progress[session_id]


@app.post("/rpc/signal/{session_id}")
async def send_signal(session_id: str, signal: SignalRequest, background_tasks: BackgroundTasks):
    """Send control signal to job application process."""
    if session_id not in job_progress:
        raise HTTPException(status_code=404, detail="Session not found")
    
    progress = job_progress[session_id]
    
    if signal.action == "pause":
        if progress.status == JobStatus.RUNNING:
            progress.status = JobStatus.PAUSED
            background_tasks.add_task(pause_application, session_id)
        else:
            raise HTTPException(status_code=400, detail="Can only pause running jobs")
    
    elif signal.action == "resume":
        if progress.status == JobStatus.PAUSED:
            progress.status = JobStatus.RUNNING
            background_tasks.add_task(resume_application, session_id)
        else:
            raise HTTPException(status_code=400, detail="Can only resume paused jobs")
    
    elif signal.action == "stop":
        if progress.status in [JobStatus.RUNNING, JobStatus.PAUSED]:
            progress.status = JobStatus.FAILED
            background_tasks.add_task(stop_application, session_id)
        else:
            raise HTTPException(status_code=400, detail="Can only stop running or paused jobs")
    
    else:
        raise HTTPException(status_code=400, detail="Invalid action")
    
    progress.updated_at = datetime.utcnow()
    return {"status": "signal_sent", "action": signal.action}


@app.get("/rpc/stream/{session_id}")
async def stream_progress(session_id: str):
    """Stream real-time progress updates."""
    async def generate():
        last_update = 0
        while True:
            if session_id in job_progress:
                progress = job_progress[session_id]
                current_update = progress.updated_at.timestamp()
                
                if current_update > last_update:
                    yield f"data: {progress.json()}\n\n"
                    last_update = current_update
                
                # Stop streaming if job is completed or failed
                if progress.status in [JobStatus.COMPLETED, JobStatus.FAILED]:
                    break
            
            await asyncio.sleep(1)
    
    return StreamingResponse(generate(), media_type="text/plain")


@app.post("/rpc/start/{session_id}")
async def start_job_application(
    session_id: str,
    job_id: str,
    company: str,
    position: str,
    background_tasks: BackgroundTasks
):
    """Start a new job application process."""
    if session_id in job_progress:
        raise HTTPException(status_code=400, detail="Session already exists")
    
    # Initialize job progress
    steps = [
        ApplicationStep(name=step, status=JobStatus.PENDING, progress=0.0)
        for step in application_steps
    ]
    
    progress = JobProgress(
        session_id=session_id,
        job_id=job_id,
        company=company,
        position=position,
        status=JobStatus.RUNNING,
        overall_progress=0.0,
        steps=steps,
        started_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    
    job_progress[session_id] = progress
    
    # Start the application process in background
    background_tasks.add_task(run_job_application, session_id)
    
    return {"status": "started", "session_id": session_id}


async def run_job_application(session_id: str):
    """Simulate running a job application with progress updates."""
    progress = job_progress[session_id]
    
    try:
        for i, step in enumerate(progress.steps):
            if progress.status != JobStatus.RUNNING:
                break
            
            # Start step
            step.status = JobStatus.RUNNING
            step.started_at = datetime.utcnow()
            progress.updated_at = datetime.utcnow()
            
            # Simulate step execution with progress updates
            for p in range(0, 101, 10):
                if progress.status != JobStatus.RUNNING:
                    break
                
                step.progress = p / 100.0
                progress.overall_progress = (i + step.progress) / len(progress.steps)
                progress.updated_at = datetime.utcnow()
                
                await asyncio.sleep(0.5)  # Simulate work
            
            if progress.status == JobStatus.RUNNING:
                step.status = JobStatus.COMPLETED
                step.completed_at = datetime.utcnow()
                step.progress = 1.0
        
        if progress.status == JobStatus.RUNNING:
            progress.status = JobStatus.COMPLETED
            progress.overall_progress = 1.0
            progress.updated_at = datetime.utcnow()
    
    except Exception as e:
        progress.status = JobStatus.FAILED
        progress.updated_at = datetime.utcnow()
        # Find current step and mark as failed
        for step in progress.steps:
            if step.status == JobStatus.RUNNING:
                step.status = JobStatus.FAILED
                step.error = str(e)
                break


async def pause_application(session_id: str):
    """Handle application pause logic."""
    # In a real implementation, this would pause the browser automation
    print(f"Pausing application for session {session_id}")


async def resume_application(session_id: str):
    """Handle application resume logic."""
    # In a real implementation, this would resume the browser automation
    print(f"Resuming application for session {session_id}")


async def stop_application(session_id: str):
    """Handle application stop logic."""
    # In a real implementation, this would stop the browser automation
    print(f"Stopping application for session {session_id}")


if __name__ == "__main__":
    port = int(os.getenv("PORT", "8000"))
    uvicorn.run(app, host="0.0.0.0", port=port)
