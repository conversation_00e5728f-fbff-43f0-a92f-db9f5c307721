import { useCallback, useEffect, useRef, useState } from 'react';

// Lazy import types to avoid bundling xterm.js until needed
type Terminal = import('xterm').Terminal;
type FitAddon = import('xterm-addon-fit').FitAddon;

interface TerminalConnection {
  websocket: WebSocket | null;
  terminal: Terminal | null;
  fitAddon: FitAddon | null;
  isConnected: boolean;
  error: string | null;
}

interface SandboxTerminalOptions {
  sessionId: string;
  userId: string;
  userRole?: string;
  autoConnect?: boolean;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: string) => void;
}

interface SandboxTerminalState {
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
  clientCount: number;
}

interface SandboxTerminalActions {
  connect: () => Promise<void>;
  disconnect: () => void;
  clear: () => void;
  fit: () => void;
  capturePane: () => string | null;
  sendInput: (data: string) => void;
}

export function useSandboxTerminal(
  options: SandboxTerminalOptions
): [SandboxTerminalState, SandboxTerminalActions, React.RefObject<HTMLDivElement>] {
  const terminalRef = useRef<HTMLDivElement>(null);
  const connectionRef = useRef<TerminalConnection>({
    websocket: null,
    terminal: null,
    fitAddon: null,
    isConnected: false,
    error: null,
  });

  const [state, setState] = useState<SandboxTerminalState>({
    isLoading: false,
    isConnected: false,
    error: null,
    clientCount: 0,
  });

  // Lazy load xterm.js and addons
  const loadXterm = useCallback(async () => {
    try {
      const [
        { Terminal },
        { FitAddon },
        { WebLinksAddon },
        { SearchAddon }
      ] = await Promise.all([
        import('xterm'),
        import('xterm-addon-fit'),
        import('xterm-addon-web-links'),
        import('xterm-addon-search')
      ]);

      // Load CSS if not already loaded
      if (!document.querySelector('link[href*="xterm.css"]')) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'https://cdn.jsdelivr.net/npm/xterm@5.3.0/css/xterm.css';
        document.head.appendChild(link);
      }

      return { Terminal, FitAddon, WebLinksAddon, SearchAddon };
    } catch (error) {
      throw new Error(`Failed to load xterm.js: ${error}`);
    }
  }, []);

  // Get WebSocket connection details from API
  const getConnectionDetails = useCallback(async () => {
    const response = await fetch(`/api/sandbox/${options.sessionId}/watch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        session_id: options.sessionId,
        user_id: options.userId,
        user_role: options.userRole || 'user',
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to get terminal access: ${error}`);
    }

    return response.json();
  }, [options.sessionId, options.userId, options.userRole]);

  // Connect to terminal
  const connect = useCallback(async () => {
    if (connectionRef.current.isConnected || state.isLoading) {
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Load xterm.js
      const { Terminal, FitAddon, WebLinksAddon, SearchAddon } = await loadXterm();

      // Get connection details
      const connectionDetails = await getConnectionDetails();

      // Create terminal instance
      const terminal = new Terminal({
        cursorBlink: true,
        fontSize: 14,
        fontFamily: 'Menlo, Monaco, "Courier New", monospace',
        theme: {
          background: '#1e1e1e',
          foreground: '#d4d4d4',
          cursor: '#ffffff',
          selection: '#264f78',
        },
        scrollback: 10000,
      });

      // Create fit addon
      const fitAddon = new FitAddon();
      terminal.loadAddon(fitAddon);
      terminal.loadAddon(new WebLinksAddon());
      terminal.loadAddon(new SearchAddon());

      // Open terminal in DOM
      if (terminalRef.current) {
        terminal.open(terminalRef.current);
        fitAddon.fit();
      }

      // Create WebSocket connection
      const wsUrl = connectionDetails.websocket_url.replace('http', 'ws');
      const websocket = new WebSocket(wsUrl, [`token-${connectionDetails.token}`]);

      websocket.binaryType = 'arraybuffer';

      websocket.onopen = () => {
        connectionRef.current.isConnected = true;
        setState(prev => ({ 
          ...prev, 
          isConnected: true, 
          isLoading: false,
          clientCount: prev.clientCount + 1
        }));
        options.onConnect?.();
      };

      websocket.onmessage = (event) => {
        if (event.data instanceof ArrayBuffer) {
          const data = new Uint8Array(event.data);
          terminal.write(data);
        } else {
          terminal.write(event.data);
        }
      };

      websocket.onclose = (event) => {
        connectionRef.current.isConnected = false;
        setState(prev => ({ 
          ...prev, 
          isConnected: false,
          clientCount: Math.max(0, prev.clientCount - 1)
        }));
        
        if (event.code !== 1000) {
          const error = `Connection closed unexpectedly: ${event.reason || 'Unknown reason'}`;
          setState(prev => ({ ...prev, error }));
          options.onError?.(error);
        }
        
        options.onDisconnect?.();
      };

      websocket.onerror = (error) => {
        const errorMsg = 'WebSocket connection error';
        setState(prev => ({ ...prev, error: errorMsg, isLoading: false }));
        options.onError?.(errorMsg);
      };

      // Handle terminal input (only for advanced users)
      if (options.userRole === 'Advanced ✚' || options.userRole === 'admin') {
        terminal.onData((data) => {
          if (websocket.readyState === WebSocket.OPEN) {
            websocket.send(data);
          }
        });
      }

      // Store connection references
      connectionRef.current = {
        websocket,
        terminal,
        fitAddon,
        isConnected: true,
        error: null,
      };

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({ ...prev, error: errorMsg, isLoading: false }));
      options.onError?.(errorMsg);
    }
  }, [loadXterm, getConnectionDetails, options, state.isLoading]);

  // Disconnect from terminal
  const disconnect = useCallback(() => {
    const connection = connectionRef.current;
    
    if (connection.websocket) {
      connection.websocket.close(1000, 'User disconnected');
    }
    
    if (connection.terminal) {
      connection.terminal.dispose();
    }

    connectionRef.current = {
      websocket: null,
      terminal: null,
      fitAddon: null,
      isConnected: false,
      error: null,
    };

    setState(prev => ({ 
      ...prev, 
      isConnected: false, 
      isLoading: false,
      clientCount: 0
    }));
  }, []);

  // Clear terminal
  const clear = useCallback(() => {
    connectionRef.current.terminal?.clear();
  }, []);

  // Fit terminal to container
  const fit = useCallback(() => {
    connectionRef.current.fitAddon?.fit();
  }, []);

  // Capture terminal pane content
  const capturePane = useCallback((): string | null => {
    const terminal = connectionRef.current.terminal;
    if (!terminal) return null;

    const buffer = terminal.buffer.active;
    let content = '';
    
    for (let i = 0; i < buffer.length; i++) {
      const line = buffer.getLine(i);
      if (line) {
        content += line.translateToString(true) + '\n';
      }
    }
    
    return content;
  }, []);

  // Send input to terminal
  const sendInput = useCallback((data: string) => {
    const websocket = connectionRef.current.websocket;
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      websocket.send(data);
    }
  }, []);

  // Handle hotkeys
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // ⌘K or Ctrl+K to capture pane
      if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
        event.preventDefault();
        const content = capturePane();
        if (content) {
          navigator.clipboard.writeText(content).catch(console.error);
        }
      }
    };

    if (state.isConnected) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [state.isConnected, capturePane]);

  // Auto-connect if requested
  useEffect(() => {
    if (options.autoConnect && !state.isConnected && !state.isLoading) {
      connect();
    }
  }, [options.autoConnect, state.isConnected, state.isLoading, connect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (connectionRef.current.fitAddon) {
        setTimeout(() => fit(), 100);
      }
    };

    if (state.isConnected) {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, [state.isConnected, fit]);

  const actions: SandboxTerminalActions = {
    connect,
    disconnect,
    clear,
    fit,
    capturePane,
    sendInput,
  };

  return [state, actions, terminalRef];
}
