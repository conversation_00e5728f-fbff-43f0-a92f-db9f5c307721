import React, { useState } from 'react';
import { useSandboxTerminal } from '../hooks/useSandboxTerminal';

interface SandboxTerminalProps {
  sessionId: string;
  userId: string;
  userRole?: string;
  className?: string;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: string) => void;
}

export const SandboxTerminal: React.FC<SandboxTerminalProps> = ({
  sessionId,
  userId,
  userRole = 'user',
  className = '',
  onConnect,
  onDisconnect,
  onError,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [state, actions, terminalRef] = useSandboxTerminal({
    sessionId,
    userId,
    userRole,
    autoConnect: false,
    onConnect: () => {
      onConnect?.();
    },
    onDisconnect: () => {
      onDisconnect?.();
    },
    onError: (error) => {
      onError?.(error);
    },
  });

  const handleToggleTerminal = async () => {
    if (!isVisible) {
      setIsVisible(true);
      if (!state.isConnected && !state.isLoading) {
        await actions.connect();
      }
    } else {
      setIsVisible(false);
      actions.disconnect();
    }
  };

  const handleClear = () => {
    actions.clear();
  };

  const handleCapture = () => {
    const content = actions.capturePane();
    if (content) {
      navigator.clipboard.writeText(content).then(() => {
        // Could show a toast notification here
        console.log('Terminal content copied to clipboard');
      }).catch(console.error);
    }
  };

  const canSendInput = userRole === 'Advanced ✚' || userRole === 'admin';

  return (
    <div className={`sandbox-terminal ${className}`}>
      {/* Terminal Toggle Button */}
      <div className="terminal-controls mb-4">
        <button
          onClick={handleToggleTerminal}
          disabled={state.isLoading}
          className={`
            px-4 py-2 rounded-lg font-medium transition-all duration-200
            ${isVisible 
              ? 'bg-red-600 hover:bg-red-700 text-white' 
              : 'bg-blue-600 hover:bg-blue-700 text-white'
            }
            ${state.isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-lg'}
          `}
        >
          {state.isLoading ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Connecting...
            </span>
          ) : isVisible ? (
            '🔴 Hide Terminal'
          ) : (
            '📺 Watch Terminal'
          )}
        </button>

        {/* Connection Status */}
        {isVisible && (
          <div className="inline-flex items-center ml-4 space-x-2">
            <div className={`w-2 h-2 rounded-full ${state.isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm text-gray-600">
              {state.isConnected ? 'Connected' : 'Disconnected'}
            </span>
            {state.clientCount > 0 && (
              <span className="text-xs text-gray-500">
                ({state.clientCount} viewer{state.clientCount !== 1 ? 's' : ''})
              </span>
            )}
          </div>
        )}
      </div>

      {/* Error Display */}
      {state.error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <strong>Terminal Error:</strong> {state.error}
        </div>
      )}

      {/* Terminal Container */}
      {isVisible && (
        <div className="terminal-container bg-gray-900 rounded-lg overflow-hidden shadow-lg">
          {/* Terminal Header */}
          <div className="terminal-header bg-gray-800 px-4 py-2 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <span className="text-gray-300 text-sm font-mono">
                Session: {sessionId}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              {!canSendInput && (
                <span className="text-xs text-yellow-400 bg-yellow-900 px-2 py-1 rounded">
                  Read-only
                </span>
              )}
              
              <button
                onClick={handleClear}
                className="text-gray-400 hover:text-white text-sm px-2 py-1 rounded hover:bg-gray-700"
                title="Clear terminal"
              >
                Clear
              </button>
              
              <button
                onClick={handleCapture}
                className="text-gray-400 hover:text-white text-sm px-2 py-1 rounded hover:bg-gray-700"
                title="Copy terminal content (⌘K)"
              >
                📋 Copy
              </button>
            </div>
          </div>

          {/* Terminal Content */}
          <div 
            ref={terminalRef}
            className="terminal-content"
            style={{ 
              height: '400px',
              padding: '8px',
              backgroundColor: '#1e1e1e'
            }}
          />

          {/* Terminal Footer */}
          <div className="terminal-footer bg-gray-800 px-4 py-2 text-xs text-gray-400">
            <div className="flex justify-between items-center">
              <span>
                {canSendInput 
                  ? 'Interactive mode - you can type commands' 
                  : 'View-only mode - upgrade to Advanced ✚ for interactive access'
                }
              </span>
              <span>Press ⌘K to copy terminal content</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SandboxTerminal;
