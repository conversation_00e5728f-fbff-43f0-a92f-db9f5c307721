/**
 * Test Setup Configuration
 * Jest and testing library configuration for comprehensive test suite
 */

import '@testing-library/jest-dom'
import { configure } from '@testing-library/react'
import { server } from './mocks/server'

// Configure testing library
configure({
  testIdAttribute: 'data-testid',
})

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock window.getComputedStyle
Object.defineProperty(window, 'getComputedStyle', {
  value: () => ({
    getPropertyValue: () => '',
  }),
})

// Mock clipboard API
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: jest.fn().mockResolvedValue(undefined),
    readText: jest.fn().mockResolvedValue(''),
  },
})

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn().mockReturnValue('mock-url')
global.URL.revokeObjectURL = jest.fn()

// Mock File and FileReader
global.File = class MockFile {
  constructor(parts: any[], filename: string, properties?: any) {
    return {
      name: filename,
      size: parts.reduce((acc, part) => acc + part.length, 0),
      type: properties?.type || 'text/plain',
      lastModified: Date.now(),
    } as File
  }
}

global.FileReader = class MockFileReader {
  result: string | ArrayBuffer | null = null
  error: any = null
  readyState: number = 0
  onload: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null
  onerror: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null

  readAsText(file: Blob) {
    setTimeout(() => {
      this.result = 'mock file content'
      this.readyState = 2
      if (this.onload) {
        this.onload({} as ProgressEvent<FileReader>)
      }
    }, 0)
  }

  readAsDataURL(file: Blob) {
    setTimeout(() => {
      this.result = 'data:text/plain;base64,bW9jayBmaWxlIGNvbnRlbnQ='
      this.readyState = 2
      if (this.onload) {
        this.onload({} as ProgressEvent<FileReader>)
      }
    }, 0)
  }
}

// Mock crypto.randomUUID
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: () => 'mock-uuid-' + Math.random().toString(36).substr(2, 9),
    getRandomValues: (arr: any) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256)
      }
      return arr
    },
  },
})

// Mock performance API
Object.defineProperty(global, 'performance', {
  value: {
    now: jest.fn(() => Date.now()),
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByType: jest.fn(() => []),
    getEntriesByName: jest.fn(() => []),
  },
})

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn(),
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
})

// Mock console methods for cleaner test output
const originalError = console.error
const originalWarn = console.warn

beforeAll(() => {
  // Start MSW server
  server.listen({ onUnhandledRequest: 'error' })
  
  // Suppress console errors/warnings in tests unless they're expected
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning:') || args[0].includes('Error:'))
    ) {
      return
    }
    originalError.call(console, ...args)
  }
  
  console.warn = (...args: any[]) => {
    if (typeof args[0] === 'string' && args[0].includes('Warning:')) {
      return
    }
    originalWarn.call(console, ...args)
  }
})

afterEach(() => {
  // Reset MSW handlers
  server.resetHandlers()
  
  // Clear all mocks
  jest.clearAllMocks()
  
  // Clear localStorage and sessionStorage
  localStorageMock.clear()
  sessionStorageMock.clear()
})

afterAll(() => {
  // Stop MSW server
  server.close()
  
  // Restore console methods
  console.error = originalError
  console.warn = originalWarn
})

// Global test utilities
export const createMockResumeDocument = () => ({
  id: 'test-resume-id',
  title: 'Test Resume',
  content: [
    {
      id: 'header-section',
      type: 'header',
      children: [
        { type: 'text', text: 'John Doe' },
        { type: 'text', text: 'Software Engineer' },
      ],
    },
    {
      id: 'experience-section',
      type: 'experience',
      children: [
        {
          type: 'experience-item',
          children: [
            { type: 'text', text: 'Senior Developer at Tech Corp' },
            { type: 'text', text: '2020 - Present' },
          ],
        },
      ],
    },
  ],
  metadata: {
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    version: 1,
    templateId: 'modern-professional',
  },
})

export const createMockUser = () => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  name: 'Test User',
  preferences: {
    theme: 'light',
    language: 'en',
  },
})

export const createMockTemplate = () => ({
  id: 'test-template',
  name: 'Test Template',
  description: 'A test template',
  version: '1.0.0',
  author: { name: 'Test Author', email: '<EMAIL>' },
  category: 'modern',
  tags: ['test', 'modern'],
  layout: {
    type: 'single-column',
    columns: [{ width: '100%', content: ['header', 'experience'] }],
    spacing: { margin: '1rem', padding: '1rem', gap: '1rem' },
    breakpoints: { mobile: '768px', tablet: '1024px', desktop: '1200px' },
  },
  sections: [],
  theme: {
    name: 'Test Theme',
    colors: {
      primary: '#000000',
      secondary: '#666666',
      accent: '#0066cc',
      text: '#333333',
      textLight: '#666666',
      background: '#ffffff',
      surface: '#f9f9f9',
      border: '#e0e0e0',
      success: '#00aa00',
      warning: '#ffaa00',
      error: '#cc0000',
    },
    typography: {
      fontFamily: { primary: 'Arial, sans-serif' },
      fontSize: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
      },
      fontWeight: { normal: '400', medium: '500', semibold: '600', bold: '700' },
      lineHeight: { tight: '1.25', normal: '1.5', relaxed: '1.75' },
    },
    spacing: {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
      '2xl': '3rem',
      '3xl': '4rem',
    },
    borderRadius: { none: '0', sm: '0.125rem', md: '0.375rem', lg: '0.5rem', full: '9999px' },
    shadows: {
      sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
      md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
      lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
      xl: '0 20px 25px -5px rgb(0 0 0 / 0.1)',
    },
  },
  settings: {
    pageSize: 'A4',
    orientation: 'portrait',
    margins: { top: '1in', right: '1in', bottom: '1in', left: '1in' },
    dpi: 300,
    quality: 'standard',
  },
  ats: {
    friendly: true,
    keywords: [],
    structure: { useSemanticHTML: true, includeMetadata: true, optimizeForParsing: true },
  },
  accessibility: { wcagLevel: 'AA', highContrast: false, screenReaderOptimized: true, keyboardNavigation: true },
  i18n: { defaultLanguage: 'en', supportedLanguages: ['en'], rtlSupport: false, dateFormat: 'MM/yyyy', numberFormat: 'en-US' },
  preview: { demoData: {} },
  metadata: {
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    downloads: 0,
    rating: 0,
    featured: false,
    premium: false,
  },
})

// Custom matchers
expect.extend({
  toBeAccessible(received) {
    // Custom accessibility matcher
    const pass = received.getAttribute('aria-label') !== null ||
                 received.getAttribute('aria-labelledby') !== null ||
                 received.tagName.toLowerCase() === 'button' ||
                 received.tagName.toLowerCase() === 'a'
    
    return {
      message: () => `expected element to be accessible`,
      pass,
    }
  },
  
  toHaveValidHTML(received) {
    // Custom HTML validation matcher
    const pass = received.innerHTML.length > 0 &&
                 !received.innerHTML.includes('<script>') &&
                 !received.innerHTML.includes('javascript:')
    
    return {
      message: () => `expected element to have valid HTML`,
      pass,
    }
  },
})

// Declare custom matchers for TypeScript
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeAccessible(): R
      toHaveValidHTML(): R
    }
  }
}
