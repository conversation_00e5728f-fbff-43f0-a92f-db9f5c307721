import { test, expect, Page } from '@playwright/test'

test.describe('Resume Editor E2E Tests', () => {
  let page: Page

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage
    await page.goto('/')
    
    // Wait for the editor to load
    await page.waitForSelector('[data-testid="resume-editor"]', { timeout: 10000 })
  })

  test.describe('Basic Editor Functionality', () => {
    test('should load the editor with default content', async () => {
      // Check that the editor is visible
      await expect(page.locator('[data-testid="resume-editor"]')).toBeVisible()
      
      // Check that the editable area is present
      await expect(page.locator('[contenteditable="true"]')).toBeVisible()
      
      // Check for placeholder text
      await expect(page.locator('text=Start writing your résumé...')).toBeVisible()
    })

    test('should allow typing in the editor', async () => {
      const editor = page.locator('[contenteditable="true"]')
      
      // Click to focus the editor
      await editor.click()
      
      // Type some content
      await editor.type('<PERSON>\nSoftware Engineer')
      
      // Verify the content was added
      await expect(editor).toContainText('John Doe')
      await expect(editor).toContainText('Software Engineer')
    })

    test('should support keyboard shortcuts', async () => {
      const editor = page.locator('[contenteditable="true"]')
      
      await editor.click()
      await editor.type('Bold text')
      
      // Select all text
      await page.keyboard.press('Control+a')
      
      // Apply bold formatting
      await page.keyboard.press('Control+b')
      
      // Verify bold formatting is applied (this would depend on implementation)
      await expect(editor.locator('strong')).toContainText('Bold text')
    })

    test('should support undo/redo functionality', async () => {
      const editor = page.locator('[contenteditable="true"]')
      
      await editor.click()
      await editor.type('First line')
      await page.keyboard.press('Enter')
      await editor.type('Second line')
      
      // Undo last action
      await page.keyboard.press('Control+z')
      
      // Should not contain "Second line"
      await expect(editor).not.toContainText('Second line')
      await expect(editor).toContainText('First line')
      
      // Redo
      await page.keyboard.press('Control+Shift+z')
      
      // Should contain both lines again
      await expect(editor).toContainText('First line')
      await expect(editor).toContainText('Second line')
    })
  })

  test.describe('Template System', () => {
    test('should load and apply templates', async () => {
      // Open template selector
      await page.click('[data-testid="template-selector"]')
      
      // Wait for templates to load
      await page.waitForSelector('[data-testid="template-grid"]')
      
      // Select a template
      await page.click('[data-testid="template-modern-professional"]')
      
      // Verify template is applied
      await expect(page.locator('.resume-container')).toHaveClass(/modern-professional/)
    })

    test('should preview templates before applying', async () => {
      await page.click('[data-testid="template-selector"]')
      
      // Hover over a template to see preview
      await page.hover('[data-testid="template-classic-traditional"]')
      
      // Verify preview modal appears
      await expect(page.locator('[data-testid="template-preview-modal"]')).toBeVisible()
      
      // Close preview
      await page.keyboard.press('Escape')
      await expect(page.locator('[data-testid="template-preview-modal"]')).not.toBeVisible()
    })
  })

  test.describe('Drag and Drop', () => {
    test('should allow reordering sections via drag and drop', async () => {
      // Add some sections first
      await page.click('[data-testid="add-section-button"]')
      await page.click('[data-testid="add-experience-section"]')
      
      await page.click('[data-testid="add-section-button"]')
      await page.click('[data-testid="add-education-section"]')
      
      // Get initial order
      const sections = page.locator('[data-testid^="section-"]')
      const firstSection = sections.nth(0)
      const secondSection = sections.nth(1)
      
      // Perform drag and drop
      await firstSection.dragTo(secondSection)
      
      // Verify order has changed
      await expect(sections.nth(0)).not.toHaveAttribute('data-testid', await firstSection.getAttribute('data-testid'))
    })

    test('should show drag indicators during drag operations', async () => {
      await page.click('[data-testid="add-section-button"]')
      await page.click('[data-testid="add-experience-section"]')
      
      const section = page.locator('[data-testid^="section-"]').first()
      const dragHandle = section.locator('[data-testid="drag-handle"]')
      
      // Start dragging
      await dragHandle.hover()
      await expect(dragHandle).toBeVisible()
      
      // Verify drag handle appears on hover
      await expect(section).toHaveClass(/draggable-hovered/)
    })
  })

  test.describe('Real-time Collaboration', () => {
    test('should show connection status', async () => {
      // Check for connection indicator
      await expect(page.locator('[data-testid="connection-status"]')).toBeVisible()
      await expect(page.locator('text=Connected')).toBeVisible()
    })

    test('should display collaborator avatars', async () => {
      // This would require setting up a mock collaboration session
      // For now, we'll test the UI elements
      await expect(page.locator('[data-testid="collaborators-list"]')).toBeVisible()
    })
  })

  test.describe('Export Functionality', () => {
    test('should export resume as PDF', async () => {
      // Add some content first
      const editor = page.locator('[contenteditable="true"]')
      await editor.click()
      await editor.type('John Doe\nSoftware Engineer\nExperienced developer with 5+ years in web development.')
      
      // Open export menu
      await page.click('[data-testid="export-button"]')
      
      // Select PDF export
      await page.click('[data-testid="export-pdf"]')
      
      // Wait for export to complete
      await page.waitForSelector('[data-testid="export-success"]', { timeout: 30000 })
      
      // Verify success message
      await expect(page.locator('text=PDF exported successfully')).toBeVisible()
    })

    test('should export with different quality settings', async () => {
      await page.click('[data-testid="export-button"]')
      await page.click('[data-testid="export-settings"]')
      
      // Change quality setting
      await page.selectOption('[data-testid="quality-select"]', 'high')
      
      // Export
      await page.click('[data-testid="export-pdf"]')
      
      await page.waitForSelector('[data-testid="export-success"]', { timeout: 30000 })
      await expect(page.locator('text=PDF exported successfully')).toBeVisible()
    })
  })

  test.describe('Magic Layout AI', () => {
    test('should provide layout suggestions', async () => {
      // Add some content to trigger suggestions
      const editor = page.locator('[contenteditable="true"]')
      await editor.click()
      await editor.type('John Doe\nSoftware Engineer\n\nExperience:\nSenior Developer at TechCorp\n\nEducation:\nBS Computer Science')
      
      // Open AI suggestions
      await page.click('[data-testid="magic-layout-button"]')
      
      // Wait for suggestions to load
      await page.waitForSelector('[data-testid="layout-suggestions"]')
      
      // Verify suggestions are displayed
      await expect(page.locator('[data-testid="suggestion-item"]')).toHaveCount.greaterThan(0)
    })

    test('should apply layout suggestions', async () => {
      await page.click('[data-testid="magic-layout-button"]')
      await page.waitForSelector('[data-testid="layout-suggestions"]')
      
      // Apply first suggestion
      await page.click('[data-testid="suggestion-item"]').first()
      await page.click('[data-testid="apply-suggestion"]')
      
      // Verify suggestion was applied
      await expect(page.locator('[data-testid="suggestion-applied"]')).toBeVisible()
    })
  })

  test.describe('Accessibility', () => {
    test('should be keyboard navigable', async () => {
      // Tab through the interface
      await page.keyboard.press('Tab')
      await expect(page.locator(':focus')).toBeVisible()
      
      // Continue tabbing
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab')
      
      // Should be able to reach the editor
      await page.keyboard.press('Tab')
      await expect(page.locator('[contenteditable="true"]:focus')).toBeVisible()
    })

    test('should have proper ARIA labels', async () => {
      // Check for ARIA labels on key elements
      await expect(page.locator('[data-testid="resume-editor"]')).toHaveAttribute('role', 'application')
      await expect(page.locator('[contenteditable="true"]')).toHaveAttribute('aria-label')
    })

    test('should support screen readers', async () => {
      // Check for screen reader announcements
      await expect(page.locator('[aria-live="polite"]')).toBeVisible()
    })
  })

  test.describe('Performance', () => {
    test('should load within performance budget', async () => {
      // Measure page load time
      const startTime = Date.now()
      await page.goto('/')
      await page.waitForSelector('[data-testid="resume-editor"]')
      const loadTime = Date.now() - startTime
      
      // Should load within 2 seconds (2000ms)
      expect(loadTime).toBeLessThan(2000)
    })

    test('should handle large documents efficiently', async () => {
      const editor = page.locator('[contenteditable="true"]')
      await editor.click()
      
      // Add a large amount of content
      const largeContent = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. '.repeat(1000)
      await editor.type(largeContent)
      
      // Verify editor remains responsive
      await page.keyboard.press('Control+a')
      await page.keyboard.press('Control+b')
      
      // Should complete within reasonable time
      await expect(editor.locator('strong')).toBeVisible({ timeout: 5000 })
    })
  })

  test.describe('Mobile Responsiveness', () => {
    test('should work on mobile devices', async () => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 })
      
      // Verify mobile layout
      await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
      await expect(page.locator('[data-testid="resume-editor"]')).toBeVisible()
    })

    test('should support touch interactions', async () => {
      await page.setViewportSize({ width: 375, height: 667 })
      
      const editor = page.locator('[contenteditable="true"]')
      
      // Tap to focus
      await editor.tap()
      
      // Verify virtual keyboard doesn't break layout
      await expect(editor).toBeFocused()
    })
  })

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async () => {
      // Simulate network failure
      await page.route('**/api/**', route => route.abort())
      
      // Try to save document
      await page.keyboard.press('Control+s')
      
      // Should show error message
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible()
      await expect(page.locator('text=Network error')).toBeVisible()
    })

    test('should recover from errors', async () => {
      // Simulate temporary error
      let requestCount = 0
      await page.route('**/api/documents/**', route => {
        requestCount++
        if (requestCount === 1) {
          route.abort()
        } else {
          route.continue()
        }
      })
      
      // Try to save
      await page.keyboard.press('Control+s')
      
      // Should show error first
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible()
      
      // Retry should succeed
      await page.click('[data-testid="retry-button"]')
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
    })
  })
})
