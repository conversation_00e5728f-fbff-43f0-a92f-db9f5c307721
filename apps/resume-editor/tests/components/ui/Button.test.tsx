/**
 * Button Component Tests
 * Comprehensive test suite for Button component with accessibility and interaction testing
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Button } from '@/components/ui/Button/Button'
import { PlusIcon, TrashIcon } from '@heroicons/react/24/outline'

describe('Button Component', () => {
  const user = userEvent.setup()

  describe('Rendering', () => {
    it('renders with default props', () => {
      render(<Button>Click me</Button>)
      
      const button = screen.getByRole('button', { name: /click me/i })
      expect(button).toBeInTheDocument()
      expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center')
    })

    it('renders with custom className', () => {
      render(<Button className="custom-class">Button</Button>)
      
      const button = screen.getByRole('button')
      expect(button).toHaveClass('custom-class')
    })

    it('renders with different variants', () => {
      const variants = ['primary', 'secondary', 'outline', 'ghost', 'destructive'] as const
      
      variants.forEach(variant => {
        const { unmount } = render(<Button variant={variant}>Button</Button>)
        const button = screen.getByRole('button')
        
        // Check that variant-specific classes are applied
        expect(button).toHaveClass(expect.stringMatching(new RegExp(variant)))
        
        unmount()
      })
    })

    it('renders with different sizes', () => {
      const sizes = ['sm', 'md', 'lg', 'xl'] as const
      
      sizes.forEach(size => {
        const { unmount } = render(<Button size={size}>Button</Button>)
        const button = screen.getByRole('button')
        
        // Check that size-specific classes are applied
        expect(button).toHaveClass(expect.stringMatching(/h-\d+/))
        
        unmount()
      })
    })

    it('renders with full width', () => {
      render(<Button fullWidth>Button</Button>)
      
      const button = screen.getByRole('button')
      expect(button).toHaveClass('w-full')
    })
  })

  describe('Icons', () => {
    it('renders with left icon', () => {
      render(
        <Button leftIcon={<PlusIcon data-testid="plus-icon" />}>
          Add Item
        </Button>
      )
      
      expect(screen.getByTestId('plus-icon')).toBeInTheDocument()
      expect(screen.getByText('Add Item')).toBeInTheDocument()
    })

    it('renders with right icon', () => {
      render(
        <Button rightIcon={<TrashIcon data-testid="trash-icon" />}>
          Delete
        </Button>
      )
      
      expect(screen.getByTestId('trash-icon')).toBeInTheDocument()
      expect(screen.getByText('Delete')).toBeInTheDocument()
    })

    it('renders icon-only button with proper accessibility', () => {
      render(
        <Button aria-label="Add new item">
          <PlusIcon data-testid="plus-icon" />
        </Button>
      )
      
      const button = screen.getByRole('button', { name: /add new item/i })
      expect(button).toBeInTheDocument()
      expect(screen.getByTestId('plus-icon')).toBeInTheDocument()
    })
  })

  describe('Loading State', () => {
    it('shows loading spinner when loading', () => {
      render(<Button loading>Loading Button</Button>)
      
      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
      expect(button).toHaveClass('cursor-not-allowed')
      
      // Check for loading spinner (Loader2 icon)
      const spinner = button.querySelector('svg')
      expect(spinner).toHaveClass('animate-spin')
    })

    it('hides icons when loading', () => {
      render(
        <Button 
          loading 
          leftIcon={<PlusIcon data-testid="plus-icon" />}
          rightIcon={<TrashIcon data-testid="trash-icon" />}
        >
          Loading
        </Button>
      )
      
      // Icons should not be visible when loading
      expect(screen.queryByTestId('plus-icon')).not.toBeInTheDocument()
      expect(screen.queryByTestId('trash-icon')).not.toBeInTheDocument()
    })

    it('prevents clicks when loading', async () => {
      const handleClick = jest.fn()
      render(
        <Button loading onClick={handleClick}>
          Loading Button
        </Button>
      )
      
      const button = screen.getByRole('button')
      await user.click(button)
      
      expect(handleClick).not.toHaveBeenCalled()
    })
  })

  describe('Disabled State', () => {
    it('renders as disabled', () => {
      render(<Button disabled>Disabled Button</Button>)
      
      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
      expect(button).toHaveClass('opacity-50')
    })

    it('prevents clicks when disabled', async () => {
      const handleClick = jest.fn()
      render(
        <Button disabled onClick={handleClick}>
          Disabled Button
        </Button>
      )
      
      const button = screen.getByRole('button')
      await user.click(button)
      
      expect(handleClick).not.toHaveBeenCalled()
    })
  })

  describe('Interactions', () => {
    it('handles click events', async () => {
      const handleClick = jest.fn()
      render(<Button onClick={handleClick}>Click me</Button>)
      
      const button = screen.getByRole('button')
      await user.click(button)
      
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('handles keyboard events', async () => {
      const handleClick = jest.fn()
      render(<Button onClick={handleClick}>Press me</Button>)
      
      const button = screen.getByRole('button')
      button.focus()
      
      await user.keyboard('{Enter}')
      expect(handleClick).toHaveBeenCalledTimes(1)
      
      await user.keyboard(' ')
      expect(handleClick).toHaveBeenCalledTimes(2)
    })

    it('handles focus and blur events', async () => {
      const handleFocus = jest.fn()
      const handleBlur = jest.fn()
      
      render(
        <Button onFocus={handleFocus} onBlur={handleBlur}>
          Focus me
        </Button>
      )
      
      const button = screen.getByRole('button')
      
      await user.click(button)
      expect(handleFocus).toHaveBeenCalled()
      
      await user.tab()
      expect(handleBlur).toHaveBeenCalled()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(
        <Button 
          aria-label="Custom label"
          aria-describedby="description"
          role="button"
        >
          Accessible Button
        </Button>
      )
      
      const button = screen.getByRole('button', { name: /custom label/i })
      expect(button).toHaveAttribute('aria-describedby', 'description')
    })

    it('is focusable by default', () => {
      render(<Button>Focusable Button</Button>)
      
      const button = screen.getByRole('button')
      expect(button).not.toHaveAttribute('tabindex', '-1')
    })

    it('is not focusable when disabled', () => {
      render(<Button disabled>Disabled Button</Button>)
      
      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
    })

    it('announces loading state to screen readers', () => {
      render(
        <Button loading aria-label="Saving document...">
          Save
        </Button>
      )
      
      const button = screen.getByRole('button', { name: /saving document/i })
      expect(button).toBeInTheDocument()
    })

    it('has sufficient color contrast', () => {
      render(<Button variant="primary">Primary Button</Button>)
      
      const button = screen.getByRole('button')
      const styles = window.getComputedStyle(button)
      
      // This would need actual color contrast calculation in a real test
      expect(button).toBeAccessible()
    })
  })

  describe('Form Integration', () => {
    it('submits form when type is submit', async () => {
      const handleSubmit = jest.fn(e => e.preventDefault())
      
      render(
        <form onSubmit={handleSubmit}>
          <Button type="submit">Submit Form</Button>
        </form>
      )
      
      const button = screen.getByRole('button', { name: /submit form/i })
      await user.click(button)
      
      expect(handleSubmit).toHaveBeenCalled()
    })

    it('resets form when type is reset', async () => {
      render(
        <form>
          <input defaultValue="test" />
          <Button type="reset">Reset Form</Button>
        </form>
      )
      
      const input = screen.getByRole('textbox')
      const button = screen.getByRole('button', { name: /reset form/i })
      
      expect(input).toHaveValue('test')
      
      await user.click(button)
      
      await waitFor(() => {
        expect(input).toHaveValue('')
      })
    })
  })

  describe('Performance', () => {
    it('does not re-render unnecessarily', () => {
      const renderSpy = jest.fn()
      
      const TestButton = React.memo(({ children, ...props }: any) => {
        renderSpy()
        return <Button {...props}>{children}</Button>
      })
      
      const { rerender } = render(<TestButton>Test</TestButton>)
      
      expect(renderSpy).toHaveBeenCalledTimes(1)
      
      // Re-render with same props
      rerender(<TestButton>Test</TestButton>)
      
      expect(renderSpy).toHaveBeenCalledTimes(1)
    })
  })

  describe('Edge Cases', () => {
    it('handles empty children gracefully', () => {
      render(<Button>{null}</Button>)
      
      const button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
      expect(button).toBeEmptyDOMElement()
    })

    it('handles very long text content', () => {
      const longText = 'A'.repeat(1000)
      render(<Button>{longText}</Button>)
      
      const button = screen.getByRole('button')
      expect(button).toHaveTextContent(longText)
    })

    it('handles rapid clicks gracefully', async () => {
      const handleClick = jest.fn()
      render(<Button onClick={handleClick}>Rapid Click</Button>)
      
      const button = screen.getByRole('button')
      
      // Simulate rapid clicking
      await user.click(button)
      await user.click(button)
      await user.click(button)
      
      expect(handleClick).toHaveBeenCalledTimes(3)
    })

    it('handles ref forwarding correctly', () => {
      const ref = React.createRef<HTMLButtonElement>()
      render(<Button ref={ref}>Ref Button</Button>)
      
      expect(ref.current).toBeInstanceOf(HTMLButtonElement)
      expect(ref.current).toHaveTextContent('Ref Button')
    })
  })

  describe('Custom Props', () => {
    it('forwards custom HTML attributes', () => {
      render(
        <Button 
          data-testid="custom-button"
          data-analytics="button-click"
          title="Custom tooltip"
        >
          Custom Button
        </Button>
      )
      
      const button = screen.getByTestId('custom-button')
      expect(button).toHaveAttribute('data-analytics', 'button-click')
      expect(button).toHaveAttribute('title', 'Custom tooltip')
    })

    it('handles custom event handlers', async () => {
      const handleMouseEnter = jest.fn()
      const handleMouseLeave = jest.fn()
      
      render(
        <Button 
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          Hover Button
        </Button>
      )
      
      const button = screen.getByRole('button')
      
      await user.hover(button)
      expect(handleMouseEnter).toHaveBeenCalled()
      
      await user.unhover(button)
      expect(handleMouseLeave).toHaveBeenCalled()
    })
  })
})
