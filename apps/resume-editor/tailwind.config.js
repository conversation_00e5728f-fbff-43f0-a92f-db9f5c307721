/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./stories/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      // Design tokens for themeable system
      colors: {
        // Primary brand colors
        primary: {
          50: 'rgb(var(--color-primary-50) / <alpha-value>)',
          100: 'rgb(var(--color-primary-100) / <alpha-value>)',
          200: 'rgb(var(--color-primary-200) / <alpha-value>)',
          300: 'rgb(var(--color-primary-300) / <alpha-value>)',
          400: 'rgb(var(--color-primary-400) / <alpha-value>)',
          500: 'rgb(var(--color-primary-500) / <alpha-value>)',
          600: 'rgb(var(--color-primary-600) / <alpha-value>)',
          700: 'rgb(var(--color-primary-700) / <alpha-value>)',
          800: 'rgb(var(--color-primary-800) / <alpha-value>)',
          900: 'rgb(var(--color-primary-900) / <alpha-value>)',
          950: 'rgb(var(--color-primary-950) / <alpha-value>)',
        },
        // Semantic colors
        success: {
          50: 'rgb(var(--color-success-50) / <alpha-value>)',
          500: 'rgb(var(--color-success-500) / <alpha-value>)',
          600: 'rgb(var(--color-success-600) / <alpha-value>)',
        },
        warning: {
          50: 'rgb(var(--color-warning-50) / <alpha-value>)',
          500: 'rgb(var(--color-warning-500) / <alpha-value>)',
          600: 'rgb(var(--color-warning-600) / <alpha-value>)',
        },
        error: {
          50: 'rgb(var(--color-error-50) / <alpha-value>)',
          500: 'rgb(var(--color-error-500) / <alpha-value>)',
          600: 'rgb(var(--color-error-600) / <alpha-value>)',
        },
        // Neutral colors for UI
        neutral: {
          0: 'rgb(var(--color-neutral-0) / <alpha-value>)',
          50: 'rgb(var(--color-neutral-50) / <alpha-value>)',
          100: 'rgb(var(--color-neutral-100) / <alpha-value>)',
          200: 'rgb(var(--color-neutral-200) / <alpha-value>)',
          300: 'rgb(var(--color-neutral-300) / <alpha-value>)',
          400: 'rgb(var(--color-neutral-400) / <alpha-value>)',
          500: 'rgb(var(--color-neutral-500) / <alpha-value>)',
          600: 'rgb(var(--color-neutral-600) / <alpha-value>)',
          700: 'rgb(var(--color-neutral-700) / <alpha-value>)',
          800: 'rgb(var(--color-neutral-800) / <alpha-value>)',
          900: 'rgb(var(--color-neutral-900) / <alpha-value>)',
          950: 'rgb(var(--color-neutral-950) / <alpha-value>)',
        },
        // Editor-specific colors
        editor: {
          background: 'rgb(var(--color-editor-background) / <alpha-value>)',
          surface: 'rgb(var(--color-editor-surface) / <alpha-value>)',
          border: 'rgb(var(--color-editor-border) / <alpha-value>)',
          text: 'rgb(var(--color-editor-text) / <alpha-value>)',
          'text-muted': 'rgb(var(--color-editor-text-muted) / <alpha-value>)',
          selection: 'rgb(var(--color-editor-selection) / <alpha-value>)',
          cursor: 'rgb(var(--color-editor-cursor) / <alpha-value>)',
        },
        // Collaboration colors
        collaboration: {
          'user-1': 'rgb(var(--color-collaboration-user-1) / <alpha-value>)',
          'user-2': 'rgb(var(--color-collaboration-user-2) / <alpha-value>)',
          'user-3': 'rgb(var(--color-collaboration-user-3) / <alpha-value>)',
          'user-4': 'rgb(var(--color-collaboration-user-4) / <alpha-value>)',
          'user-5': 'rgb(var(--color-collaboration-user-5) / <alpha-value>)',
        },
      },
      
      // Typography scale
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
        '7xl': ['4.5rem', { lineHeight: '1' }],
        '8xl': ['6rem', { lineHeight: '1' }],
        '9xl': ['8rem', { lineHeight: '1' }],
      },
      
      // Spacing scale
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      
      // Animation and transitions
      animation: {
        'fade-in': 'fadeIn 0.2s ease-in-out',
        'fade-out': 'fadeOut 0.2s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
        'slide-out': 'slideOut 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'scale-out': 'scaleOut 0.2s ease-out',
        'bounce-subtle': 'bounceSubtle 0.4s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        slideIn: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideOut: {
          '0%': { transform: 'translateY(0)', opacity: '1' },
          '100%': { transform: 'translateY(-10px)', opacity: '0' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        scaleOut: {
          '0%': { transform: 'scale(1)', opacity: '1' },
          '100%': { transform: 'scale(0.95)', opacity: '0' },
        },
        bounceSubtle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-4px)' },
        },
      },
      
      // Custom shadows
      boxShadow: {
        'soft': '0 2px 8px 0 rgb(0 0 0 / 0.08)',
        'medium': '0 4px 16px 0 rgb(0 0 0 / 0.12)',
        'hard': '0 8px 32px 0 rgb(0 0 0 / 0.16)',
        'editor': '0 0 0 1px rgb(var(--color-editor-border)), 0 2px 8px 0 rgb(0 0 0 / 0.08)',
        'collaboration': '0 0 0 2px rgb(var(--color-primary-500) / 0.2), 0 0 0 4px rgb(var(--color-primary-500) / 0.1)',
      },
      
      // Border radius scale
      borderRadius: {
        'xs': '0.125rem',
        'sm': '0.25rem',
        'md': '0.375rem',
        'lg': '0.5rem',
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
      
      // Z-index scale
      zIndex: {
        'dropdown': '1000',
        'sticky': '1020',
        'fixed': '1030',
        'modal-backdrop': '1040',
        'modal': '1050',
        'popover': '1060',
        'tooltip': '1070',
        'toast': '1080',
      },
      
      // Custom fonts
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
        'mono': ['JetBrains Mono', 'Consolas', 'monospace'],
        'display': ['Cal Sans', 'Inter', 'system-ui', 'sans-serif'],
      },
      
      // Backdrop blur
      backdropBlur: {
        'xs': '2px',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
    require('@tailwindcss/aspect-ratio'),
    
    // Custom plugin for design tokens
    function({ addBase, theme }) {
      addBase({
        ':root': {
          // Primary colors (Blue theme)
          '--color-primary-50': '239 246 255',
          '--color-primary-100': '219 234 254',
          '--color-primary-200': '191 219 254',
          '--color-primary-300': '147 197 253',
          '--color-primary-400': '96 165 250',
          '--color-primary-500': '59 130 246',
          '--color-primary-600': '37 99 235',
          '--color-primary-700': '29 78 216',
          '--color-primary-800': '30 64 175',
          '--color-primary-900': '30 58 138',
          '--color-primary-950': '23 37 84',
          
          // Semantic colors
          '--color-success-50': '240 253 244',
          '--color-success-500': '34 197 94',
          '--color-success-600': '22 163 74',
          
          '--color-warning-50': '255 251 235',
          '--color-warning-500': '245 158 11',
          '--color-warning-600': '217 119 6',
          
          '--color-error-50': '254 242 242',
          '--color-error-500': '239 68 68',
          '--color-error-600': '220 38 38',
          
          // Neutral colors
          '--color-neutral-0': '255 255 255',
          '--color-neutral-50': '249 250 251',
          '--color-neutral-100': '243 244 246',
          '--color-neutral-200': '229 231 235',
          '--color-neutral-300': '209 213 219',
          '--color-neutral-400': '156 163 175',
          '--color-neutral-500': '107 114 128',
          '--color-neutral-600': '75 85 99',
          '--color-neutral-700': '55 65 81',
          '--color-neutral-800': '31 41 55',
          '--color-neutral-900': '17 24 39',
          '--color-neutral-950': '3 7 18',
          
          // Editor colors
          '--color-editor-background': '255 255 255',
          '--color-editor-surface': '249 250 251',
          '--color-editor-border': '229 231 235',
          '--color-editor-text': '17 24 39',
          '--color-editor-text-muted': '107 114 128',
          '--color-editor-selection': '191 219 254',
          '--color-editor-cursor': '59 130 246',
          
          // Collaboration colors
          '--color-collaboration-user-1': '239 68 68',
          '--color-collaboration-user-2': '34 197 94',
          '--color-collaboration-user-3': '245 158 11',
          '--color-collaboration-user-4': '168 85 247',
          '--color-collaboration-user-5': '236 72 153',
        },
        
        '.dark': {
          // Dark mode overrides
          '--color-editor-background': '3 7 18',
          '--color-editor-surface': '17 24 39',
          '--color-editor-border': '55 65 81',
          '--color-editor-text': '249 250 251',
          '--color-editor-text-muted': '156 163 175',
          '--color-editor-selection': '30 64 175',
        },
      })
    },
  ],
}
