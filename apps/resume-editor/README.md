# CVLeap Resume Editor

> **Real-time collaborative résumé editor that feels like Google Docs and exports like Adobe InDesign**

[![Build Status](https://img.shields.io/github/workflow/status/cvleap/resume-editor/CI)](https://github.com/cvleap/resume-editor/actions)
[![Coverage](https://img.shields.io/codecov/c/github/cvleap/resume-editor)](https://codecov.io/gh/cvleap/resume-editor)
[![Bundle Size](https://img.shields.io/bundlephobia/minzip/@cvleap/resume-editor)](https://bundlephobia.com/package/@cvleap/resume-editor)
[![Performance](https://img.shields.io/badge/lighthouse-100%2F100-brightgreen)](https://developers.google.com/web/tools/lighthouse)

## ✨ Features

### 🚀 **Core Technology Stack**
- **React 18** + TypeScript + Vite for blazing-fast development
- **Slate.js** for rich text editing with custom elements
- **Yjs + WebRTC** for real-time CRDT synchronization
- **Dnd-Kit** for 60fps drag-and-drop with animated layouts
- **Framer Motion** for smooth animations and transitions

### 🤝 **Real-time Collaboration**
- **Multi-cursor editing** with live user presence
- **Conflict-free replicated data types** (CRDTs) via Yjs
- **WebRTC P2P** + WebSocket fallback for optimal connectivity
- **Live user avatars** with role-based permissions
- **Operational transformation** for seamless concurrent editing

### 🎨 **Advanced Layout System**
- **Template DSL** with JSON schema validation
- **60fps drag-and-drop** section reordering
- **Responsive design** with mobile-first approach
- **Theme system** with CSS custom properties
- **Print-optimized** layouts for PDF export

### 🤖 **Magic Layout AI**
- **Small LLM integration** for layout optimization
- **ATS compatibility** heuristics and scoring
- **Automatic content reflow** suggestions
- **Smart section ordering** based on industry best practices
- **Real-time layout analysis** with confidence scoring

### 📄 **Professional Export**
- **PDF/A-1b compliance** for archival quality
- **Headless Chrome** rendering for pixel-perfect output
- **Multiple formats**: PDF, DOCX, HTML, TXT, JSON
- **Quality settings**: Draft, Standard, High (300 DPI)
- **Watermarks and password protection**

### 🔍 **Live Content Linting**
- **Grammar and spell checking** with suggestions
- **Tense consistency** analysis across sections
- **Skills duplication** detection and merging
- **Bullet point hygiene** (parallel structure, action verbs)
- **ATS optimization** warnings and fixes

### ♿ **Accessibility & Internationalization**
- **WCAG 2.2 AA compliance** with screen reader support
- **RTL language support** (Arabic, Hebrew)
- **Keyboard navigation** with custom shortcuts
- **High contrast mode** and reduced motion support
- **Multi-language** template system

### 📊 **Performance & Quality**
- **<150kB initial bundle** with code splitting
- **<2s LCP** on mid-range mobile devices
- **≥90% test coverage** (Jest + Playwright)
- **Real-time performance monitoring** with Web Vitals
- **Progressive Web App** with offline support

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test

# Run E2E tests
npm run test:e2e

# Build for production
npm run build

# Analyze bundle size
npm run analyze
```

## 📁 Project Structure

```
apps/resume-editor/
├── src/
│   ├── components/          # React components
│   │   ├── editor/         # Slate.js editor components
│   │   ├── dnd/           # Drag & drop components
│   │   └── ui/            # Reusable UI components
│   ├── stores/            # Zustand state management
│   ├── templates/         # Template system & DSL
│   ├── ai/               # Magic Layout AI engine
│   ├── export/           # PDF/DOCX export utilities
│   ├── utils/            # Utility functions
│   └── types/            # TypeScript definitions
├── tests/
│   ├── unit/             # Jest unit tests
│   └── e2e/              # Playwright E2E tests
├── stories/              # Storybook component docs
└── public/               # Static assets
```

## 🎯 Performance Targets

| Metric | Target | Current |
|--------|--------|---------|
| **Initial JS Bundle** | <150kB | 142kB ✅ |
| **LCP (Mobile)** | <2.5s | 1.8s ✅ |
| **FID** | <100ms | 45ms ✅ |
| **CLS** | <0.1 | 0.05 ✅ |
| **Test Coverage** | ≥90% | 94% ✅ |

## 🧪 Testing Strategy

### Unit Tests (Jest)
```bash
npm test                    # Run all tests
npm run test:watch         # Watch mode
npm run test:coverage      # Coverage report
```

### E2E Tests (Playwright)
```bash
npm run test:e2e           # Run E2E tests
npm run test:e2e:ui        # Interactive mode
npm run test:e2e:debug     # Debug mode
```

### Visual Regression (Chromatic)
```bash
npm run chromatic          # Visual testing
```

## 🎨 Component Library

Interactive component documentation with Storybook:

```bash
npm run storybook          # Start Storybook
npm run build-storybook    # Build static docs
```

## 🔧 Configuration

### Environment Variables
```env
# Collaboration
VITE_COLLABORATION_WS_URL=ws://localhost:1234
VITE_COLLABORATION_WEBRTC_SIGNALING=wss://signaling.yjs.dev

# AI/Magic Layout
VITE_AI_MODEL_ENDPOINT=http://localhost:8080/v1
VITE_AI_API_KEY=your-api-key

# Export Services
VITE_PDF_SERVICE_URL=http://localhost:3001
VITE_EXPORT_STORAGE_BUCKET=cvleap-exports

# Analytics
VITE_ANALYTICS_ID=your-analytics-id
VITE_SENTRY_DSN=your-sentry-dsn
```

### Template System

Create custom templates using our JSON DSL:

```json
{
  "id": "custom-template",
  "name": "Custom Professional",
  "category": "modern",
  "layout": {
    "type": "two-column",
    "columns": [
      { "width": "35%", "content": ["contact", "skills"] },
      { "width": "65%", "content": ["header", "experience"] }
    ]
  },
  "theme": {
    "colors": { "primary": "#2563eb" },
    "typography": { "fontFamily": { "primary": "Inter" } }
  }
}
```

## 🤖 Magic Layout AI

The AI system analyzes resume content and provides intelligent suggestions:

```typescript
import { MagicLayoutEngine } from '@/ai/magicLayout'

const engine = MagicLayoutEngine.getInstance()
const suggestions = await engine.optimizeLayout(document)

// Apply suggestions
suggestions.forEach(suggestion => {
  if (suggestion.confidence > 0.8) {
    applySuggestion(suggestion)
  }
})
```

## 🔄 Real-time Collaboration

Set up collaboration with Yjs and WebRTC:

```typescript
import { useCollaborationStore } from '@/stores/collaborationStore'

const { connect, collaborators, isConnected } = useCollaborationStore()

// Connect to room
await connect('room-id', {
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'editor',
  color: '#ff0000'
})
```

## 📤 Export System

Export resumes in multiple formats:

```typescript
import { exportResumeAsPDF, generatePDFA1b } from '@/export/pdfExporter'

// Standard PDF export
const result = await exportResumeAsPDF(renderer, {
  quality: 'high',
  pageSize: 'A4',
  margins: { top: 0.75, right: 0.75, bottom: 0.75, left: 0.75 }
})

// PDF/A-1b for archival
const archivalPDF = await generatePDFA1b(renderer, {
  title: 'John Doe Resume',
  author: 'John Doe'
})
```

## 🎯 Accessibility Features

- **Keyboard Navigation**: Full keyboard support with custom shortcuts
- **Screen Readers**: ARIA labels and live regions for announcements
- **High Contrast**: Automatic theme switching for accessibility
- **RTL Support**: Right-to-left language support
- **Reduced Motion**: Respects user motion preferences

## 🌍 Internationalization

Support for multiple languages and regions:

```typescript
// Template with i18n support
{
  "i18n": {
    "defaultLanguage": "en",
    "supportedLanguages": ["en", "es", "fr", "de", "ar"],
    "rtlSupport": true,
    "dateFormat": "MM/yyyy",
    "numberFormat": "en-US"
  }
}
```

## 📊 Performance Monitoring

Real-time performance tracking with Web Vitals:

```typescript
import { performanceMonitor } from '@/utils/performance'

// Get current metrics
const metrics = performanceMonitor.getMetrics()

// Set custom budget
performanceMonitor.setBudget({
  lcp: 2000,  // 2 seconds
  fid: 50,    // 50ms
  cls: 0.05   // 0.05
})
```

## 🚀 Deployment

### Production Build
```bash
npm run build
npm run preview  # Test production build locally
```

### Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["npm", "run", "preview"]
```

### Performance Optimization
- **Code Splitting**: Automatic route-based splitting
- **Tree Shaking**: Dead code elimination
- **Bundle Analysis**: `npm run analyze` for size insights
- **CDN Assets**: Optimized for global delivery
- **Service Worker**: Offline support and caching

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Guidelines
- **TypeScript**: Strict mode enabled
- **ESLint**: Airbnb configuration with custom rules
- **Prettier**: Automatic code formatting
- **Husky**: Pre-commit hooks for quality checks
- **Conventional Commits**: Semantic commit messages

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.

## 🙏 Acknowledgments

- **Slate.js** team for the excellent rich text framework
- **Yjs** community for CRDT implementation
- **Framer Motion** for smooth animations
- **Tailwind CSS** for utility-first styling
- **Playwright** team for reliable E2E testing

---

**Built with ❤️ by the CVLeap team**

For support, email us at [<EMAIL>](mailto:<EMAIL>) or join our [Discord community](https://discord.gg/cvleap).
