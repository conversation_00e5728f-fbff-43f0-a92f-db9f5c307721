import type { StorybookConfig } from '@storybook/nextjs'
import path from 'path'

const config: StorybookConfig = {
  stories: [
    '../src/**/*.stories.@(js|jsx|ts|tsx|mdx)',
    '../src/**/*.story.@(js|jsx|ts|tsx|mdx)',
  ],
  
  addons: [
    '@storybook/addon-essentials',
    '@storybook/addon-interactions',
    '@storybook/addon-links',
    '@storybook/addon-docs',
    '@storybook/addon-controls',
    '@storybook/addon-viewport',
    '@storybook/addon-backgrounds',
    '@storybook/addon-measure',
    '@storybook/addon-outline',
    '@storybook/addon-a11y',
    '@storybook/addon-design-tokens',
    '@chromatic-com/storybook',
  ],
  
  framework: {
    name: '@storybook/nextjs',
    options: {
      nextConfigPath: '../next.config.js',
    },
  },
  
  typescript: {
    check: false,
    reactDocgen: 'react-docgen-typescript',
    reactDocgenTypescriptOptions: {
      shouldExtractLiteralValuesFromEnum: true,
      propFilter: (prop) => (prop.parent ? !/node_modules/.test(prop.parent.fileName) : true),
    },
  },
  
  webpackFinal: async (config) => {
    // Add path aliases
    if (config.resolve) {
      config.resolve.alias = {
        ...config.resolve.alias,
        '@': path.resolve(__dirname, '../src'),
        '@/components': path.resolve(__dirname, '../src/components'),
        '@/lib': path.resolve(__dirname, '../src/lib'),
        '@/hooks': path.resolve(__dirname, '../src/hooks'),
        '@/stores': path.resolve(__dirname, '../src/stores'),
        '@/types': path.resolve(__dirname, '../src/types'),
        '@/utils': path.resolve(__dirname, '../src/utils'),
      }
    }
    
    // Handle CSS modules
    const cssRule = config.module?.rules?.find(
      (rule) => rule && typeof rule === 'object' && rule.test && rule.test.toString().includes('css')
    )
    
    if (cssRule && typeof cssRule === 'object' && cssRule.use && Array.isArray(cssRule.use)) {
      cssRule.use.forEach((use) => {
        if (typeof use === 'object' && use.loader && use.loader.includes('css-loader')) {
          use.options = {
            ...use.options,
            modules: {
              auto: true,
              localIdentName: '[name]__[local]--[hash:base64:5]',
            },
          }
        }
      })
    }
    
    return config
  },
  
  features: {
    experimentalRSC: true,
  },
  
  docs: {
    autodocs: 'tag',
    defaultName: 'Documentation',
  },
  
  staticDirs: ['../public'],
  
  env: (config) => ({
    ...config,
    STORYBOOK: 'true',
  }),
}

export default config
