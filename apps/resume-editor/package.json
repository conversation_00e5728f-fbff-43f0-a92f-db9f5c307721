{"name": "@cvleap/resume-editor", "version": "1.0.0", "description": "Real-time collaborative résumé editor with Google Docs feel and Adobe InDesign output", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test", "test:coverage": "jest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "analyze": "npx vite-bundle-analyzer", "perf": "lighthouse http://localhost:4173 --view"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "slate": "^0.101.5", "slate-react": "^0.102.0", "slate-history": "^0.100.0", "yjs": "^13.6.8", "y-websocket": "^1.5.0", "y-webrtc": "^10.2.5", "slate-yjs": "^4.0.2", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "framer-motion": "^10.16.4", "pdf-lib": "^1.17.1", "puppeteer": "^21.5.2", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "zustand": "^4.4.6", "immer": "^10.0.3", "react-router-dom": "^6.18.0", "react-query": "^3.39.3", "react-hook-form": "^7.47.0", "zod": "^3.22.4", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.292.0", "react-hotkeys-hook": "^4.4.1", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "fuse.js": "^7.0.0", "date-fns": "^2.30.0", "nanoid": "^5.0.3"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "vite": "^4.5.0", "typescript": "^5.2.2", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "eslint-plugin-jsx-a11y": "^6.8.0", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/aspect-ratio": "^0.4.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1", "@playwright/test": "^1.40.0", "@storybook/react": "^7.5.3", "@storybook/react-vite": "^7.5.3", "@storybook/addon-essentials": "^7.5.3", "@storybook/addon-interactions": "^7.5.3", "@storybook/addon-links": "^7.5.3", "@storybook/addon-a11y": "^7.5.3", "@storybook/testing-library": "^0.2.2", "vite-bundle-analyzer": "^0.7.0", "vite-plugin-pwa": "^0.17.0", "workbox-window": "^7.0.0", "@types/jest": "^29.5.8", "msw": "^2.0.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/test/setup.ts"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1", "\\.(css|less|scss|sass)$": "identity-obj-proxy"}, "collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/test/**/*", "!src/stories/**/*"], "coverageThreshold": {"global": {"branches": 90, "functions": 90, "lines": 90, "statements": 90}}}}