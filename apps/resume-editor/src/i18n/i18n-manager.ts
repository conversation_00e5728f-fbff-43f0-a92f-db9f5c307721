/**
 * Internationalization Manager
 * RTL support, locale management, and translation system
 */

export interface Locale {
  code: string
  name: string
  nativeName: string
  direction: 'ltr' | 'rtl'
  dateFormat: string
  numberFormat: Intl.NumberFormatOptions
  currency: string
  region: string
}

export interface Translation {
  [key: string]: string | Translation
}

export interface I18nOptions {
  defaultLocale: string
  fallbackLocale: string
  supportedLocales: string[]
  loadTranslations: (locale: string) => Promise<Translation>
  detectBrowserLocale: boolean
  persistLocale: boolean
}

export class I18nManager {
  private currentLocale: string
  private translations: Map<string, Translation> = new Map()
  private locales: Map<string, Locale> = new Map()
  private options: I18nOptions
  private observers: Set<(locale: string) => void> = new Set()

  constructor(options: I18nOptions) {
    this.options = options
    this.currentLocale = options.defaultLocale

    this.initializeLocales()
    this.initializeLocale()
  }

  private initializeLocales(): void {
    // Define supported locales
    const localeDefinitions: Locale[] = [
      {
        code: 'en',
        name: 'English',
        nativeName: 'English',
        direction: 'ltr',
        dateFormat: 'MM/dd/yyyy',
        numberFormat: { style: 'decimal', minimumFractionDigits: 0 },
        currency: 'USD',
        region: 'US'
      },
      {
        code: 'es',
        name: 'Spanish',
        nativeName: 'Español',
        direction: 'ltr',
        dateFormat: 'dd/MM/yyyy',
        numberFormat: { style: 'decimal', minimumFractionDigits: 0 },
        currency: 'EUR',
        region: 'ES'
      },
      {
        code: 'fr',
        name: 'French',
        nativeName: 'Français',
        direction: 'ltr',
        dateFormat: 'dd/MM/yyyy',
        numberFormat: { style: 'decimal', minimumFractionDigits: 0 },
        currency: 'EUR',
        region: 'FR'
      },
      {
        code: 'de',
        name: 'German',
        nativeName: 'Deutsch',
        direction: 'ltr',
        dateFormat: 'dd.MM.yyyy',
        numberFormat: { style: 'decimal', minimumFractionDigits: 0 },
        currency: 'EUR',
        region: 'DE'
      },
      {
        code: 'ar',
        name: 'Arabic',
        nativeName: 'العربية',
        direction: 'rtl',
        dateFormat: 'dd/MM/yyyy',
        numberFormat: { style: 'decimal', minimumFractionDigits: 0 },
        currency: 'USD',
        region: 'SA'
      },
      {
        code: 'he',
        name: 'Hebrew',
        nativeName: 'עברית',
        direction: 'rtl',
        dateFormat: 'dd/MM/yyyy',
        numberFormat: { style: 'decimal', minimumFractionDigits: 0 },
        currency: 'ILS',
        region: 'IL'
      },
      {
        code: 'zh',
        name: 'Chinese',
        nativeName: '中文',
        direction: 'ltr',
        dateFormat: 'yyyy/MM/dd',
        numberFormat: { style: 'decimal', minimumFractionDigits: 0 },
        currency: 'CNY',
        region: 'CN'
      },
      {
        code: 'ja',
        name: 'Japanese',
        nativeName: '日本語',
        direction: 'ltr',
        dateFormat: 'yyyy/MM/dd',
        numberFormat: { style: 'decimal', minimumFractionDigits: 0 },
        currency: 'JPY',
        region: 'JP'
      }
    ]

    localeDefinitions.forEach(locale => {
      this.locales.set(locale.code, locale)
    })
  }

  private async initializeLocale(): Promise<void> {
    // Detect browser locale if enabled
    if (this.options.detectBrowserLocale) {
      const browserLocale = this.detectBrowserLocale()
      if (browserLocale && this.options.supportedLocales.includes(browserLocale)) {
        this.currentLocale = browserLocale
      }
    }

    // Load persisted locale
    if (this.options.persistLocale) {
      const persistedLocale = localStorage.getItem('cvleap-locale')
      if (persistedLocale && this.options.supportedLocales.includes(persistedLocale)) {
        this.currentLocale = persistedLocale
      }
    }

    // Load initial translations
    await this.loadLocale(this.currentLocale)
    this.applyLocale(this.currentLocale)
  }

  private detectBrowserLocale(): string | null {
    const browserLanguages = navigator.languages || [navigator.language]
    
    for (const lang of browserLanguages) {
      // Try exact match first
      if (this.options.supportedLocales.includes(lang)) {
        return lang
      }
      
      // Try language code without region
      const langCode = lang.split('-')[0]
      if (this.options.supportedLocales.includes(langCode)) {
        return langCode
      }
    }
    
    return null
  }

  // Public API
  async setLocale(locale: string): Promise<void> {
    if (!this.options.supportedLocales.includes(locale)) {
      throw new Error(`Unsupported locale: ${locale}`)
    }

    if (locale === this.currentLocale) return

    await this.loadLocale(locale)
    this.currentLocale = locale
    this.applyLocale(locale)

    // Persist locale
    if (this.options.persistLocale) {
      localStorage.setItem('cvleap-locale', locale)
    }

    // Notify observers
    this.observers.forEach(observer => observer(locale))
  }

  getCurrentLocale(): string {
    return this.currentLocale
  }

  getCurrentLocaleInfo(): Locale | null {
    return this.locales.get(this.currentLocale) || null
  }

  getSupportedLocales(): Locale[] {
    return this.options.supportedLocales
      .map(code => this.locales.get(code))
      .filter(Boolean) as Locale[]
  }

  isRTL(): boolean {
    const locale = this.getCurrentLocaleInfo()
    return locale?.direction === 'rtl'
  }

  // Translation methods
  t(key: string, params?: Record<string, string | number>): string {
    const translation = this.getTranslation(key)
    
    if (!translation) {
      console.warn(`Translation missing for key: ${key}`)
      return key
    }

    return this.interpolate(translation, params)
  }

  private getTranslation(key: string): string | null {
    const currentTranslations = this.translations.get(this.currentLocale)
    const fallbackTranslations = this.translations.get(this.options.fallbackLocale)
    
    return (
      this.getNestedValue(currentTranslations, key) ||
      this.getNestedValue(fallbackTranslations, key) ||
      null
    )
  }

  private getNestedValue(obj: any, path: string): string | null {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null
    }, obj)
  }

  private interpolate(template: string, params?: Record<string, string | number>): string {
    if (!params) return template

    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return params[key] !== undefined ? String(params[key]) : match
    })
  }

  // Locale loading
  private async loadLocale(locale: string): Promise<void> {
    if (this.translations.has(locale)) return

    try {
      const translations = await this.options.loadTranslations(locale)
      this.translations.set(locale, translations)
    } catch (error) {
      console.error(`Failed to load translations for locale: ${locale}`, error)
      
      // Load fallback if not already loaded
      if (locale !== this.options.fallbackLocale && !this.translations.has(this.options.fallbackLocale)) {
        try {
          const fallbackTranslations = await this.options.loadTranslations(this.options.fallbackLocale)
          this.translations.set(this.options.fallbackLocale, fallbackTranslations)
        } catch (fallbackError) {
          console.error(`Failed to load fallback translations`, fallbackError)
        }
      }
    }
  }

  private applyLocale(locale: string): void {
    const localeInfo = this.locales.get(locale)
    if (!localeInfo) return

    // Set document attributes
    document.documentElement.lang = locale
    document.documentElement.dir = localeInfo.direction

    // Apply RTL/LTR classes
    document.documentElement.classList.toggle('rtl', localeInfo.direction === 'rtl')
    document.documentElement.classList.toggle('ltr', localeInfo.direction === 'ltr')

    // Set locale-specific CSS custom properties
    document.documentElement.style.setProperty('--locale-direction', localeInfo.direction)
  }

  // Formatting methods
  formatDate(date: Date, options?: Intl.DateTimeFormatOptions): string {
    const locale = this.getCurrentLocaleInfo()
    const formatOptions = options || {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }

    return new Intl.DateTimeFormat(this.currentLocale, formatOptions).format(date)
  }

  formatNumber(number: number, options?: Intl.NumberFormatOptions): string {
    const locale = this.getCurrentLocaleInfo()
    const formatOptions = options || locale?.numberFormat || {}

    return new Intl.NumberFormat(this.currentLocale, formatOptions).format(number)
  }

  formatCurrency(amount: number, currency?: string): string {
    const locale = this.getCurrentLocaleInfo()
    const currencyCode = currency || locale?.currency || 'USD'

    return new Intl.NumberFormat(this.currentLocale, {
      style: 'currency',
      currency: currencyCode
    }).format(amount)
  }

  formatRelativeTime(date: Date): string {
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    const rtf = new Intl.RelativeTimeFormat(this.currentLocale, { numeric: 'auto' })

    if (Math.abs(diffInSeconds) < 60) {
      return rtf.format(-diffInSeconds, 'second')
    } else if (Math.abs(diffInSeconds) < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute')
    } else if (Math.abs(diffInSeconds) < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour')
    } else if (Math.abs(diffInSeconds) < 2592000) {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day')
    } else if (Math.abs(diffInSeconds) < 31536000) {
      return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month')
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year')
    }
  }

  // Observer pattern
  subscribe(observer: (locale: string) => void): () => void {
    this.observers.add(observer)
    return () => this.observers.delete(observer)
  }

  // Utility methods
  getTextDirection(): 'ltr' | 'rtl' {
    return this.isRTL() ? 'rtl' : 'ltr'
  }

  getStartDirection(): 'left' | 'right' {
    return this.isRTL() ? 'right' : 'left'
  }

  getEndDirection(): 'left' | 'right' {
    return this.isRTL() ? 'left' : 'right'
  }

  // Cleanup
  destroy(): void {
    this.observers.clear()
    this.translations.clear()
  }
}

// React hook for i18n
export function useI18n() {
  const [currentLocale, setCurrentLocale] = React.useState('')
  const [isRTL, setIsRTL] = React.useState(false)
  const i18nRef = React.useRef<I18nManager | null>(null)

  React.useEffect(() => {
    if (i18nRef.current) {
      const unsubscribe = i18nRef.current.subscribe((locale) => {
        setCurrentLocale(locale)
        setIsRTL(i18nRef.current?.isRTL() || false)
      })

      // Set initial values
      setCurrentLocale(i18nRef.current.getCurrentLocale())
      setIsRTL(i18nRef.current.isRTL())

      return unsubscribe
    }
  }, [])

  const t = React.useCallback((key: string, params?: Record<string, string | number>) => {
    return i18nRef.current?.t(key, params) || key
  }, [])

  const setLocale = React.useCallback(async (locale: string) => {
    await i18nRef.current?.setLocale(locale)
  }, [])

  const formatDate = React.useCallback((date: Date, options?: Intl.DateTimeFormatOptions) => {
    return i18nRef.current?.formatDate(date, options) || date.toLocaleDateString()
  }, [])

  const formatNumber = React.useCallback((number: number, options?: Intl.NumberFormatOptions) => {
    return i18nRef.current?.formatNumber(number, options) || number.toString()
  }, [])

  const formatCurrency = React.useCallback((amount: number, currency?: string) => {
    return i18nRef.current?.formatCurrency(amount, currency) || amount.toString()
  }, [])

  return {
    currentLocale,
    isRTL,
    t,
    setLocale,
    formatDate,
    formatNumber,
    formatCurrency,
    supportedLocales: i18nRef.current?.getSupportedLocales() || [],
    textDirection: isRTL ? 'rtl' : 'ltr'
  }
}
