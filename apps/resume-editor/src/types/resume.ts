import { BaseEditor, Descendant } from 'slate'
import { ReactEditor } from 'slate-react'
import { HistoryEditor } from 'slate-history'

// Extend Slate types for TypeScript
declare module 'slate' {
  interface CustomTypes {
    Editor: BaseEditor & ReactEditor & HistoryEditor
    Element: CustomElement
    Text: CustomText
  }
}

// Custom text interface with formatting
export interface CustomText {
  text: string
  bold?: boolean
  italic?: boolean
  underline?: boolean
  strikethrough?: boolean
  code?: boolean
  color?: string
  backgroundColor?: string
  fontSize?: string
  fontFamily?: string
}

// Custom element types for resume sections
export type CustomElement = 
  | ParagraphElement
  | HeadingElement
  | BulletListElement
  | ListItemElement
  | ContactInfoElement
  | SectionElement
  | SkillElement
  | ExperienceElement
  | EducationElement
  | ProjectElement

export interface BaseElement {
  id: string
  type: string
  children: Descendant[]
}

export interface ParagraphElement extends BaseElement {
  type: 'paragraph'
  align?: 'left' | 'center' | 'right' | 'justify'
}

export interface HeadingElement extends BaseElement {
  type: 'heading'
  level: 1 | 2 | 3 | 4 | 5 | 6
  align?: 'left' | 'center' | 'right'
}

export interface BulletListElement extends BaseElement {
  type: 'bullet-list'
  style?: 'disc' | 'circle' | 'square' | 'none'
}

export interface ListItemElement extends BaseElement {
  type: 'list-item'
}

export interface ContactInfoElement extends BaseElement {
  type: 'contact-info'
  fields: {
    name?: string
    email?: string
    phone?: string
    location?: string
    website?: string
    linkedin?: string
    github?: string
  }
}

export interface SectionElement extends BaseElement {
  type: 'section'
  title: string
  collapsible?: boolean
  collapsed?: boolean
}

export interface SkillElement extends BaseElement {
  type: 'skill'
  category: string
  skills: string[]
  level?: 'beginner' | 'intermediate' | 'advanced' | 'expert'
}

export interface ExperienceElement extends BaseElement {
  type: 'experience'
  company: string
  position: string
  startDate: string
  endDate?: string
  current?: boolean
  location?: string
  description: Descendant[]
}

export interface EducationElement extends BaseElement {
  type: 'education'
  institution: string
  degree: string
  field?: string
  startDate: string
  endDate?: string
  gpa?: string
  honors?: string[]
}

export interface ProjectElement extends BaseElement {
  type: 'project'
  name: string
  description: Descendant[]
  technologies: string[]
  url?: string
  github?: string
  startDate?: string
  endDate?: string
}

// Resume document structure
export interface ResumeDocument {
  id: string
  title: string
  content: Descendant[]
  metadata: ResumeMetadata
  template: TemplateConfig
  version: number
  createdAt: string
  updatedAt: string
  collaborators: Collaborator[]
}

export interface ResumeMetadata {
  language: string
  direction: 'ltr' | 'rtl'
  pageSize: 'A4' | 'Letter' | 'Legal'
  margins: {
    top: number
    right: number
    bottom: number
    left: number
  }
  fonts: {
    primary: string
    secondary?: string
    monospace?: string
  }
  colors: {
    primary: string
    secondary?: string
    text: string
    background: string
  }
  spacing: {
    lineHeight: number
    paragraphSpacing: number
    sectionSpacing: number
  }
}

// Template system
export interface TemplateConfig {
  id: string
  name: string
  category: 'modern' | 'classic' | 'creative' | 'minimal' | 'academic'
  layout: LayoutConfig
  styling: StylingConfig
  sections: SectionConfig[]
}

export interface LayoutConfig {
  columns: 1 | 2 | 3
  columnRatio?: number[]
  header: 'full-width' | 'left-column' | 'right-column'
  footer?: 'full-width' | 'left-column' | 'right-column'
  sidebar?: 'left' | 'right' | 'none'
  sidebarWidth?: number
}

export interface StylingConfig {
  typography: {
    headingFont: string
    bodyFont: string
    headingSizes: Record<string, string>
    bodySize: string
    lineHeight: number
  }
  colors: {
    primary: string
    secondary: string
    accent: string
    text: string
    textLight: string
    background: string
    border: string
  }
  spacing: {
    section: string
    element: string
    compact: string
  }
  borders: {
    width: string
    style: 'solid' | 'dashed' | 'dotted'
    radius: string
  }
}

export interface SectionConfig {
  type: string
  required: boolean
  order: number
  title: string
  icon?: string
  maxItems?: number
  fields: FieldConfig[]
}

export interface FieldConfig {
  name: string
  type: 'text' | 'textarea' | 'date' | 'email' | 'url' | 'select' | 'multiselect'
  required: boolean
  placeholder?: string
  options?: string[]
  validation?: ValidationRule[]
}

export interface ValidationRule {
  type: 'required' | 'email' | 'url' | 'minLength' | 'maxLength' | 'pattern'
  value?: string | number
  message: string
}

// Collaboration types
export interface Collaborator {
  id: string
  name: string
  email: string
  avatar?: string
  role: 'owner' | 'editor' | 'viewer'
  color: string
  cursor?: CursorPosition
  selection?: SelectionRange
  lastSeen: string
  isOnline: boolean
}

export interface CursorPosition {
  path: number[]
  offset: number
}

export interface SelectionRange {
  anchor: CursorPosition
  focus: CursorPosition
}

// Version control types
export interface DocumentVersion {
  id: string
  documentId: string
  version: number
  title: string
  content: Descendant[]
  metadata: ResumeMetadata
  author: {
    id: string
    name: string
    email: string
  }
  createdAt: string
  message?: string
  parentVersion?: number
  tags: string[]
  isPublished: boolean
}

export interface VersionDiff {
  type: 'added' | 'removed' | 'modified'
  path: number[]
  oldValue?: any
  newValue?: any
  description: string
}

// Export types
export interface ExportOptions {
  format: 'pdf' | 'docx' | 'html' | 'txt' | 'json'
  template?: string
  quality: 'draft' | 'standard' | 'high'
  includeMetadata: boolean
  watermark?: string
  password?: string
}

export interface ExportResult {
  success: boolean
  url?: string
  error?: string
  metadata: {
    format: string
    size: number
    pages: number
    generatedAt: string
  }
}

// AI/Magic Layout types
export interface LayoutSuggestion {
  id: string
  type: 'reorder' | 'resize' | 'reformat' | 'optimize'
  title: string
  description: string
  confidence: number
  preview?: string
  changes: LayoutChange[]
}

export interface LayoutChange {
  path: number[]
  type: 'move' | 'resize' | 'style' | 'content'
  before: any
  after: any
  reason: string
}

// Linting types
export interface LintRule {
  id: string
  name: string
  category: 'content' | 'formatting' | 'ats' | 'grammar'
  severity: 'error' | 'warning' | 'info'
  description: string
  enabled: boolean
}

export interface LintIssue {
  id: string
  ruleId: string
  severity: 'error' | 'warning' | 'info'
  message: string
  path: number[]
  range?: {
    start: number
    end: number
  }
  suggestions?: LintSuggestion[]
}

export interface LintSuggestion {
  message: string
  fix?: {
    type: 'replace' | 'insert' | 'delete'
    text?: string
    range?: {
      start: number
      end: number
    }
  }
}
