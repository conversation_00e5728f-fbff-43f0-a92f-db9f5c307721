import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import * as Y from 'yjs'
import { WebrtcProvider } from 'y-webrtc'
import { WebsocketProvider } from 'y-websocket'
import { withYjs, YjsEditor } from 'slate-yjs'
import { Editor } from 'slate'
import { Collaborator, CursorPosition, SelectionRange } from '@/types/resume'

interface CollaborationState {
  // Connection state
  isConnected: boolean
  connectionType: 'websocket' | 'webrtc' | 'hybrid'
  roomId: string | null
  
  // Yjs document and providers
  ydoc: Y.Doc | null
  wsProvider: WebsocketProvider | null
  webrtcProvider: WebrtcProvider | null
  
  // Collaborators
  collaborators: Collaborator[]
  currentUser: Collaborator | null
  
  // Awareness (cursors, selections)
  awareness: Map<number, any>
  
  // Connection quality
  latency: number
  connectionQuality: 'excellent' | 'good' | 'poor' | 'disconnected'
  
  // Actions
  connect: (roomId: string, user: Omit<Collaborator, 'id' | 'isOnline' | 'lastSeen'>) => Promise<void>
  disconnect: () => void
  sendOperation: (operation: any) => void
  sendCursor: (cursor: { selection: SelectionRange | null; timestamp: number }) => void
  updateUserPresence: (presence: Partial<Collaborator>) => void
  
  // Editor integration
  bindEditor: (editor: Editor) => YjsEditor
  unbindEditor: () => void
}

export const useCollaborationStore = create<CollaborationState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // Initial state
      isConnected: false,
      connectionType: 'hybrid',
      roomId: null,
      ydoc: null,
      wsProvider: null,
      webrtcProvider: null,
      collaborators: [],
      currentUser: null,
      awareness: new Map(),
      latency: 0,
      connectionQuality: 'disconnected',

      // Connect to collaboration room
      connect: async (roomId: string, user: Omit<Collaborator, 'id' | 'isOnline' | 'lastSeen'>) => {
        const state = get()
        
        // Disconnect if already connected
        if (state.isConnected) {
          state.disconnect()
        }

        try {
          // Create Yjs document
          const ydoc = new Y.Doc()
          
          // Generate unique user ID
          const userId = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
          
          // Create current user
          const currentUser: Collaborator = {
            ...user,
            id: userId,
            isOnline: true,
            lastSeen: new Date().toISOString(),
          }

          // Setup WebSocket provider (primary)
          const wsProvider = new WebsocketProvider(
            process.env.VITE_COLLABORATION_WS_URL || 'ws://localhost:1234',
            roomId,
            ydoc,
            {
              connect: true,
              awareness: {
                user: currentUser,
              },
            }
          )

          // Setup WebRTC provider (fallback/P2P)
          const webrtcProvider = new WebrtcProvider(roomId, ydoc, {
            signaling: [
              'wss://signaling.yjs.dev',
              'wss://y-webrtc-signaling-eu.herokuapp.com',
              'wss://y-webrtc-signaling-us.herokuapp.com',
            ],
            awareness: wsProvider.awareness,
          })

          // Handle connection events
          wsProvider.on('status', (event: { status: string }) => {
            set((state) => {
              state.isConnected = event.status === 'connected'
              state.connectionQuality = event.status === 'connected' ? 'good' : 'disconnected'
            })
          })

          // Handle awareness changes (collaborators)
          wsProvider.awareness.on('change', () => {
            const awarenessStates = Array.from(wsProvider.awareness.getStates().entries())
            const collaborators: Collaborator[] = []

            awarenessStates.forEach(([clientId, state]) => {
              if (state.user && clientId !== wsProvider.awareness.clientID) {
                collaborators.push({
                  ...state.user,
                  isOnline: true,
                  cursor: state.cursor,
                  selection: state.selection,
                  lastSeen: new Date().toISOString(),
                })
              }
            })

            set((state) => {
              state.collaborators = collaborators
            })
          })

          // Monitor connection quality
          const qualityInterval = setInterval(() => {
            const start = Date.now()
            
            // Simple ping to measure latency
            wsProvider.awareness.setLocalStateField('ping', start)
            
            setTimeout(() => {
              const latency = Date.now() - start
              set((state) => {
                state.latency = latency
                state.connectionQuality = 
                  latency < 100 ? 'excellent' :
                  latency < 300 ? 'good' :
                  latency < 1000 ? 'poor' : 'disconnected'
              })
            }, 10)
          }, 5000)

          // Update state
          set((state) => {
            state.ydoc = ydoc
            state.wsProvider = wsProvider
            state.webrtcProvider = webrtcProvider
            state.roomId = roomId
            state.currentUser = currentUser
            state.isConnected = true
          })

          // Set initial user presence
          wsProvider.awareness.setLocalStateField('user', currentUser)

        } catch (error) {
          console.error('Failed to connect to collaboration room:', error)
          set((state) => {
            state.isConnected = false
            state.connectionQuality = 'disconnected'
          })
        }
      },

      // Disconnect from collaboration
      disconnect: () => {
        const state = get()
        
        if (state.wsProvider) {
          state.wsProvider.destroy()
        }
        
        if (state.webrtcProvider) {
          state.webrtcProvider.destroy()
        }
        
        if (state.ydoc) {
          state.ydoc.destroy()
        }

        set((state) => {
          state.isConnected = false
          state.roomId = null
          state.ydoc = null
          state.wsProvider = null
          state.webrtcProvider = null
          state.collaborators = []
          state.currentUser = null
          state.awareness.clear()
          state.connectionQuality = 'disconnected'
        })
      },

      // Send operation (handled automatically by Yjs)
      sendOperation: (operation: any) => {
        // Operations are automatically synchronized by Yjs
        // This is here for compatibility and potential custom operations
      },

      // Send cursor position
      sendCursor: (cursor: { selection: SelectionRange | null; timestamp: number }) => {
        const state = get()
        
        if (state.wsProvider && state.isConnected) {
          state.wsProvider.awareness.setLocalStateField('cursor', cursor.selection)
          state.wsProvider.awareness.setLocalStateField('selection', cursor.selection)
        }
      },

      // Update user presence
      updateUserPresence: (presence: Partial<Collaborator>) => {
        const state = get()
        
        if (state.wsProvider && state.currentUser) {
          const updatedUser = {
            ...state.currentUser,
            ...presence,
            lastSeen: new Date().toISOString(),
          }
          
          state.wsProvider.awareness.setLocalStateField('user', updatedUser)
          
          set((state) => {
            state.currentUser = updatedUser
          })
        }
      },

      // Bind editor to Yjs
      bindEditor: (editor: Editor): YjsEditor => {
        const state = get()
        
        if (!state.ydoc) {
          throw new Error('Must connect to collaboration room before binding editor')
        }

        // Get or create shared type for the document
        const sharedType = state.ydoc.get('content', Y.XmlText) as Y.XmlText
        
        // Apply Yjs plugin to editor
        const yjsEditor = withYjs(editor, sharedType, {
          autoConnect: true,
        })

        return yjsEditor
      },

      // Unbind editor
      unbindEditor: () => {
        // Cleanup is handled by Yjs automatically
      },
    }))
  )
)

// Utility function to generate user colors
export const generateUserColor = (userId: string): string => {
  const colors = [
    '#ef4444', // red
    '#22c55e', // green
    '#f59e0b', // amber
    '#a855f7', // purple
    '#ec4899', // pink
    '#06b6d4', // cyan
    '#84cc16', // lime
    '#f97316', // orange
  ]
  
  // Generate consistent color based on user ID
  let hash = 0
  for (let i = 0; i < userId.length; i++) {
    hash = ((hash << 5) - hash + userId.charCodeAt(i)) & 0xffffffff
  }
  
  return colors[Math.abs(hash) % colors.length]
}

// Hook for easier access to collaboration features
export const useCollaboration = (roomId?: string) => {
  const store = useCollaborationStore()

  // Note: React import would be needed here in actual implementation
  // React.useEffect(() => {
    // Auto-connect if roomId is provided and not already connected
    if (roomId && !store.isConnected && !store.roomId) {
      // This would typically be called with user info from auth
      // store.connect(roomId, userInfo)
    }
    
    // Cleanup on unmount
    return () => {
      if (store.isConnected) {
        store.disconnect()
      }
    }
  }, [roomId, store])
  
  return store
}
