import { PDFDocument, rgb, StandardFonts } from 'pdf-lib'
import puppeteer from 'puppeteer'
import { ExportOptions, ExportResult } from '@/types/resume'
import { TemplateRenderer } from '@/templates/renderer'

interface PDFExportOptions extends ExportOptions {
  pageSize: 'A4' | 'Letter' | 'Legal'
  orientation: 'portrait' | 'landscape'
  margins: {
    top: number
    right: number
    bottom: number
    left: number
  }
  scale: number
  printBackground: boolean
  preferCSSPageSize: boolean
}

export class PDFExporter {
  private static instance: PDFExporter
  private browser: puppeteer.Browser | null = null

  private constructor() {}

  static getInstance(): PDFExporter {
    if (!PDFExporter.instance) {
      PDFExporter.instance = new PDFExporter()
    }
    return PDFExporter.instance
  }

  // Initialize browser for PDF generation
  async initialize(): Promise<void> {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
        ],
      })
    }
  }

  // Export resume to PDF using headless Chrome
  async exportToPDF(
    html: string,
    options: PDFExportOptions
  ): Promise<ExportResult> {
    try {
      await this.initialize()

      if (!this.browser) {
        throw new Error('Failed to initialize browser')
      }

      const page = await this.browser.newPage()

      // Set viewport for consistent rendering
      await page.setViewport({
        width: this.getPageWidth(options.pageSize, options.orientation),
        height: this.getPageHeight(options.pageSize, options.orientation),
        deviceScaleFactor: options.scale || 1,
      })

      // Set content and wait for fonts to load
      await page.setContent(html, {
        waitUntil: ['networkidle0', 'domcontentloaded'],
      })

      // Wait for custom fonts to load
      await page.evaluateHandle('document.fonts.ready')

      // Add print media styles
      await page.emulateMediaType('print')

      // Generate PDF
      const pdfBuffer = await page.pdf({
        format: options.pageSize.toLowerCase() as any,
        landscape: options.orientation === 'landscape',
        printBackground: options.printBackground,
        preferCSSPageSize: options.preferCSSPageSize,
        margin: {
          top: `${options.margins.top}in`,
          right: `${options.margins.right}in`,
          bottom: `${options.margins.bottom}in`,
          left: `${options.margins.left}in`,
        },
        displayHeaderFooter: false,
        scale: options.scale || 1,
      })

      await page.close()

      // Post-process PDF if needed
      const processedPDF = await this.postProcessPDF(pdfBuffer, options)

      // Generate result
      const result: ExportResult = {
        success: true,
        url: await this.savePDF(processedPDF),
        metadata: {
          format: 'pdf',
          size: processedPDF.length,
          pages: await this.getPDFPageCount(processedPDF),
          generatedAt: new Date().toISOString(),
        },
      }

      return result
    } catch (error) {
      console.error('PDF export failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          format: 'pdf',
          size: 0,
          pages: 0,
          generatedAt: new Date().toISOString(),
        },
      }
    }
  }

  // Post-process PDF for optimization and metadata
  private async postProcessPDF(
    pdfBuffer: Buffer,
    options: PDFExportOptions
  ): Promise<Buffer> {
    const pdfDoc = await PDFDocument.load(pdfBuffer)

    // Add metadata
    pdfDoc.setTitle(options.title || 'Resume')
    pdfDoc.setAuthor(options.author || 'CVLeap Resume Editor')
    pdfDoc.setSubject('Professional Resume')
    pdfDoc.setKeywords(['resume', 'cv', 'professional', 'cvleap'])
    pdfDoc.setProducer('CVLeap Resume Editor')
    pdfDoc.setCreator('CVLeap Resume Editor')
    pdfDoc.setCreationDate(new Date())
    pdfDoc.setModificationDate(new Date())

    // Add watermark if specified
    if (options.watermark) {
      await this.addWatermark(pdfDoc, options.watermark)
    }

    // Optimize for file size based on quality
    const optimizedPDF = await this.optimizePDF(pdfDoc, options.quality)

    // Add password protection if specified
    if (options.password) {
      return await this.addPasswordProtection(optimizedPDF, options.password)
    }

    return optimizedPDF
  }

  // Add watermark to PDF
  private async addWatermark(pdfDoc: PDFDocument, watermarkText: string): Promise<void> {
    const pages = pdfDoc.getPages()
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica)

    for (const page of pages) {
      const { width, height } = page.getSize()
      
      page.drawText(watermarkText, {
        x: width / 2 - (watermarkText.length * 8) / 2,
        y: height / 2,
        size: 48,
        font,
        color: rgb(0.9, 0.9, 0.9),
        opacity: 0.3,
        rotate: { angle: -45, origin: { x: width / 2, y: height / 2 } },
      })
    }
  }

  // Optimize PDF based on quality setting
  private async optimizePDF(pdfDoc: PDFDocument, quality: string): Promise<Buffer> {
    // Quality-based optimization settings
    const optimizationSettings = {
      draft: {
        compressStreams: true,
        removeUnusedObjects: true,
        imageQuality: 0.5,
      },
      standard: {
        compressStreams: true,
        removeUnusedObjects: true,
        imageQuality: 0.8,
      },
      high: {
        compressStreams: false,
        removeUnusedObjects: false,
        imageQuality: 1.0,
      },
    }

    const settings = optimizationSettings[quality as keyof typeof optimizationSettings] || optimizationSettings.standard

    // Apply optimization settings
    return await pdfDoc.save({
      useObjectStreams: settings.compressStreams,
      addDefaultPage: false,
    })
  }

  // Add password protection to PDF
  private async addPasswordProtection(pdfBuffer: Buffer, password: string): Promise<Buffer> {
    // Note: pdf-lib doesn't support encryption directly
    // In production, you'd use a library like HummusJS or call an external service
    console.warn('Password protection not implemented in this demo')
    return pdfBuffer
  }

  // Get PDF page count
  private async getPDFPageCount(pdfBuffer: Buffer): Promise<number> {
    try {
      const pdfDoc = await PDFDocument.load(pdfBuffer)
      return pdfDoc.getPageCount()
    } catch {
      return 1
    }
  }

  // Save PDF and return URL
  private async savePDF(pdfBuffer: Buffer): Promise<string> {
    // In production, save to cloud storage (S3, GCS, etc.)
    // For demo, create blob URL
    const blob = new Blob([pdfBuffer], { type: 'application/pdf' })
    return URL.createObjectURL(blob)
  }

  // Get page dimensions
  private getPageWidth(pageSize: string, orientation: string): number {
    const dimensions = {
      A4: { width: 595, height: 842 },
      Letter: { width: 612, height: 792 },
      Legal: { width: 612, height: 1008 },
    }

    const size = dimensions[pageSize as keyof typeof dimensions] || dimensions.A4
    return orientation === 'landscape' ? size.height : size.width
  }

  private getPageHeight(pageSize: string, orientation: string): number {
    const dimensions = {
      A4: { width: 595, height: 842 },
      Letter: { width: 612, height: 792 },
      Legal: { width: 612, height: 1008 },
    }

    const size = dimensions[pageSize as keyof typeof dimensions] || dimensions.A4
    return orientation === 'landscape' ? size.width : size.height
  }

  // Cleanup browser resources
  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close()
      this.browser = null
    }
  }
}

// Export utility functions
export const exportResumeAsPDF = async (
  renderer: TemplateRenderer,
  options: Partial<PDFExportOptions> = {}
): Promise<ExportResult> => {
  const defaultOptions: PDFExportOptions = {
    format: 'pdf',
    quality: 'standard',
    includeMetadata: true,
    pageSize: 'A4',
    orientation: 'portrait',
    margins: {
      top: 0.75,
      right: 0.75,
      bottom: 0.75,
      left: 0.75,
    },
    scale: 1,
    printBackground: true,
    preferCSSPageSize: true,
    ...options,
  }

  const exporter = PDFExporter.getInstance()
  const html = await renderer.render()
  
  return await exporter.exportToPDF(html, defaultOptions)
}

// PDF/A-1b compliance utility
export const generatePDFA1b = async (
  renderer: TemplateRenderer,
  options: Partial<PDFExportOptions> = {}
): Promise<ExportResult> => {
  // PDF/A-1b specific requirements
  const pdfAOptions: PDFExportOptions = {
    ...options,
    format: 'pdf',
    quality: 'high',
    includeMetadata: true,
    pageSize: options.pageSize || 'A4',
    orientation: options.orientation || 'portrait',
    margins: options.margins || {
      top: 0.75,
      right: 0.75,
      bottom: 0.75,
      left: 0.75,
    },
    scale: 1,
    printBackground: true,
    preferCSSPageSize: true,
  }

  const exporter = PDFExporter.getInstance()
  const html = await renderer.render()
  
  // Add PDF/A-1b specific metadata and structure
  const htmlWithPDFA = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="DC.title" content="${options.title || 'Resume'}">
      <meta name="DC.creator" content="${options.author || 'CVLeap Resume Editor'}">
      <meta name="DC.subject" content="Professional Resume">
      <meta name="DC.description" content="Professional resume generated by CVLeap">
      <meta name="DC.format" content="application/pdf">
      <meta name="DC.language" content="en">
      <meta name="DC.date" content="${new Date().toISOString()}">
      <title>${options.title || 'Resume'}</title>
      <style>
        /* PDF/A-1b compliant styles */
        @media print {
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
          }
        }
      </style>
    </head>
    <body>
      ${html}
    </body>
    </html>
  `
  
  return await exporter.exportToPDF(htmlWithPDFA, pdfAOptions)
}

// Batch export utility
export const batchExportPDF = async (
  renderers: TemplateRenderer[],
  options: Partial<PDFExportOptions> = {}
): Promise<ExportResult[]> => {
  const exporter = PDFExporter.getInstance()
  const results: ExportResult[] = []

  for (const renderer of renderers) {
    try {
      const result = await exportResumeAsPDF(renderer, options)
      results.push(result)
    } catch (error) {
      results.push({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          format: 'pdf',
          size: 0,
          pages: 0,
          generatedAt: new Date().toISOString(),
        },
      })
    }
  }

  return results
}
