/**
 * Yjs CRDT Provider for Real-time Collaboration
 * WebRTC + WebSocket fallback with multi-cursor support
 */

import * as Y from 'yjs'
import { WebrtcProvider } from 'y-webrtc'
import { WebsocketProvider } from 'y-websocket'
import { IndexeddbPersistence } from 'y-indexeddb'
import { Awareness } from 'y-protocols/awareness'
import { Descendant, Editor, Operation, Path, Point, Range } from 'slate'
import { ReactEditor } from 'slate-react'
import { withYjs, YjsEditor } from '@slate-yjs/core'

export interface CollaborationUser {
  id: string
  name: string
  email: string
  avatar: string
  color: string
  role: 'owner' | 'editor' | 'viewer'
  isOnline: boolean
  lastSeen: string
  cursor?: {
    path: Path
    offset: number
  }
}

export interface CollaborationConfig {
  roomId: string
  userId: string
  user: Omit<CollaborationUser, 'id' | 'isOnline' | 'lastSeen'>
  websocketUrl?: string
  signalingUrls?: string[]
  password?: string
  maxConns?: number
  filterBcConns?: boolean
}

export class YjsCollaborationProvider {
  private doc: Y.Doc
  private awareness: Awareness
  private webrtcProvider: WebrtcProvider | null = null
  private websocketProvider: WebsocketProvider | null = null
  private indexeddbProvider: IndexeddbPersistence | null = null
  private sharedType: Y.XmlText
  private config: CollaborationConfig
  private isConnected = false
  private connectionListeners: Set<(connected: boolean) => void> = new Set()
  private userListeners: Set<(users: CollaborationUser[]) => void> = new Set()
  private cursorListeners: Set<(cursors: Map<string, CollaborationUser>) => void> = new Set()

  constructor(config: CollaborationConfig) {
    this.config = config
    this.doc = new Y.Doc()
    this.awareness = new Awareness(this.doc)
    this.sharedType = this.doc.getXmlFragment('slate-content')
    
    this.setupAwareness()
    this.setupProviders()
    this.setupPersistence()
  }

  private setupAwareness() {
    // Set local user state
    this.awareness.setLocalStateField('user', {
      id: this.config.userId,
      name: this.config.user.name,
      email: this.config.user.email,
      avatar: this.config.user.avatar,
      color: this.config.user.color,
      role: this.config.user.role,
      isOnline: true,
      lastSeen: new Date().toISOString()
    })

    // Listen for awareness changes
    this.awareness.on('change', this.handleAwarenessChange.bind(this))
  }

  private setupProviders() {
    // Setup WebRTC provider for P2P communication
    this.webrtcProvider = new WebrtcProvider(
      this.config.roomId,
      this.doc,
      {
        signaling: this.config.signalingUrls || ['wss://signaling.yjs.dev'],
        password: this.config.password,
        awareness: this.awareness,
        maxConns: this.config.maxConns || 20,
        filterBcConns: this.config.filterBcConns ?? true,
        peerOpts: {
          config: {
            iceServers: [
              { urls: 'stun:stun.l.google.com:19302' },
              { urls: 'stun:global.stun.twilio.com:3478' }
            ]
          }
        }
      }
    )

    // Setup WebSocket provider as fallback
    if (this.config.websocketUrl) {
      this.websocketProvider = new WebsocketProvider(
        this.config.websocketUrl,
        this.config.roomId,
        this.doc,
        {
          awareness: this.awareness,
          params: {
            userId: this.config.userId,
            token: localStorage.getItem('auth_token') || ''
          }
        }
      )

      this.websocketProvider.on('status', (event: { status: string }) => {
        this.isConnected = event.status === 'connected'
        this.notifyConnectionListeners()
      })
    }

    // Setup connection monitoring
    this.webrtcProvider.on('status', (event: { status: string }) => {
      if (!this.websocketProvider || this.websocketProvider.wsconnected === false) {
        this.isConnected = event.status === 'connected'
        this.notifyConnectionListeners()
      }
    })
  }

  private setupPersistence() {
    // Setup IndexedDB persistence for offline support
    this.indexeddbProvider = new IndexeddbPersistence(
      `resume-${this.config.roomId}`,
      this.doc
    )

    this.indexeddbProvider.on('synced', () => {
      console.log('📦 Document synced with IndexedDB')
    })
  }

  private handleAwarenessChange() {
    const users = this.getCollaborators()
    const cursors = this.getCursors()
    
    this.notifyUserListeners(users)
    this.notifyCursorListeners(cursors)
  }

  private notifyConnectionListeners() {
    this.connectionListeners.forEach(listener => listener(this.isConnected))
  }

  private notifyUserListeners(users: CollaborationUser[]) {
    this.userListeners.forEach(listener => listener(users))
  }

  private notifyCursorListeners(cursors: Map<string, CollaborationUser>) {
    this.cursorListeners.forEach(listener => listener(cursors))
  }

  // Public API
  getSharedType(): Y.XmlText {
    return this.sharedType
  }

  getDoc(): Y.Doc {
    return this.doc
  }

  getAwareness(): Awareness {
    return this.awareness
  }

  isConnectedToRoom(): boolean {
    return this.isConnected
  }

  getCollaborators(): CollaborationUser[] {
    const states = this.awareness.getStates()
    const users: CollaborationUser[] = []

    states.forEach((state, clientId) => {
      if (state.user && clientId !== this.awareness.clientID) {
        users.push({
          ...state.user,
          isOnline: true,
          lastSeen: new Date().toISOString()
        })
      }
    })

    return users
  }

  getCursors(): Map<string, CollaborationUser> {
    const cursors = new Map<string, CollaborationUser>()
    const states = this.awareness.getStates()

    states.forEach((state, clientId) => {
      if (state.user && state.cursor && clientId !== this.awareness.clientID) {
        cursors.set(state.user.id, {
          ...state.user,
          cursor: state.cursor,
          isOnline: true,
          lastSeen: new Date().toISOString()
        })
      }
    })

    return cursors
  }

  updateCursor(editor: Editor) {
    if (!ReactEditor.isFocused(editor) || !editor.selection) {
      // Clear cursor when editor loses focus
      this.awareness.setLocalStateField('cursor', null)
      return
    }

    const { selection } = editor
    if (Range.isCollapsed(selection)) {
      // Single cursor
      this.awareness.setLocalStateField('cursor', {
        path: selection.anchor.path,
        offset: selection.anchor.offset
      })
    } else {
      // Selection range
      this.awareness.setLocalStateField('cursor', {
        anchor: selection.anchor,
        focus: selection.focus
      })
    }
  }

  updateUserPresence(updates: Partial<CollaborationUser>) {
    const currentUser = this.awareness.getLocalState()?.user
    if (currentUser) {
      this.awareness.setLocalStateField('user', {
        ...currentUser,
        ...updates,
        lastSeen: new Date().toISOString()
      })
    }
  }

  // Event listeners
  onConnectionChange(listener: (connected: boolean) => void): () => void {
    this.connectionListeners.add(listener)
    return () => this.connectionListeners.delete(listener)
  }

  onUsersChange(listener: (users: CollaborationUser[]) => void): () => void {
    this.userListeners.add(listener)
    return () => this.userListeners.delete(listener)
  }

  onCursorsChange(listener: (cursors: Map<string, CollaborationUser>) => void): () => void {
    this.cursorListeners.add(listener)
    return () => this.cursorListeners.delete(listener)
  }

  // Cleanup
  destroy() {
    this.awareness.destroy()
    this.webrtcProvider?.destroy()
    this.websocketProvider?.destroy()
    this.indexeddbProvider?.destroy()
    this.doc.destroy()
    
    this.connectionListeners.clear()
    this.userListeners.clear()
    this.cursorListeners.clear()
  }
}

// Slate.js integration
export function withCollaboration(editor: Editor, provider: YjsCollaborationProvider): Editor {
  const sharedType = provider.getSharedType()
  const collaborativeEditor = withYjs(editor, sharedType)

  const { onChange } = collaborativeEditor

  collaborativeEditor.onChange = () => {
    // Update cursor position for other users
    provider.updateCursor(collaborativeEditor)
    
    // Call original onChange
    onChange()
  }

  return collaborativeEditor
}

// React hook for collaboration
export function useCollaboration(config: CollaborationConfig) {
  const [provider, setProvider] = React.useState<YjsCollaborationProvider | null>(null)
  const [isConnected, setIsConnected] = React.useState(false)
  const [collaborators, setCollaborators] = React.useState<CollaborationUser[]>([])
  const [cursors, setCursors] = React.useState<Map<string, CollaborationUser>>(new Map())

  React.useEffect(() => {
    const newProvider = new YjsCollaborationProvider(config)
    setProvider(newProvider)

    // Setup event listeners
    const unsubscribeConnection = newProvider.onConnectionChange(setIsConnected)
    const unsubscribeUsers = newProvider.onUsersChange(setCollaborators)
    const unsubscribeCursors = newProvider.onCursorsChange(setCursors)

    return () => {
      unsubscribeConnection()
      unsubscribeUsers()
      unsubscribeCursors()
      newProvider.destroy()
    }
  }, [config.roomId, config.userId])

  const updatePresence = React.useCallback((updates: Partial<CollaborationUser>) => {
    provider?.updateUserPresence(updates)
  }, [provider])

  return {
    provider,
    isConnected,
    collaborators,
    cursors,
    updatePresence
  }
}

// Cursor rendering component
export interface CollaborationCursorProps {
  user: CollaborationUser
  children: React.ReactNode
}

export function CollaborationCursor({ user, children }: CollaborationCursorProps) {
  return (
    <span
      style={{
        position: 'relative',
        backgroundColor: user.color + '20',
        borderLeft: `2px solid ${user.color}`,
      }}
      data-user-id={user.id}
    >
      {children}
      <span
        style={{
          position: 'absolute',
          top: '-20px',
          left: '-2px',
          backgroundColor: user.color,
          color: 'white',
          padding: '2px 6px',
          borderRadius: '3px',
          fontSize: '12px',
          fontWeight: 'bold',
          whiteSpace: 'nowrap',
          zIndex: 1000,
          pointerEvents: 'none'
        }}
      >
        {user.name}
      </span>
    </span>
  )
}

// User avatar component
export interface UserAvatarProps {
  user: CollaborationUser
  size?: 'sm' | 'md' | 'lg'
  showStatus?: boolean
}

export function UserAvatar({ user, size = 'md', showStatus = true }: UserAvatarProps) {
  const sizeClasses = {
    sm: 'w-6 h-6 text-xs',
    md: 'w-8 h-8 text-sm',
    lg: 'w-10 h-10 text-base'
  }

  return (
    <div className="relative">
      {user.avatar ? (
        <img
          src={user.avatar}
          alt={user.name}
          className={`${sizeClasses[size]} rounded-full border-2`}
          style={{ borderColor: user.color }}
        />
      ) : (
        <div
          className={`${sizeClasses[size]} rounded-full flex items-center justify-center text-white font-semibold`}
          style={{ backgroundColor: user.color }}
        >
          {user.name.charAt(0).toUpperCase()}
        </div>
      )}
      
      {showStatus && (
        <div
          className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
            user.isOnline ? 'bg-green-500' : 'bg-gray-400'
          }`}
        />
      )}
    </div>
  )
}

// Collaboration status indicator
export interface CollaborationStatusProps {
  isConnected: boolean
  collaborators: CollaborationUser[]
}

export function CollaborationStatus({ isConnected, collaborators }: CollaborationStatusProps) {
  return (
    <div className="flex items-center space-x-2">
      <div className="flex items-center space-x-1">
        <div
          className={`w-2 h-2 rounded-full ${
            isConnected ? 'bg-green-500' : 'bg-red-500'
          }`}
        />
        <span className="text-sm text-gray-600">
          {isConnected ? 'Connected' : 'Reconnecting...'}
        </span>
      </div>
      
      {collaborators.length > 0 && (
        <div className="flex items-center space-x-1">
          <div className="flex -space-x-1">
            {collaborators.slice(0, 3).map(user => (
              <UserAvatar
                key={user.id}
                user={user}
                size="sm"
                showStatus={false}
              />
            ))}
          </div>
          {collaborators.length > 3 && (
            <span className="text-xs text-gray-500">
              +{collaborators.length - 3} more
            </span>
          )}
        </div>
      )}
    </div>
  )
}
