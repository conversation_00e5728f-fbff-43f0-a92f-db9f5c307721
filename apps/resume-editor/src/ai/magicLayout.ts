import { Descendant } from 'slate'
import { ResumeDocument, LayoutSuggestion, LayoutChange, CustomElement } from '@/types/resume'

interface LayoutAnalysis {
  contentDensity: number
  sectionBalance: number
  visualHierarchy: number
  atsCompatibility: number
  readabilityScore: number
  overallScore: number
}

interface ATSHeuristics {
  hasContactInfo: boolean
  hasWorkExperience: boolean
  hasEducation: boolean
  hasSkills: boolean
  usesStandardSectionNames: boolean
  hasConsistentDateFormats: boolean
  avoidsTables: boolean
  usesStandardFonts: boolean
  hasProperHeadings: boolean
  minimizesGraphics: boolean
}

export class MagicLayoutEngine {
  private static instance: MagicLayoutEngine
  private modelEndpoint: string
  private apiKey: string

  private constructor() {
    this.modelEndpoint = process.env.VITE_AI_MODEL_ENDPOINT || 'http://localhost:8080/v1'
    this.apiKey = process.env.VITE_AI_API_KEY || ''
  }

  static getInstance(): MagicLayoutEngine {
    if (!MagicLayoutEngine.instance) {
      MagicLayoutEngine.instance = new MagicLayoutEngine()
    }
    return MagicLayoutEngine.instance
  }

  // Main entry point for layout optimization
  async optimizeLayout(document: ResumeDocument): Promise<LayoutSuggestion[]> {
    try {
      // Analyze current layout
      const analysis = await this.analyzeLayout(document)
      
      // Generate suggestions based on analysis
      const suggestions = await this.generateSuggestions(document, analysis)
      
      // Rank suggestions by impact and confidence
      return this.rankSuggestions(suggestions)
    } catch (error) {
      console.error('Magic Layout optimization failed:', error)
      return []
    }
  }

  // Analyze current layout and content
  private async analyzeLayout(document: ResumeDocument): Promise<LayoutAnalysis> {
    const content = document.content
    const sections = this.extractSections(content)
    
    // Calculate various metrics
    const contentDensity = this.calculateContentDensity(sections)
    const sectionBalance = this.calculateSectionBalance(sections)
    const visualHierarchy = this.calculateVisualHierarchy(content)
    const atsCompatibility = this.calculateATSCompatibility(document)
    const readabilityScore = this.calculateReadabilityScore(content)
    
    const overallScore = (
      contentDensity * 0.2 +
      sectionBalance * 0.25 +
      visualHierarchy * 0.2 +
      atsCompatibility * 0.25 +
      readabilityScore * 0.1
    )

    return {
      contentDensity,
      sectionBalance,
      visualHierarchy,
      atsCompatibility,
      readabilityScore,
      overallScore,
    }
  }

  // Extract sections from document content
  private extractSections(content: Descendant[]): CustomElement[] {
    const sections: CustomElement[] = []
    
    const traverse = (nodes: Descendant[]) => {
      for (const node of nodes) {
        if ('type' in node && node.type === 'section') {
          sections.push(node as CustomElement)
        }
        if ('children' in node && Array.isArray(node.children)) {
          traverse(node.children)
        }
      }
    }
    
    traverse(content)
    return sections
  }

  // Calculate content density (words per section)
  private calculateContentDensity(sections: CustomElement[]): number {
    if (sections.length === 0) return 0
    
    const totalWords = sections.reduce((total, section) => {
      const wordCount = this.countWords(section)
      return total + wordCount
    }, 0)
    
    const averageWordsPerSection = totalWords / sections.length
    
    // Optimal range: 50-150 words per section
    const optimal = 100
    const deviation = Math.abs(averageWordsPerSection - optimal) / optimal
    
    return Math.max(0, 1 - deviation)
  }

  // Calculate section balance (even distribution of content)
  private calculateSectionBalance(sections: CustomElement[]): number {
    if (sections.length === 0) return 0
    
    const wordCounts = sections.map(section => this.countWords(section))
    const average = wordCounts.reduce((sum, count) => sum + count, 0) / wordCounts.length
    
    if (average === 0) return 0
    
    const variance = wordCounts.reduce((sum, count) => {
      return sum + Math.pow(count - average, 2)
    }, 0) / wordCounts.length
    
    const standardDeviation = Math.sqrt(variance)
    const coefficientOfVariation = standardDeviation / average
    
    // Lower variation is better
    return Math.max(0, 1 - coefficientOfVariation)
  }

  // Calculate visual hierarchy score
  private calculateVisualHierarchy(content: Descendant[]): number {
    let score = 0
    let hasH1 = false
    let hasH2 = false
    let properHeadingOrder = true
    let lastHeadingLevel = 0
    
    const traverse = (nodes: Descendant[]) => {
      for (const node of nodes) {
        if ('type' in node && node.type === 'heading') {
          const heading = node as any
          const level = heading.level || 1
          
          if (level === 1) hasH1 = true
          if (level === 2) hasH2 = true
          
          // Check heading order
          if (lastHeadingLevel > 0 && level > lastHeadingLevel + 1) {
            properHeadingOrder = false
          }
          lastHeadingLevel = level
        }
        
        if ('children' in node && Array.isArray(node.children)) {
          traverse(node.children)
        }
      }
    }
    
    traverse(content)
    
    if (hasH1) score += 0.4
    if (hasH2) score += 0.3
    if (properHeadingOrder) score += 0.3
    
    return score
  }

  // Calculate ATS compatibility score
  private calculateATSCompatibility(document: ResumeDocument): number {
    const heuristics = this.evaluateATSHeuristics(document)
    
    const weights = {
      hasContactInfo: 0.15,
      hasWorkExperience: 0.15,
      hasEducation: 0.1,
      hasSkills: 0.1,
      usesStandardSectionNames: 0.15,
      hasConsistentDateFormats: 0.1,
      avoidsTables: 0.1,
      usesStandardFonts: 0.05,
      hasProperHeadings: 0.05,
      minimizesGraphics: 0.05,
    }
    
    let score = 0
    for (const [key, value] of Object.entries(heuristics)) {
      if (value) {
        score += weights[key as keyof typeof weights] || 0
      }
    }
    
    return score
  }

  // Evaluate ATS-specific heuristics
  private evaluateATSHeuristics(document: ResumeDocument): ATSHeuristics {
    const content = document.content
    const sections = this.extractSections(content)
    
    return {
      hasContactInfo: this.hasSection(sections, 'contact'),
      hasWorkExperience: this.hasSection(sections, 'experience'),
      hasEducation: this.hasSection(sections, 'education'),
      hasSkills: this.hasSection(sections, 'skills'),
      usesStandardSectionNames: this.usesStandardSectionNames(sections),
      hasConsistentDateFormats: this.hasConsistentDateFormats(content),
      avoidsTables: this.avoidsTables(content),
      usesStandardFonts: this.usesStandardFonts(document),
      hasProperHeadings: this.hasProperHeadings(content),
      minimizesGraphics: this.minimizesGraphics(content),
    }
  }

  // Calculate readability score
  private calculateReadabilityScore(content: Descendant[]): number {
    const text = this.extractText(content)
    
    if (text.length === 0) return 0
    
    // Simple readability metrics
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const words = text.split(/\s+/).filter(w => w.length > 0)
    const syllables = words.reduce((total, word) => total + this.countSyllables(word), 0)
    
    if (sentences.length === 0 || words.length === 0) return 0
    
    // Flesch Reading Ease Score
    const avgSentenceLength = words.length / sentences.length
    const avgSyllablesPerWord = syllables / words.length
    
    const fleschScore = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord)
    
    // Normalize to 0-1 range (60-100 is good for professional documents)
    return Math.max(0, Math.min(1, (fleschScore - 30) / 70))
  }

  // Generate layout suggestions using AI
  private async generateSuggestions(
    document: ResumeDocument,
    analysis: LayoutAnalysis
  ): Promise<LayoutSuggestion[]> {
    const suggestions: LayoutSuggestion[] = []
    
    // Rule-based suggestions (fallback if AI is unavailable)
    suggestions.push(...this.generateRuleBasedSuggestions(document, analysis))
    
    // AI-powered suggestions
    try {
      const aiSuggestions = await this.generateAISuggestions(document, analysis)
      suggestions.push(...aiSuggestions)
    } catch (error) {
      console.warn('AI suggestions unavailable, using rule-based fallback:', error)
    }
    
    return suggestions
  }

  // Generate rule-based suggestions
  private generateRuleBasedSuggestions(
    document: ResumeDocument,
    analysis: LayoutAnalysis
  ): LayoutSuggestion[] {
    const suggestions: LayoutSuggestion[] = []
    const sections = this.extractSections(document.content)
    
    // Suggest reordering if experience section is not prominent
    const experienceSection = sections.find(s => (s as any).type === 'experience')
    if (experienceSection && sections.indexOf(experienceSection) > 2) {
      suggestions.push({
        id: 'reorder-experience',
        type: 'reorder',
        title: 'Move Experience Section Up',
        description: 'Place work experience near the top for better ATS parsing and recruiter attention.',
        confidence: 0.8,
        changes: [{
          path: [sections.indexOf(experienceSection)],
          type: 'move',
          before: experienceSection,
          after: { ...experienceSection, order: 1 },
          reason: 'Experience should be prominently placed for ATS optimization',
        }],
      })
    }
    
    // Suggest skills section optimization
    const skillsSection = sections.find(s => (s as any).type === 'skills')
    if (skillsSection) {
      const skillsWordCount = this.countWords(skillsSection)
      if (skillsWordCount < 20) {
        suggestions.push({
          id: 'expand-skills',
          type: 'resize',
          title: 'Expand Skills Section',
          description: 'Add more relevant skills to improve keyword matching and ATS compatibility.',
          confidence: 0.7,
          changes: [{
            path: [sections.indexOf(skillsSection)],
            type: 'content',
            before: skillsSection,
            after: { ...skillsSection, expanded: true },
            reason: 'More comprehensive skills section improves ATS keyword matching',
          }],
        }],
      }
    }
    
    // Suggest contact info optimization
    if (analysis.atsCompatibility < 0.8) {
      suggestions.push({
        id: 'optimize-contact',
        type: 'reformat',
        title: 'Optimize Contact Information',
        description: 'Ensure contact information is ATS-friendly and properly formatted.',
        confidence: 0.9,
        changes: [{
          path: [0],
          type: 'style',
          before: {},
          after: { atsOptimized: true },
          reason: 'ATS-optimized contact format improves parsing accuracy',
        }],
      })
    }
    
    return suggestions
  }

  // Generate AI-powered suggestions (placeholder for actual AI integration)
  private async generateAISuggestions(
    document: ResumeDocument,
    analysis: LayoutAnalysis
  ): Promise<LayoutSuggestion[]> {
    // In production, this would call a small language model
    // For now, return empty array as placeholder
    return []
  }

  // Rank suggestions by impact and confidence
  private rankSuggestions(suggestions: LayoutSuggestion[]): LayoutSuggestion[] {
    return suggestions.sort((a, b) => {
      const scoreA = a.confidence * this.getImpactWeight(a.type)
      const scoreB = b.confidence * this.getImpactWeight(b.type)
      return scoreB - scoreA
    })
  }

  // Get impact weight for suggestion type
  private getImpactWeight(type: string): number {
    const weights = {
      reorder: 0.8,
      resize: 0.6,
      reformat: 0.7,
      optimize: 0.9,
    }
    return weights[type as keyof typeof weights] || 0.5
  }

  // Helper methods
  private countWords(element: CustomElement): number {
    const text = this.extractText([element])
    return text.split(/\s+/).filter(word => word.length > 0).length
  }

  private extractText(nodes: Descendant[]): string {
    let text = ''
    
    const traverse = (nodes: Descendant[]) => {
      for (const node of nodes) {
        if ('text' in node) {
          text += node.text + ' '
        }
        if ('children' in node && Array.isArray(node.children)) {
          traverse(node.children)
        }
      }
    }
    
    traverse(nodes)
    return text.trim()
  }

  private countSyllables(word: string): number {
    word = word.toLowerCase()
    if (word.length <= 3) return 1
    
    const vowels = 'aeiouy'
    let syllableCount = 0
    let previousWasVowel = false
    
    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i])
      if (isVowel && !previousWasVowel) {
        syllableCount++
      }
      previousWasVowel = isVowel
    }
    
    // Handle silent 'e'
    if (word.endsWith('e')) {
      syllableCount--
    }
    
    return Math.max(1, syllableCount)
  }

  private hasSection(sections: CustomElement[], type: string): boolean {
    return sections.some(section => (section as any).type === type)
  }

  private usesStandardSectionNames(sections: CustomElement[]): boolean {
    const standardNames = [
      'experience', 'education', 'skills', 'contact',
      'summary', 'projects', 'certifications'
    ]
    
    const sectionTypes = sections.map(s => (s as any).type)
    const standardCount = sectionTypes.filter(type => standardNames.includes(type)).length
    
    return standardCount / sectionTypes.length > 0.8
  }

  private hasConsistentDateFormats(content: Descendant[]): boolean {
    // Simplified check - in production, would use regex to validate date formats
    return true
  }

  private avoidsTables(content: Descendant[]): boolean {
    // Check for table elements
    const hasTable = this.findElementType(content, 'table')
    return !hasTable
  }

  private usesStandardFonts(document: ResumeDocument): boolean {
    const standardFonts = ['Arial', 'Times New Roman', 'Calibri', 'Helvetica', 'Georgia']
    const primaryFont = document.metadata.fonts.primary
    return standardFonts.some(font => primaryFont.includes(font))
  }

  private hasProperHeadings(content: Descendant[]): boolean {
    return this.findElementType(content, 'heading')
  }

  private minimizesGraphics(content: Descendant[]): boolean {
    // Check for image or graphic elements
    const hasGraphics = this.findElementType(content, 'image')
    return !hasGraphics
  }

  private findElementType(nodes: Descendant[], type: string): boolean {
    for (const node of nodes) {
      if ('type' in node && node.type === type) {
        return true
      }
      if ('children' in node && Array.isArray(node.children)) {
        if (this.findElementType(node.children, type)) {
          return true
        }
      }
    }
    return false
  }
}
