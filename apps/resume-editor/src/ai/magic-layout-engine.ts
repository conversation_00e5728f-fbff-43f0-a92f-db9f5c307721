/**
 * Magic Layout AI Engine
 * Small LLM integration for layout optimization with ATS compatibility scoring
 */

import { ResumeDocument, CustomElement } from '@/types/resume'
import { TemplateConfig } from '@/templates/template-engine'

export interface LayoutSuggestion {
  id: string
  type: 'reorder' | 'restructure' | 'optimize' | 'enhance'
  title: string
  description: string
  confidence: number
  impact: 'low' | 'medium' | 'high'
  category: 'ats' | 'visual' | 'content' | 'structure'
  changes: LayoutChange[]
  reasoning: string
  atsImpact: number
  visualImpact: number
  beforePreview?: string
  afterPreview?: string
}

export interface LayoutChange {
  path: number[]
  type: 'move' | 'modify' | 'add' | 'remove'
  before: any
  after: any
  reason: string
}

export interface ATSScore {
  overall: number
  breakdown: {
    structure: number
    keywords: number
    formatting: number
    readability: number
    sections: number
  }
  issues: string[]
  recommendations: string[]
}

export interface LayoutAnalysis {
  contentDensity: number
  visualHierarchy: number
  readabilityScore: number
  sectionBalance: number
  whiteSpaceRatio: number
  keywordDensity: number
  atsCompatibility: ATSScore
}

export class MagicLayoutEngine {
  private static instance: MagicLayoutEngine
  private apiEndpoint: string
  private apiKey: string
  private isInitialized = false
  private modelVersion = 'v1.0'

  private constructor() {
    this.apiEndpoint = process.env.NEXT_PUBLIC_AI_ENDPOINT || 'http://localhost:8080/v1'
    this.apiKey = process.env.NEXT_PUBLIC_AI_API_KEY || ''
  }

  static getInstance(): MagicLayoutEngine {
    if (!MagicLayoutEngine.instance) {
      MagicLayoutEngine.instance = new MagicLayoutEngine()
    }
    return MagicLayoutEngine.instance
  }

  async initialize(): Promise<void> {
    try {
      // Test API connection
      const response = await fetch(`${this.apiEndpoint}/health`)
      if (!response.ok) {
        throw new Error('AI service unavailable')
      }
      
      this.isInitialized = true
      console.log('✅ Magic Layout AI initialized')
    } catch (error) {
      console.warn('⚠️ AI service unavailable, using fallback rules')
      this.isInitialized = false
    }
  }

  async analyzeLayout(document: ResumeDocument, template?: TemplateConfig): Promise<LayoutAnalysis> {
    const analysis: LayoutAnalysis = {
      contentDensity: this.calculateContentDensity(document),
      visualHierarchy: this.analyzeVisualHierarchy(document),
      readabilityScore: this.calculateReadabilityScore(document),
      sectionBalance: this.analyzeSectionBalance(document),
      whiteSpaceRatio: this.calculateWhiteSpaceRatio(document),
      keywordDensity: this.calculateKeywordDensity(document),
      atsCompatibility: await this.analyzeATSCompatibility(document)
    }

    return analysis
  }

  async generateSuggestions(
    document: ResumeDocument, 
    targetRole?: string,
    template?: TemplateConfig
  ): Promise<LayoutSuggestion[]> {
    const analysis = await this.analyzeLayout(document, template)
    const suggestions: LayoutSuggestion[] = []

    // AI-powered suggestions if available
    if (this.isInitialized) {
      try {
        const aiSuggestions = await this.getAISuggestions(document, analysis, targetRole)
        suggestions.push(...aiSuggestions)
      } catch (error) {
        console.warn('AI suggestions failed, using rule-based fallback')
      }
    }

    // Rule-based fallback suggestions
    const ruleSuggestions = this.generateRuleBasedSuggestions(document, analysis)
    suggestions.push(...ruleSuggestions)

    // Sort by confidence and impact
    return suggestions
      .sort((a, b) => (b.confidence * this.getImpactWeight(b.impact)) - (a.confidence * this.getImpactWeight(a.impact)))
      .slice(0, 10) // Limit to top 10 suggestions
  }

  async optimizeLayout(
    document: ResumeDocument,
    suggestions: LayoutSuggestion[],
    selectedIds: string[]
  ): Promise<ResumeDocument> {
    let optimizedDocument = { ...document }

    for (const suggestionId of selectedIds) {
      const suggestion = suggestions.find(s => s.id === suggestionId)
      if (!suggestion) continue

      optimizedDocument = this.applySuggestion(optimizedDocument, suggestion)
    }

    return optimizedDocument
  }

  private async getAISuggestions(
    document: ResumeDocument,
    analysis: LayoutAnalysis,
    targetRole?: string
  ): Promise<LayoutSuggestion[]> {
    const prompt = this.buildOptimizationPrompt(document, analysis, targetRole)
    
    const response = await fetch(`${this.apiEndpoint}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are an expert resume layout optimizer. Analyze the resume and provide specific, actionable suggestions to improve ATS compatibility and visual appeal.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.3,
        response_format: { type: 'json_object' }
      })
    })

    if (!response.ok) {
      throw new Error('AI API request failed')
    }

    const result = await response.json()
    const aiResponse = JSON.parse(result.choices[0].message.content)

    return this.parseAISuggestions(aiResponse)
  }

  private buildOptimizationPrompt(
    document: ResumeDocument,
    analysis: LayoutAnalysis,
    targetRole?: string
  ): string {
    return `
Analyze this resume and provide layout optimization suggestions:

RESUME CONTENT:
${JSON.stringify(document.content, null, 2)}

CURRENT ANALYSIS:
- Content Density: ${analysis.contentDensity}
- Visual Hierarchy: ${analysis.visualHierarchy}
- Readability: ${analysis.readabilityScore}
- ATS Score: ${analysis.atsCompatibility.overall}

TARGET ROLE: ${targetRole || 'General'}

Please provide suggestions in this JSON format:
{
  "suggestions": [
    {
      "id": "unique-id",
      "type": "reorder|restructure|optimize|enhance",
      "title": "Brief title",
      "description": "Detailed description",
      "confidence": 0.0-1.0,
      "impact": "low|medium|high",
      "category": "ats|visual|content|structure",
      "reasoning": "Why this helps",
      "atsImpact": 0.0-1.0,
      "visualImpact": 0.0-1.0,
      "changes": [
        {
          "path": [section_index],
          "type": "move|modify|add|remove",
          "reason": "Explanation"
        }
      ]
    }
  ]
}

Focus on:
1. ATS optimization (keyword placement, section order, formatting)
2. Visual hierarchy (section prominence, content flow)
3. Industry best practices for the target role
4. Readability and scannability improvements
`
  }

  private parseAISuggestions(aiResponse: any): LayoutSuggestion[] {
    const suggestions: LayoutSuggestion[] = []

    if (aiResponse.suggestions && Array.isArray(aiResponse.suggestions)) {
      for (const suggestion of aiResponse.suggestions) {
        suggestions.push({
          id: suggestion.id || `ai-${Date.now()}-${Math.random()}`,
          type: suggestion.type || 'optimize',
          title: suggestion.title || 'AI Suggestion',
          description: suggestion.description || '',
          confidence: Math.min(1, Math.max(0, suggestion.confidence || 0.5)),
          impact: suggestion.impact || 'medium',
          category: suggestion.category || 'structure',
          changes: suggestion.changes || [],
          reasoning: suggestion.reasoning || '',
          atsImpact: suggestion.atsImpact || 0.5,
          visualImpact: suggestion.visualImpact || 0.5
        })
      }
    }

    return suggestions
  }

  private generateRuleBasedSuggestions(
    document: ResumeDocument,
    analysis: LayoutAnalysis
  ): LayoutSuggestion[] {
    const suggestions: LayoutSuggestion[] = []

    // Section order optimization
    if (this.shouldReorderSections(document)) {
      suggestions.push({
        id: 'reorder-sections',
        type: 'reorder',
        title: 'Optimize Section Order',
        description: 'Move experience section before education for better ATS parsing',
        confidence: 0.8,
        impact: 'high',
        category: 'ats',
        changes: [{
          path: [1, 2],
          type: 'move',
          before: 'education-first',
          after: 'experience-first',
          reason: 'Experience should come before education for experienced professionals'
        }],
        reasoning: 'ATS systems and recruiters typically scan for experience first',
        atsImpact: 0.7,
        visualImpact: 0.6
      })
    }

    // Content density optimization
    if (analysis.contentDensity > 0.8) {
      suggestions.push({
        id: 'reduce-density',
        type: 'optimize',
        title: 'Reduce Content Density',
        description: 'Break up dense text blocks for better readability',
        confidence: 0.7,
        impact: 'medium',
        category: 'visual',
        changes: [{
          path: [],
          type: 'modify',
          before: 'dense-content',
          after: 'spaced-content',
          reason: 'Improve visual breathing room'
        }],
        reasoning: 'Dense content is harder to scan and may overwhelm readers',
        atsImpact: 0.4,
        visualImpact: 0.8
      })
    }

    // ATS compatibility improvements
    if (analysis.atsCompatibility.overall < 0.7) {
      suggestions.push({
        id: 'improve-ats',
        type: 'enhance',
        title: 'Improve ATS Compatibility',
        description: 'Add standard section headers and optimize formatting',
        confidence: 0.9,
        impact: 'high',
        category: 'ats',
        changes: [{
          path: [],
          type: 'modify',
          before: 'non-standard-headers',
          after: 'standard-headers',
          reason: 'Use ATS-friendly section names'
        }],
        reasoning: 'Standard headers improve parsing accuracy',
        atsImpact: 0.9,
        visualImpact: 0.3
      })
    }

    // Visual hierarchy improvements
    if (analysis.visualHierarchy < 0.6) {
      suggestions.push({
        id: 'improve-hierarchy',
        type: 'restructure',
        title: 'Enhance Visual Hierarchy',
        description: 'Improve heading structure and content organization',
        confidence: 0.6,
        impact: 'medium',
        category: 'visual',
        changes: [{
          path: [],
          type: 'modify',
          before: 'flat-structure',
          after: 'hierarchical-structure',
          reason: 'Create clear information hierarchy'
        }],
        reasoning: 'Better hierarchy guides the reader\'s eye through the content',
        atsImpact: 0.3,
        visualImpact: 0.8
      })
    }

    return suggestions
  }

  private calculateContentDensity(document: ResumeDocument): number {
    const totalContent = this.extractTextContent(document).length
    const estimatedPageArea = 8.5 * 11 * 72 * 72 // Letter size in points
    const averageCharWidth = 6 // points
    const averageLineHeight = 12 // points
    
    const maxCharsPerPage = (estimatedPageArea / (averageCharWidth * averageLineHeight))
    return Math.min(1, totalContent / maxCharsPerPage)
  }

  private analyzeVisualHierarchy(document: ResumeDocument): number {
    let score = 0
    let totalElements = 0

    for (const section of document.content) {
      totalElements++
      
      // Check for proper heading structure
      if (section.type === 'heading' || section.type === 'section') {
        score += 0.2
      }
      
      // Check for consistent formatting
      if (this.hasConsistentFormatting(section)) {
        score += 0.1
      }
    }

    return totalElements > 0 ? Math.min(1, score / totalElements * 5) : 0
  }

  private calculateReadabilityScore(document: ResumeDocument): number {
    const text = this.extractTextContent(document)
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const words = text.split(/\s+/).filter(w => w.length > 0)
    
    if (sentences.length === 0 || words.length === 0) return 0

    const avgWordsPerSentence = words.length / sentences.length
    const avgSyllablesPerWord = this.estimateSyllables(words)
    
    // Simplified Flesch Reading Ease
    const fleschScore = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord)
    
    // Normalize to 0-1 scale (60-100 is good for professional documents)
    return Math.max(0, Math.min(1, (fleschScore - 30) / 70))
  }

  private analyzeSectionBalance(document: ResumeDocument): number {
    const sectionLengths = document.content.map(section => 
      this.extractTextContent({ content: [section] }).length
    )
    
    if (sectionLengths.length === 0) return 0
    
    const mean = sectionLengths.reduce((a, b) => a + b, 0) / sectionLengths.length
    const variance = sectionLengths.reduce((acc, length) => acc + Math.pow(length - mean, 2), 0) / sectionLengths.length
    const standardDeviation = Math.sqrt(variance)
    
    // Lower standard deviation = better balance
    const coefficientOfVariation = standardDeviation / mean
    return Math.max(0, 1 - coefficientOfVariation)
  }

  private calculateWhiteSpaceRatio(document: ResumeDocument): number {
    // Simplified calculation - in production, analyze actual layout
    const textLength = this.extractTextContent(document).length
    const estimatedWhiteSpace = Math.max(0, 2000 - textLength) // Assume 2000 chars is optimal
    return Math.min(1, estimatedWhiteSpace / 1000)
  }

  private calculateKeywordDensity(document: ResumeDocument): number {
    const text = this.extractTextContent(document).toLowerCase()
    const words = text.split(/\s+/)
    
    // Common professional keywords
    const keywords = [
      'managed', 'developed', 'implemented', 'led', 'created', 'designed',
      'improved', 'increased', 'reduced', 'achieved', 'delivered', 'collaborated'
    ]
    
    const keywordCount = words.filter(word => keywords.includes(word)).length
    return words.length > 0 ? keywordCount / words.length : 0
  }

  private async analyzeATSCompatibility(document: ResumeDocument): Promise<ATSScore> {
    // Simplified ATS analysis - in production, use comprehensive rules
    const score: ATSScore = {
      overall: 0.75,
      breakdown: {
        structure: 0.8,
        keywords: 0.7,
        formatting: 0.8,
        readability: 0.7,
        sections: 0.75
      },
      issues: [],
      recommendations: []
    }

    // Check for standard sections
    const standardSections = ['experience', 'education', 'skills', 'summary']
    const foundSections = document.content.map(s => s.type)
    const missingSections = standardSections.filter(s => !foundSections.includes(s))
    
    if (missingSections.length > 0) {
      score.issues.push(`Missing standard sections: ${missingSections.join(', ')}`)
      score.recommendations.push('Add missing standard sections for better ATS parsing')
    }

    score.overall = Object.values(score.breakdown).reduce((a, b) => a + b, 0) / Object.keys(score.breakdown).length

    return score
  }

  private shouldReorderSections(document: ResumeDocument): boolean {
    const sectionTypes = document.content.map(s => s.type)
    const experienceIndex = sectionTypes.indexOf('experience')
    const educationIndex = sectionTypes.indexOf('education')
    
    // Suggest reordering if education comes before experience
    return educationIndex !== -1 && experienceIndex !== -1 && educationIndex < experienceIndex
  }

  private hasConsistentFormatting(section: CustomElement): boolean {
    // Simplified check - in production, analyze actual formatting
    return true
  }

  private extractTextContent(document: Partial<ResumeDocument>): string {
    // Simplified text extraction - in production, implement proper content parsing
    return JSON.stringify(document.content || [])
      .replace(/[{}"[\]]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
  }

  private estimateSyllables(words: string[]): number {
    // Simplified syllable estimation
    return words.reduce((total, word) => {
      const syllables = word.toLowerCase().replace(/[^a-z]/g, '').match(/[aeiouy]+/g)
      return total + (syllables ? syllables.length : 1)
    }, 0) / words.length
  }

  private getImpactWeight(impact: string): number {
    switch (impact) {
      case 'high': return 1.0
      case 'medium': return 0.7
      case 'low': return 0.4
      default: return 0.5
    }
  }

  private applySuggestion(document: ResumeDocument, suggestion: LayoutSuggestion): ResumeDocument {
    // Simplified application - in production, implement proper change application
    console.log(`Applying suggestion: ${suggestion.title}`)
    return document
  }

  // Public API for React components
  isAvailable(): boolean {
    return this.isInitialized
  }

  getModelVersion(): string {
    return this.modelVersion
  }
}
