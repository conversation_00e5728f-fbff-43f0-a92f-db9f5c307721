/**
 * High-performance drag-and-drop system with Dnd-Kit and Framer Motion
 * 60fps animations with collision detection and auto-scrolling
 */

import React, { useState, useCallback, useMemo } from 'react'
import {
  DndContext,
  DragOverlay,
  DragStartEvent,
  DragEndEvent,
  DragOverEvent,
  PointerSensor,
  KeyboardSensor,
  useSensor,
  useSensors,
  closestCenter,
  closestCorners,
  rectIntersection,
  getFirstCollision,
  pointerWithin,
  MeasuringStrategy,
  DropAnimation,
  defaultDropAnimationSideEffects,
  UniqueIdentifier,
  CollisionDetection,
  Active,
  Over
} from '@dnd-kit/core'
import {
  SortableContext,
  verticalListSortingStrategy,
  horizontalListSortingStrategy,
  rectSortingStrategy,
  arrayMove,
  sortableKeyboardCoordinates
} from '@dnd-kit/sortable'
import {
  restrictToVerticalAxis,
  restrictToHorizontalAxis,
  restrictToWindowEdges,
  restrictToParentElement
} from '@dnd-kit/modifiers'
import { motion, AnimatePresence, LayoutGroup } from 'framer-motion'
import { createPortal } from 'react-dom'

import { useEditorStore } from '@/stores/editorStore'
import { CustomElement } from '@/types/resume'

export interface DragDropConfig {
  animationDuration: number
  collisionDetection: 'closest-center' | 'closest-corners' | 'rect-intersection' | 'pointer-within'
  autoScroll: boolean
  scrollSpeed: number
  dropAnimation: boolean
  hapticFeedback: boolean
}

export interface DragDropContextValue {
  isDragging: boolean
  activeId: UniqueIdentifier | null
  overId: UniqueIdentifier | null
  draggedElement: CustomElement | null
  config: DragDropConfig
  updateConfig: (config: Partial<DragDropConfig>) => void
}

const DragDropContext = React.createContext<DragDropContextValue | null>(null)

export function useDragDrop() {
  const context = React.useContext(DragDropContext)
  if (!context) {
    throw new Error('useDragDrop must be used within DragDropProvider')
  }
  return context
}

interface DragDropProviderProps {
  children: React.ReactNode
  config?: Partial<DragDropConfig>
}

const defaultConfig: DragDropConfig = {
  animationDuration: 200,
  collisionDetection: 'closest-center',
  autoScroll: true,
  scrollSpeed: 15,
  dropAnimation: true,
  hapticFeedback: true
}

const dropAnimationConfig: DropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: '0.4',
      },
    },
  }),
}

const measuring = {
  droppable: {
    strategy: MeasuringStrategy.Always,
  },
}

export function DragDropProvider({ children, config: configOverride }: DragDropProviderProps) {
  const [config, setConfig] = useState<DragDropConfig>({
    ...defaultConfig,
    ...configOverride
  })
  
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null)
  const [overId, setOverId] = useState<UniqueIdentifier | null>(null)
  const [draggedElement, setDraggedElement] = useState<CustomElement | null>(null)
  
  const { document: resumeDocument, updateDocument } = useEditorStore()

  // Sensors for different input methods
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px movement required to start drag
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // Collision detection algorithms
  const collisionDetectionAlgorithm: CollisionDetection = useCallback((args) => {
    switch (config.collisionDetection) {
      case 'closest-corners':
        return closestCorners(args)
      case 'rect-intersection':
        return rectIntersection(args)
      case 'pointer-within':
        return pointerWithin(args)
      default:
        return closestCenter(args)
    }
  }, [config.collisionDetection])

  // Custom collision detection for nested containers
  const customCollisionDetection: CollisionDetection = useCallback((args) => {
    // First, let's see if there are any collisions with the pointer
    const pointerCollisions = pointerWithin(args)
    
    if (pointerCollisions.length > 0) {
      return pointerCollisions
    }

    // If there are no pointer collisions, use the default algorithm
    return collisionDetectionAlgorithm(args)
  }, [collisionDetectionAlgorithm])

  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event
    setActiveId(active.id)
    
    // Find the dragged element
    const element = findElementById(resumeDocument?.content || [], active.id as string)
    setDraggedElement(element)
    
    // Haptic feedback on supported devices
    if (config.hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(50)
    }
    
    // Add dragging class to body for global styles
    document.body.classList.add('is-dragging')
  }, [resumeDocument, config.hapticFeedback])

  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { over } = event
    setOverId(over?.id || null)
  }, [])

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event
    
    setActiveId(null)
    setOverId(null)
    setDraggedElement(null)
    document.body.classList.remove('is-dragging')
    
    if (!over || !resumeDocument) return
    
    const activeId = active.id as string
    const overId = over.id as string
    
    if (activeId === overId) return
    
    // Handle different drop scenarios
    if (activeId.startsWith('section-') && overId.startsWith('section-')) {
      handleSectionReorder(activeId, overId)
    } else if (activeId.startsWith('element-') && overId.startsWith('section-')) {
      handleElementToSection(activeId, overId)
    } else if (activeId.startsWith('element-') && overId.startsWith('element-')) {
      handleElementReorder(activeId, overId)
    }
    
    // Haptic feedback on drop
    if (config.hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(100)
    }
  }, [resumeDocument, config.hapticFeedback])

  const handleSectionReorder = useCallback((activeId: string, overId: string) => {
    if (!resumeDocument) return
    
    const content = resumeDocument.content
    const activeIndex = content.findIndex(item => item.id === activeId)
    const overIndex = content.findIndex(item => item.id === overId)
    
    if (activeIndex === -1 || overIndex === -1) return
    
    const newContent = arrayMove(content, activeIndex, overIndex)
    
    updateDocument({
      ...resumeDocument,
      content: newContent
    })
  }, [resumeDocument, updateDocument])

  const handleElementToSection = useCallback((elementId: string, sectionId: string) => {
    if (!resumeDocument) return
    
    // Find element and remove from current location
    let element: CustomElement | null = null
    const newContent = resumeDocument.content.map(section => {
      if (section.type === 'section' && section.children) {
        const elementIndex = section.children.findIndex(child => 
          'id' in child && child.id === elementId
        )
        
        if (elementIndex !== -1) {
          element = section.children[elementIndex] as CustomElement
          return {
            ...section,
            children: section.children.filter((_, index) => index !== elementIndex)
          }
        }
      }
      return section
    })
    
    if (!element) return
    
    // Add element to target section
    const finalContent = newContent.map(section => {
      if (section.id === sectionId && section.type === 'section') {
        return {
          ...section,
          children: [...(section.children || []), element!]
        }
      }
      return section
    })
    
    updateDocument({
      ...resumeDocument,
      content: finalContent
    })
  }, [resumeDocument, updateDocument])

  const handleElementReorder = useCallback((activeId: string, overId: string) => {
    if (!resumeDocument) return
    
    // Find the parent section of both elements
    let parentSection: CustomElement | null = null
    let activeIndex = -1
    let overIndex = -1
    
    for (const section of resumeDocument.content) {
      if (section.type === 'section' && section.children) {
        const activeIdx = section.children.findIndex(child => 
          'id' in child && child.id === activeId
        )
        const overIdx = section.children.findIndex(child => 
          'id' in child && child.id === overId
        )
        
        if (activeIdx !== -1 && overIdx !== -1) {
          parentSection = section
          activeIndex = activeIdx
          overIndex = overIdx
          break
        }
      }
    }
    
    if (!parentSection || activeIndex === -1 || overIndex === -1) return
    
    const newChildren = arrayMove(parentSection.children!, activeIndex, overIndex)
    
    const newContent = resumeDocument.content.map(section => {
      if (section.id === parentSection!.id) {
        return {
          ...section,
          children: newChildren
        }
      }
      return section
    })
    
    updateDocument({
      ...resumeDocument,
      content: newContent
    })
  }, [resumeDocument, updateDocument])

  const updateConfig = useCallback((newConfig: Partial<DragDropConfig>) => {
    setConfig(prev => ({ ...prev, ...newConfig }))
  }, [])

  const contextValue = useMemo<DragDropContextValue>(() => ({
    isDragging: activeId !== null,
    activeId,
    overId,
    draggedElement,
    config,
    updateConfig
  }), [activeId, overId, draggedElement, config, updateConfig])

  return (
    <DragDropContext.Provider value={contextValue}>
      <LayoutGroup>
        <DndContext
          sensors={sensors}
          collisionDetection={customCollisionDetection}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
          measuring={measuring}
          autoScroll={config.autoScroll}
        >
          {children}
          
          <DragOverlay
            dropAnimation={config.dropAnimation ? dropAnimationConfig : null}
            style={{
              transformOrigin: '0 0',
            }}
          >
            {activeId && draggedElement ? (
              <DragOverlayContent element={draggedElement} />
            ) : null}
          </DragOverlay>
        </DndContext>
      </LayoutGroup>
    </DragDropContext.Provider>
  )
}

// Drag overlay content component
interface DragOverlayContentProps {
  element: CustomElement
}

function DragOverlayContent({ element }: DragOverlayContentProps) {
  return (
    <motion.div
      initial={{ scale: 1, rotate: 0 }}
      animate={{ scale: 1.05, rotate: 2 }}
      className="bg-white shadow-2xl border-2 border-blue-500 rounded-lg p-4 opacity-90"
      style={{
        maxWidth: '300px',
        pointerEvents: 'none'
      }}
    >
      <div className="text-sm font-medium text-gray-900 truncate">
        {element.type === 'section' ? 'Section' : 'Element'}
      </div>
      <div className="text-xs text-gray-500 mt-1">
        {element.id}
      </div>
    </motion.div>
  )
}

// Sortable container component
interface SortableContainerProps {
  id: string
  children: React.ReactNode
  strategy?: 'vertical' | 'horizontal' | 'rect'
  disabled?: boolean
  className?: string
}

export function SortableContainer({ 
  id, 
  children, 
  strategy = 'vertical',
  disabled = false,
  className = ''
}: SortableContainerProps) {
  const items = React.Children.map(children, (child, index) => 
    React.isValidElement(child) && child.props.id ? child.props.id : `${id}-${index}`
  ) || []

  const sortingStrategy = {
    vertical: verticalListSortingStrategy,
    horizontal: horizontalListSortingStrategy,
    rect: rectSortingStrategy
  }[strategy]

  return (
    <SortableContext 
      id={id}
      items={items} 
      strategy={sortingStrategy}
      disabled={disabled}
    >
      <div className={className} data-sortable-container={id}>
        {children}
      </div>
    </SortableContext>
  )
}

// Drop zone component
interface DropZoneProps {
  id: string
  children: React.ReactNode
  className?: string
  activeClassName?: string
  onDrop?: (activeId: string) => void
}

export function DropZone({ 
  id, 
  children, 
  className = '',
  activeClassName = '',
  onDrop
}: DropZoneProps) {
  const { overId, activeId } = useDragDrop()
  const isActive = overId === id && activeId !== null

  return (
    <div
      data-drop-zone={id}
      className={`${className} ${isActive ? activeClassName : ''}`}
      onDrop={() => {
        if (activeId && onDrop) {
          onDrop(activeId as string)
        }
      }}
    >
      {children}
      
      {isActive && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="absolute inset-0 bg-blue-500 bg-opacity-20 border-2 border-blue-500 border-dashed rounded-lg flex items-center justify-center"
          style={{ pointerEvents: 'none' }}
        >
          <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
            Drop here
          </div>
        </motion.div>
      )}
    </div>
  )
}

// Utility functions
function findElementById(content: any[], id: string): CustomElement | null {
  for (const item of content) {
    if (item.id === id) {
      return item
    }
    
    if (item.children && Array.isArray(item.children)) {
      const found = findElementById(item.children, id)
      if (found) return found
    }
  }
  
  return null
}

// Performance monitoring hook
export function useDragDropPerformance() {
  const [metrics, setMetrics] = useState({
    dragStartTime: 0,
    dragDuration: 0,
    frameRate: 0,
    droppedFrames: 0
  })

  const startPerformanceMonitoring = useCallback(() => {
    const startTime = performance.now()
    let frameCount = 0
    let lastFrameTime = startTime
    
    const measureFrame = () => {
      const currentTime = performance.now()
      const frameDuration = currentTime - lastFrameTime
      
      frameCount++
      lastFrameTime = currentTime
      
      // Calculate FPS
      const fps = 1000 / frameDuration
      
      setMetrics(prev => ({
        ...prev,
        dragStartTime: startTime,
        frameRate: fps,
        droppedFrames: fps < 55 ? prev.droppedFrames + 1 : prev.droppedFrames
      }))
      
      requestAnimationFrame(measureFrame)
    }
    
    requestAnimationFrame(measureFrame)
    
    return () => {
      const endTime = performance.now()
      setMetrics(prev => ({
        ...prev,
        dragDuration: endTime - startTime
      }))
    }
  }, [])

  return { metrics, startPerformanceMonitoring }
}
