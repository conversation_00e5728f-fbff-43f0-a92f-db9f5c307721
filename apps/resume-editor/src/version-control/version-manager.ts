/**
 * Git-style Version Control System
 * Implements branching, merging, diff visualization, and rollback functionality
 */

import { ResumeDocument, CustomElement } from '@/types/resume'
import { nanoid } from 'nanoid'

export interface VersionCommit {
  id: string
  parentIds: string[]
  timestamp: number
  author: {
    id: string
    name: string
    email: string
  }
  message: string
  changes: VersionChange[]
  document: ResumeDocument
  metadata: {
    branch: string
    tags: string[]
    stats: {
      additions: number
      deletions: number
      modifications: number
    }
  }
}

export interface VersionChange {
  type: 'add' | 'remove' | 'modify'
  path: string[]
  before?: any
  after?: any
  description: string
}

export interface VersionBranch {
  name: string
  headCommitId: string
  createdAt: number
  createdBy: string
  description?: string
  protected: boolean
}

export interface VersionDiff {
  additions: VersionChange[]
  deletions: VersionChange[]
  modifications: VersionChange[]
  summary: {
    totalChanges: number
    additionsCount: number
    deletionsCount: number
    modificationsCount: number
  }
}

export interface VersionGraph {
  commits: Map<string, VersionCommit>
  branches: Map<string, VersionBranch>
  currentBranch: string
  currentCommit: string
}

export class VersionManager {
  private graph: VersionGraph
  private maxHistorySize: number = 100
  private autoCommitInterval: number = 30000 // 30 seconds

  constructor() {
    this.graph = {
      commits: new Map(),
      branches: new Map(),
      currentBranch: 'main',
      currentCommit: ''
    }

    // Initialize main branch
    this.createBranch('main', 'Main branch', true)
  }

  // Commit operations
  async createCommit(
    document: ResumeDocument,
    message: string,
    author: VersionCommit['author'],
    parentDocument?: ResumeDocument
  ): Promise<VersionCommit> {
    const commitId = nanoid()
    const parentIds = this.graph.currentCommit ? [this.graph.currentCommit] : []
    
    // Calculate changes
    const changes = parentDocument 
      ? this.calculateChanges(parentDocument, document)
      : this.getInitialChanges(document)

    const commit: VersionCommit = {
      id: commitId,
      parentIds,
      timestamp: Date.now(),
      author,
      message,
      changes,
      document: this.deepClone(document),
      metadata: {
        branch: this.graph.currentBranch,
        tags: [],
        stats: this.calculateStats(changes)
      }
    }

    // Add to graph
    this.graph.commits.set(commitId, commit)
    this.graph.currentCommit = commitId

    // Update branch head
    const branch = this.graph.branches.get(this.graph.currentBranch)
    if (branch) {
      branch.headCommitId = commitId
    }

    // Cleanup old commits if needed
    this.cleanupHistory()

    return commit
  }

  async amendCommit(
    document: ResumeDocument,
    message?: string
  ): Promise<VersionCommit | null> {
    const currentCommit = this.getCurrentCommit()
    if (!currentCommit) return null

    // Update the current commit
    const parentDocument = currentCommit.parentIds.length > 0
      ? this.graph.commits.get(currentCommit.parentIds[0])?.document
      : undefined

    const changes = parentDocument 
      ? this.calculateChanges(parentDocument, document)
      : this.getInitialChanges(document)

    const updatedCommit: VersionCommit = {
      ...currentCommit,
      message: message || currentCommit.message,
      changes,
      document: this.deepClone(document),
      timestamp: Date.now(),
      metadata: {
        ...currentCommit.metadata,
        stats: this.calculateStats(changes)
      }
    }

    this.graph.commits.set(currentCommit.id, updatedCommit)
    return updatedCommit
  }

  // Branch operations
  createBranch(name: string, description?: string, protected: boolean = false): VersionBranch {
    if (this.graph.branches.has(name)) {
      throw new Error(`Branch ${name} already exists`)
    }

    const branch: VersionBranch = {
      name,
      headCommitId: this.graph.currentCommit,
      createdAt: Date.now(),
      createdBy: 'current-user', // In production, get from auth context
      description,
      protected
    }

    this.graph.branches.set(name, branch)
    return branch
  }

  switchBranch(branchName: string): ResumeDocument | null {
    const branch = this.graph.branches.get(branchName)
    if (!branch) {
      throw new Error(`Branch ${branchName} does not exist`)
    }

    this.graph.currentBranch = branchName
    this.graph.currentCommit = branch.headCommitId

    const headCommit = this.graph.commits.get(branch.headCommitId)
    return headCommit ? this.deepClone(headCommit.document) : null
  }

  mergeBranch(
    sourceBranch: string,
    targetBranch: string,
    author: VersionCommit['author'],
    message?: string
  ): Promise<VersionCommit> {
    const source = this.graph.branches.get(sourceBranch)
    const target = this.graph.branches.get(targetBranch)

    if (!source || !target) {
      throw new Error('Source or target branch does not exist')
    }

    const sourceCommit = this.graph.commits.get(source.headCommitId)
    const targetCommit = this.graph.commits.get(target.headCommitId)

    if (!sourceCommit || !targetCommit) {
      throw new Error('Invalid commit references')
    }

    // Simple merge strategy - use source document
    // In production, implement proper 3-way merge
    const mergedDocument = this.deepClone(sourceCommit.document)

    // Switch to target branch
    this.switchBranch(targetBranch)

    // Create merge commit
    const mergeCommit: VersionCommit = {
      id: nanoid(),
      parentIds: [target.headCommitId, source.headCommitId],
      timestamp: Date.now(),
      author,
      message: message || `Merge ${sourceBranch} into ${targetBranch}`,
      changes: this.calculateChanges(targetCommit.document, mergedDocument),
      document: mergedDocument,
      metadata: {
        branch: targetBranch,
        tags: ['merge'],
        stats: { additions: 0, deletions: 0, modifications: 1 }
      }
    }

    this.graph.commits.set(mergeCommit.id, mergeCommit)
    this.graph.currentCommit = mergeCommit.id
    target.headCommitId = mergeCommit.id

    return Promise.resolve(mergeCommit)
  }

  // History and diff operations
  getCommitHistory(limit: number = 50): VersionCommit[] {
    const history: VersionCommit[] = []
    let currentId = this.graph.currentCommit

    while (currentId && history.length < limit) {
      const commit = this.graph.commits.get(currentId)
      if (!commit) break

      history.push(commit)
      currentId = commit.parentIds[0] // Follow first parent
    }

    return history
  }

  getBranchHistory(branchName: string, limit: number = 50): VersionCommit[] {
    const branch = this.graph.branches.get(branchName)
    if (!branch) return []

    const history: VersionCommit[] = []
    let currentId = branch.headCommitId

    while (currentId && history.length < limit) {
      const commit = this.graph.commits.get(currentId)
      if (!commit) break

      history.push(commit)
      currentId = commit.parentIds[0]
    }

    return history
  }

  getDiff(fromCommitId: string, toCommitId: string): VersionDiff {
    const fromCommit = this.graph.commits.get(fromCommitId)
    const toCommit = this.graph.commits.get(toCommitId)

    if (!fromCommit || !toCommit) {
      throw new Error('Invalid commit IDs')
    }

    const changes = this.calculateChanges(fromCommit.document, toCommit.document)
    
    return {
      additions: changes.filter(c => c.type === 'add'),
      deletions: changes.filter(c => c.type === 'remove'),
      modifications: changes.filter(c => c.type === 'modify'),
      summary: {
        totalChanges: changes.length,
        additionsCount: changes.filter(c => c.type === 'add').length,
        deletionsCount: changes.filter(c => c.type === 'remove').length,
        modificationsCount: changes.filter(c => c.type === 'modify').length
      }
    }
  }

  // Rollback operations
  rollbackToCommit(commitId: string): ResumeDocument | null {
    const commit = this.graph.commits.get(commitId)
    if (!commit) {
      throw new Error('Commit not found')
    }

    this.graph.currentCommit = commitId
    
    // Update branch head
    const branch = this.graph.branches.get(this.graph.currentBranch)
    if (branch) {
      branch.headCommitId = commitId
    }

    return this.deepClone(commit.document)
  }

  revertCommit(
    commitId: string,
    author: VersionCommit['author']
  ): Promise<VersionCommit> {
    const commit = this.graph.commits.get(commitId)
    if (!commit) {
      throw new Error('Commit not found')
    }

    const currentCommit = this.getCurrentCommit()
    if (!currentCommit) {
      throw new Error('No current commit')
    }

    // Create inverse changes
    const inverseChanges = this.createInverseChanges(commit.changes)
    const revertedDocument = this.applyChanges(currentCommit.document, inverseChanges)

    const revertCommit: VersionCommit = {
      id: nanoid(),
      parentIds: [this.graph.currentCommit],
      timestamp: Date.now(),
      author,
      message: `Revert "${commit.message}"`,
      changes: inverseChanges,
      document: revertedDocument,
      metadata: {
        branch: this.graph.currentBranch,
        tags: ['revert'],
        stats: this.calculateStats(inverseChanges)
      }
    }

    this.graph.commits.set(revertCommit.id, revertCommit)
    this.graph.currentCommit = revertCommit.id

    // Update branch head
    const branch = this.graph.branches.get(this.graph.currentBranch)
    if (branch) {
      branch.headCommitId = revertCommit.id
    }

    return Promise.resolve(revertCommit)
  }

  // Utility methods
  getCurrentCommit(): VersionCommit | null {
    return this.graph.currentCommit 
      ? this.graph.commits.get(this.graph.currentCommit) || null
      : null
  }

  getCurrentDocument(): ResumeDocument | null {
    const commit = this.getCurrentCommit()
    return commit ? this.deepClone(commit.document) : null
  }

  getBranches(): VersionBranch[] {
    return Array.from(this.graph.branches.values())
  }

  getCurrentBranch(): string {
    return this.graph.currentBranch
  }

  // Private helper methods
  private calculateChanges(before: ResumeDocument, after: ResumeDocument): VersionChange[] {
    const changes: VersionChange[] = []
    
    // Compare document metadata
    if (JSON.stringify(before.metadata) !== JSON.stringify(after.metadata)) {
      changes.push({
        type: 'modify',
        path: ['metadata'],
        before: before.metadata,
        after: after.metadata,
        description: 'Updated document metadata'
      })
    }

    // Compare content sections
    this.compareArrays(before.content, after.content, ['content'], changes)

    return changes
  }

  private compareArrays(before: any[], after: any[], path: string[], changes: VersionChange[]): void {
    const maxLength = Math.max(before.length, after.length)

    for (let i = 0; i < maxLength; i++) {
      const beforeItem = before[i]
      const afterItem = after[i]
      const itemPath = [...path, i.toString()]

      if (!beforeItem && afterItem) {
        changes.push({
          type: 'add',
          path: itemPath,
          after: afterItem,
          description: `Added ${afterItem.type || 'item'} at position ${i}`
        })
      } else if (beforeItem && !afterItem) {
        changes.push({
          type: 'remove',
          path: itemPath,
          before: beforeItem,
          description: `Removed ${beforeItem.type || 'item'} from position ${i}`
        })
      } else if (beforeItem && afterItem) {
        if (JSON.stringify(beforeItem) !== JSON.stringify(afterItem)) {
          changes.push({
            type: 'modify',
            path: itemPath,
            before: beforeItem,
            after: afterItem,
            description: `Modified ${afterItem.type || 'item'} at position ${i}`
          })
        }
      }
    }
  }

  private getInitialChanges(document: ResumeDocument): VersionChange[] {
    return [{
      type: 'add',
      path: [],
      after: document,
      description: 'Initial document creation'
    }]
  }

  private calculateStats(changes: VersionChange[]): VersionCommit['metadata']['stats'] {
    return {
      additions: changes.filter(c => c.type === 'add').length,
      deletions: changes.filter(c => c.type === 'remove').length,
      modifications: changes.filter(c => c.type === 'modify').length
    }
  }

  private createInverseChanges(changes: VersionChange[]): VersionChange[] {
    return changes.map(change => {
      switch (change.type) {
        case 'add':
          return {
            type: 'remove' as const,
            path: change.path,
            before: change.after,
            description: `Revert: ${change.description}`
          }
        case 'remove':
          return {
            type: 'add' as const,
            path: change.path,
            after: change.before,
            description: `Revert: ${change.description}`
          }
        case 'modify':
          return {
            type: 'modify' as const,
            path: change.path,
            before: change.after,
            after: change.before,
            description: `Revert: ${change.description}`
          }
      }
    })
  }

  private applyChanges(document: ResumeDocument, changes: VersionChange[]): ResumeDocument {
    let result = this.deepClone(document)
    
    for (const change of changes) {
      result = this.applyChange(result, change)
    }
    
    return result
  }

  private applyChange(document: ResumeDocument, change: VersionChange): ResumeDocument {
    // Simplified change application - in production, implement proper path resolution
    return document
  }

  private cleanupHistory(): void {
    if (this.graph.commits.size > this.maxHistorySize) {
      // Remove oldest commits (simplified cleanup)
      const commits = Array.from(this.graph.commits.values())
        .sort((a, b) => b.timestamp - a.timestamp)
      
      const toKeep = commits.slice(0, this.maxHistorySize)
      const toRemove = commits.slice(this.maxHistorySize)
      
      this.graph.commits.clear()
      toKeep.forEach(commit => this.graph.commits.set(commit.id, commit))
    }
  }

  private deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj))
  }
}
