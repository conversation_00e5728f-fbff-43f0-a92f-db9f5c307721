// Performance monitoring and optimization utilities

interface PerformanceMetrics {
  lcp: number // Largest Contentful Paint
  fid: number // First Input Delay
  cls: number // Cumulative Layout Shift
  fcp: number // First Contentful Paint
  ttfb: number // Time to First Byte
  bundleSize: number
  memoryUsage: number
}

interface PerformanceBudget {
  lcp: number // 2.5s
  fid: number // 100ms
  cls: number // 0.1
  fcp: number // 1.8s
  ttfb: number // 600ms
  bundleSize: number // 150kB
  memoryUsage: number // 50MB
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Partial<PerformanceMetrics> = {}
  private budget: PerformanceBudget = {
    lcp: 2500,
    fid: 100,
    cls: 0.1,
    fcp: 1800,
    ttfb: 600,
    bundleSize: 150 * 1024, // 150kB
    memoryUsage: 50 * 1024 * 1024, // 50MB
  }

  private constructor() {
    this.initializeMonitoring()
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  private initializeMonitoring(): void {
    if (typeof window === 'undefined') return

    // Monitor Core Web Vitals
    this.monitorLCP()
    this.monitorFID()
    this.monitorCLS()
    this.monitorFCP()
    this.monitorTTFB()
    this.monitorBundleSize()
    this.monitorMemoryUsage()

    // Report metrics periodically
    setInterval(() => this.reportMetrics(), 30000) // Every 30 seconds
  }

  private monitorLCP(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as any
        this.metrics.lcp = lastEntry.startTime
        this.checkBudget('lcp', lastEntry.startTime)
      })
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
    }
  }

  private monitorFID(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          this.metrics.fid = entry.processingStart - entry.startTime
          this.checkBudget('fid', entry.processingStart - entry.startTime)
        })
      })
      observer.observe({ entryTypes: ['first-input'] })
    }
  }

  private monitorCLS(): void {
    if ('PerformanceObserver' in window) {
      let clsValue = 0
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
            this.metrics.cls = clsValue
            this.checkBudget('cls', clsValue)
          }
        })
      })
      observer.observe({ entryTypes: ['layout-shift'] })
    }
  }

  private monitorFCP(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.fcp = entry.startTime
            this.checkBudget('fcp', entry.startTime)
          }
        })
      })
      observer.observe({ entryTypes: ['paint'] })
    }
  }

  private monitorTTFB(): void {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const navigationEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[]
      if (navigationEntries.length > 0) {
        const ttfb = navigationEntries[0].responseStart - navigationEntries[0].requestStart
        this.metrics.ttfb = ttfb
        this.checkBudget('ttfb', ttfb)
      }
    }
  }

  private monitorBundleSize(): void {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
      let totalSize = 0
      
      resourceEntries.forEach((entry) => {
        if (entry.name.includes('.js') || entry.name.includes('.css')) {
          totalSize += entry.transferSize || 0
        }
      })
      
      this.metrics.bundleSize = totalSize
      this.checkBudget('bundleSize', totalSize)
    }
  }

  private monitorMemoryUsage(): void {
    if ('performance' in window && 'memory' in (performance as any)) {
      const memory = (performance as any).memory
      this.metrics.memoryUsage = memory.usedJSHeapSize
      this.checkBudget('memoryUsage', memory.usedJSHeapSize)
    }
  }

  private checkBudget(metric: keyof PerformanceBudget, value: number): void {
    const budgetValue = this.budget[metric]
    if (value > budgetValue) {
      console.warn(`Performance budget exceeded for ${metric}: ${value} > ${budgetValue}`)
      this.reportBudgetViolation(metric, value, budgetValue)
    }
  }

  private reportBudgetViolation(metric: string, actual: number, budget: number): void {
    // In production, send to analytics service
    if (process.env.NODE_ENV === 'production') {
      // Analytics.track('performance_budget_violation', {
      //   metric,
      //   actual,
      //   budget,
      //   url: window.location.href,
      //   userAgent: navigator.userAgent,
      // })
    }
  }

  private reportMetrics(): void {
    if (process.env.NODE_ENV === 'production') {
      // Send metrics to monitoring service
      // Analytics.track('performance_metrics', this.metrics)
    }
    
    console.log('Performance Metrics:', this.metrics)
  }

  getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics }
  }

  getBudget(): PerformanceBudget {
    return { ...this.budget }
  }

  setBudget(newBudget: Partial<PerformanceBudget>): void {
    this.budget = { ...this.budget, ...newBudget }
  }
}

// React performance utilities
export const withPerformanceTracking = <P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) => {
  return React.memo((props: P) => {
    const renderStart = performance.now()
    
    React.useEffect(() => {
      const renderEnd = performance.now()
      const renderTime = renderEnd - renderStart
      
      if (renderTime > 16) { // More than one frame (60fps)
        console.warn(`Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`)
      }
    })
    
    return <Component {...props} />
  })
}

// Lazy loading utilities
export const createLazyComponent = <T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) => {
  const LazyComponent = React.lazy(importFn)
  
  return (props: React.ComponentProps<T>) => (
    <React.Suspense fallback={fallback ? <fallback /> : <div>Loading...</div>}>
      <LazyComponent {...props} />
    </React.Suspense>
  )
}

// Bundle size analysis
export const analyzeBundleSize = (): void => {
  if ('performance' in window) {
    const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
    const bundleAnalysis = {
      totalSize: 0,
      jsSize: 0,
      cssSize: 0,
      imageSize: 0,
      fontSize: 0,
      resources: [] as Array<{ name: string; size: number; type: string }>,
    }
    
    resourceEntries.forEach((entry) => {
      const size = entry.transferSize || 0
      const name = entry.name
      let type = 'other'
      
      if (name.includes('.js')) {
        type = 'javascript'
        bundleAnalysis.jsSize += size
      } else if (name.includes('.css')) {
        type = 'css'
        bundleAnalysis.cssSize += size
      } else if (name.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) {
        type = 'image'
        bundleAnalysis.imageSize += size
      } else if (name.match(/\.(woff|woff2|ttf|otf)$/)) {
        type = 'font'
        bundleAnalysis.fontSize += size
      }
      
      bundleAnalysis.totalSize += size
      bundleAnalysis.resources.push({ name, size, type })
    })
    
    // Sort by size descending
    bundleAnalysis.resources.sort((a, b) => b.size - a.size)
    
    console.table(bundleAnalysis.resources.slice(0, 20)) // Top 20 largest resources
    console.log('Bundle Analysis:', {
      totalSize: `${(bundleAnalysis.totalSize / 1024).toFixed(2)} KB`,
      jsSize: `${(bundleAnalysis.jsSize / 1024).toFixed(2)} KB`,
      cssSize: `${(bundleAnalysis.cssSize / 1024).toFixed(2)} KB`,
      imageSize: `${(bundleAnalysis.imageSize / 1024).toFixed(2)} KB`,
      fontSize: `${(bundleAnalysis.fontSize / 1024).toFixed(2)} KB`,
    })
  }
}

// Memory leak detection
export const detectMemoryLeaks = (): void => {
  if ('performance' in window && 'memory' in (performance as any)) {
    const memory = (performance as any).memory
    const initialMemory = memory.usedJSHeapSize
    
    setTimeout(() => {
      const currentMemory = memory.usedJSHeapSize
      const memoryIncrease = currentMemory - initialMemory
      
      if (memoryIncrease > 10 * 1024 * 1024) { // 10MB increase
        console.warn(`Potential memory leak detected: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB increase`)
      }
    }, 60000) // Check after 1 minute
  }
}

// Performance optimization hooks
export const usePerformanceOptimization = () => {
  const [isVisible, setIsVisible] = React.useState(false)
  const [isIdle, setIsIdle] = React.useState(false)
  
  React.useEffect(() => {
    // Intersection Observer for visibility
    const observer = new IntersectionObserver(
      ([entry]) => setIsVisible(entry.isIntersecting),
      { threshold: 0.1 }
    )
    
    // Idle callback for non-critical work
    const idleCallback = requestIdleCallback(() => setIsIdle(true))
    
    return () => {
      observer.disconnect()
      cancelIdleCallback(idleCallback)
    }
  }, [])
  
  return { isVisible, isIdle }
}

// Initialize performance monitoring
export const initializePerformanceMonitoring = (): void => {
  PerformanceMonitor.getInstance()
  
  // Run bundle analysis in development
  if (process.env.NODE_ENV === 'development') {
    setTimeout(analyzeBundleSize, 2000)
    detectMemoryLeaks()
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance()
