import React from 'react'
import { RenderElementProps } from 'slate-react'
import { motion } from 'framer-motion'
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Globe, 
  Linkedin, 
  Github,
  Calendar,
  Building,
  GraduationCap,
  Code,
  Award
} from 'lucide-react'
import { cn } from '@/utils/cn'
import { 
  CustomElement, 
  ContactInfoElement, 
  ExperienceElement, 
  EducationElement,
  ProjectElement,
  SkillElement 
} from '@/types/resume'

interface ElementRendererProps extends RenderElementProps {
  element: CustomElement
}

export const ElementRenderer: React.FC<ElementRendererProps> = ({ 
  attributes, 
  children, 
  element 
}) => {
  const baseProps = {
    ...attributes,
    'data-element-id': element.id,
    'data-element-type': element.type,
  }

  switch (element.type) {
    case 'paragraph':
      return (
        <motion.p
          {...baseProps}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className={cn(
            'mb-4 text-editor-text leading-relaxed',
            element.align && `text-${element.align}`
          )}
        >
          {children}
        </motion.p>
      )

    case 'heading':
      const HeadingTag = `h${element.level}` as keyof JSX.IntrinsicElements
      return (
        <motion.div
          {...baseProps}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="mb-6"
        >
          <HeadingTag
            className={cn(
              'font-display font-semibold text-editor-text',
              element.level === 1 && 'text-4xl mb-2',
              element.level === 2 && 'text-3xl mb-2 border-b border-editor-border pb-2',
              element.level === 3 && 'text-2xl mb-2',
              element.level === 4 && 'text-xl mb-1',
              element.level === 5 && 'text-lg mb-1',
              element.level === 6 && 'text-base mb-1',
              element.align && `text-${element.align}`
            )}
          >
            {children}
          </HeadingTag>
        </motion.div>
      )

    case 'bullet-list':
      return (
        <motion.ul
          {...baseProps}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className={cn(
            'mb-4 space-y-2',
            element.style === 'disc' && 'list-disc list-inside',
            element.style === 'circle' && 'list-disc list-inside',
            element.style === 'square' && 'list-square list-inside',
            element.style === 'none' && 'list-none'
          )}
        >
          {children}
        </motion.ul>
      )

    case 'list-item':
      return (
        <motion.li
          {...baseProps}
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          className="text-editor-text leading-relaxed"
        >
          {children}
        </motion.li>
      )

    case 'contact-info':
      return <ContactInfoRenderer {...baseProps} element={element} children={children} />

    case 'section':
      return (
        <motion.section
          {...baseProps}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-display font-semibold text-editor-text border-b border-primary-200 pb-1">
              {element.title}
            </h2>
            {element.collapsible && (
              <button
                className="text-editor-text-muted hover:text-editor-text transition-colors"
                onClick={() => {
                  // Toggle collapsed state
                }}
              >
                {element.collapsed ? 'Expand' : 'Collapse'}
              </button>
            )}
          </div>
          {!element.collapsed && (
            <div className="space-y-4">
              {children}
            </div>
          )}
        </motion.section>
      )

    case 'experience':
      return <ExperienceRenderer {...baseProps} element={element} children={children} />

    case 'education':
      return <EducationRenderer {...baseProps} element={element} children={children} />

    case 'project':
      return <ProjectRenderer {...baseProps} element={element} children={children} />

    case 'skill':
      return <SkillRenderer {...baseProps} element={element} children={children} />

    default:
      return (
        <div {...baseProps} className="mb-4">
          {children}
        </div>
      )
  }
}

// Contact Info Component
const ContactInfoRenderer: React.FC<{
  element: ContactInfoElement
  children: React.ReactNode
  [key: string]: any
}> = ({ element, children, ...props }) => {
  const { fields } = element

  return (
    <motion.div
      {...props}
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="mb-8 p-6 bg-editor-surface rounded-lg border border-editor-border"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {fields.name && (
          <div className="flex items-center space-x-3">
            <User className="w-5 h-5 text-primary-600" />
            <span className="text-lg font-semibold text-editor-text">{fields.name}</span>
          </div>
        )}
        
        {fields.email && (
          <div className="flex items-center space-x-3">
            <Mail className="w-5 h-5 text-primary-600" />
            <a 
              href={`mailto:${fields.email}`}
              className="text-editor-text hover:text-primary-600 transition-colors"
            >
              {fields.email}
            </a>
          </div>
        )}
        
        {fields.phone && (
          <div className="flex items-center space-x-3">
            <Phone className="w-5 h-5 text-primary-600" />
            <a 
              href={`tel:${fields.phone}`}
              className="text-editor-text hover:text-primary-600 transition-colors"
            >
              {fields.phone}
            </a>
          </div>
        )}
        
        {fields.location && (
          <div className="flex items-center space-x-3">
            <MapPin className="w-5 h-5 text-primary-600" />
            <span className="text-editor-text">{fields.location}</span>
          </div>
        )}
        
        {fields.website && (
          <div className="flex items-center space-x-3">
            <Globe className="w-5 h-5 text-primary-600" />
            <a 
              href={fields.website}
              target="_blank"
              rel="noopener noreferrer"
              className="text-editor-text hover:text-primary-600 transition-colors"
            >
              {fields.website}
            </a>
          </div>
        )}
        
        {fields.linkedin && (
          <div className="flex items-center space-x-3">
            <Linkedin className="w-5 h-5 text-primary-600" />
            <a 
              href={fields.linkedin}
              target="_blank"
              rel="noopener noreferrer"
              className="text-editor-text hover:text-primary-600 transition-colors"
            >
              LinkedIn
            </a>
          </div>
        )}
        
        {fields.github && (
          <div className="flex items-center space-x-3">
            <Github className="w-5 h-5 text-primary-600" />
            <a 
              href={fields.github}
              target="_blank"
              rel="noopener noreferrer"
              className="text-editor-text hover:text-primary-600 transition-colors"
            >
              GitHub
            </a>
          </div>
        )}
      </div>
      {children}
    </motion.div>
  )
}

// Experience Component
const ExperienceRenderer: React.FC<{
  element: ExperienceElement
  children: React.ReactNode
  [key: string]: any
}> = ({ element, children, ...props }) => {
  return (
    <motion.div
      {...props}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="mb-6 p-4 border-l-4 border-primary-200 bg-editor-surface rounded-r-lg"
    >
      <div className="flex items-start justify-between mb-3">
        <div>
          <h3 className="text-xl font-semibold text-editor-text">{element.position}</h3>
          <div className="flex items-center space-x-2 text-editor-text-muted">
            <Building className="w-4 h-4" />
            <span className="font-medium">{element.company}</span>
            {element.location && (
              <>
                <span>•</span>
                <span>{element.location}</span>
              </>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2 text-sm text-editor-text-muted">
          <Calendar className="w-4 h-4" />
          <span>
            {element.startDate} - {element.current ? 'Present' : element.endDate}
          </span>
        </div>
      </div>
      <div className="prose prose-sm max-w-none text-editor-text">
        {children}
      </div>
    </motion.div>
  )
}

// Education Component
const EducationRenderer: React.FC<{
  element: EducationElement
  children: React.ReactNode
  [key: string]: any
}> = ({ element, children, ...props }) => {
  return (
    <motion.div
      {...props}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="mb-6 p-4 border-l-4 border-success-200 bg-editor-surface rounded-r-lg"
    >
      <div className="flex items-start justify-between mb-3">
        <div>
          <h3 className="text-xl font-semibold text-editor-text">{element.degree}</h3>
          {element.field && (
            <p className="text-editor-text-muted">in {element.field}</p>
          )}
          <div className="flex items-center space-x-2 text-editor-text-muted">
            <GraduationCap className="w-4 h-4" />
            <span className="font-medium">{element.institution}</span>
          </div>
        </div>
        <div className="text-right">
          <div className="flex items-center space-x-2 text-sm text-editor-text-muted mb-1">
            <Calendar className="w-4 h-4" />
            <span>{element.startDate} - {element.endDate}</span>
          </div>
          {element.gpa && (
            <div className="text-sm text-editor-text-muted">
              GPA: {element.gpa}
            </div>
          )}
        </div>
      </div>
      {element.honors && element.honors.length > 0 && (
        <div className="mb-3">
          <div className="flex items-center space-x-2 text-sm text-editor-text-muted">
            <Award className="w-4 h-4" />
            <span>Honors: {element.honors.join(', ')}</span>
          </div>
        </div>
      )}
      <div className="prose prose-sm max-w-none text-editor-text">
        {children}
      </div>
    </motion.div>
  )
}

// Project Component
const ProjectRenderer: React.FC<{
  element: ProjectElement
  children: React.ReactNode
  [key: string]: any
}> = ({ element, children, ...props }) => {
  return (
    <motion.div
      {...props}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="mb-6 p-4 border-l-4 border-warning-200 bg-editor-surface rounded-r-lg"
    >
      <div className="flex items-start justify-between mb-3">
        <div>
          <h3 className="text-xl font-semibold text-editor-text">{element.name}</h3>
          <div className="flex items-center space-x-4 mt-2">
            {element.url && (
              <a 
                href={element.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-1 text-sm text-primary-600 hover:text-primary-700"
              >
                <Globe className="w-4 h-4" />
                <span>Live Demo</span>
              </a>
            )}
            {element.github && (
              <a 
                href={element.github}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-1 text-sm text-primary-600 hover:text-primary-700"
              >
                <Github className="w-4 h-4" />
                <span>Source</span>
              </a>
            )}
          </div>
        </div>
        {(element.startDate || element.endDate) && (
          <div className="flex items-center space-x-2 text-sm text-editor-text-muted">
            <Calendar className="w-4 h-4" />
            <span>
              {element.startDate} {element.endDate && `- ${element.endDate}`}
            </span>
          </div>
        )}
      </div>
      
      {element.technologies.length > 0 && (
        <div className="mb-3">
          <div className="flex items-center space-x-2 mb-2">
            <Code className="w-4 h-4 text-editor-text-muted" />
            <span className="text-sm font-medium text-editor-text-muted">Technologies:</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {element.technologies.map((tech, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>
      )}
      
      <div className="prose prose-sm max-w-none text-editor-text">
        {children}
      </div>
    </motion.div>
  )
}

// Skill Component
const SkillRenderer: React.FC<{
  element: SkillElement
  children: React.ReactNode
  [key: string]: any
}> = ({ element, children, ...props }) => {
  const levelColors = {
    beginner: 'bg-neutral-200',
    intermediate: 'bg-warning-200',
    advanced: 'bg-primary-200',
    expert: 'bg-success-200',
  }

  return (
    <motion.div
      {...props}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="mb-4 p-4 bg-editor-surface rounded-lg border border-editor-border"
    >
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-editor-text">{element.category}</h3>
        {element.level && (
          <span className={cn(
            'px-2 py-1 text-xs font-medium rounded-full',
            levelColors[element.level],
            'text-neutral-700'
          )}>
            {element.level}
          </span>
        )}
      </div>
      
      <div className="flex flex-wrap gap-2">
        {element.skills.map((skill, index) => (
          <motion.span
            key={index}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.05 }}
            className="px-3 py-1 bg-neutral-100 text-neutral-700 text-sm rounded-full border border-neutral-200"
          >
            {skill}
          </motion.span>
        ))}
      </div>
      
      {children}
    </motion.div>
  )
}
