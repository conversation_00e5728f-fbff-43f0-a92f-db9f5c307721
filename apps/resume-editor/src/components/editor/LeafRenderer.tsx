import React from 'react'
import { RenderLeafProps } from 'slate-react'
import { cn } from '@/utils/cn'
import { CustomText } from '@/types/resume'

interface LeafRendererProps extends RenderLeafProps {
  leaf: CustomText
}

export const LeafRenderer: React.FC<LeafRendererProps> = ({ 
  attributes, 
  children, 
  leaf 
}) => {
  let element = children

  // Apply text formatting
  if (leaf.bold) {
    element = <strong className="font-semibold">{element}</strong>
  }

  if (leaf.italic) {
    element = <em className="italic">{element}</em>
  }

  if (leaf.underline) {
    element = <u className="underline">{element}</u>
  }

  if (leaf.strikethrough) {
    element = <s className="line-through">{element}</s>
  }

  if (leaf.code) {
    element = (
      <code className="px-1.5 py-0.5 bg-neutral-100 text-neutral-800 rounded text-sm font-mono border">
        {element}
      </code>
    )
  }

  // Apply custom styling
  const style: React.CSSProperties = {}
  
  if (leaf.color) {
    style.color = leaf.color
  }
  
  if (leaf.backgroundColor) {
    style.backgroundColor = leaf.backgroundColor
  }
  
  if (leaf.fontSize) {
    style.fontSize = leaf.fontSize
  }
  
  if (leaf.fontFamily) {
    style.fontFamily = leaf.fontFamily
  }

  return (
    <span
      {...attributes}
      className={cn(
        'transition-colors duration-150',
        // Add any additional classes based on leaf properties
      )}
      style={Object.keys(style).length > 0 ? style : undefined}
    >
      {element}
    </span>
  )
}
