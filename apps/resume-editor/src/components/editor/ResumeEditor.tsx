import React, { useCallback, useMemo, useState } from 'react'
import { createEditor, Descendant, Editor, Transforms, Range, Point } from 'slate'
import { Slate, Editable, withReact, RenderElementProps, RenderLeafProps } from 'slate-react'
import { withHistory } from 'slate-history'
import { motion, AnimatePresence } from 'framer-motion'
import { useHotkeys } from 'react-hotkeys-hook'
import { cn } from '@/utils/cn'
import { CustomElement, CustomText } from '@/types/resume'
import { EditorToolbar } from './EditorToolbar'
import { ElementRenderer } from './ElementRenderer'
import { LeafRenderer } from './LeafRenderer'
import { withCustomElements } from './plugins/withCustomElements'
import { withShortcuts } from './plugins/withShortcuts'
import { withCollaboration } from './plugins/withCollaboration'
import { useEditorStore } from '@/stores/editorStore'
import { useCollaborationStore } from '@/stores/collaborationStore'

interface ResumeEditorProps {
  documentId: string
  initialValue?: Descendant[]
  readOnly?: boolean
  className?: string
  onSelectionChange?: (selection: Range | null) => void
  onContentChange?: (content: Descendant[]) => void
}

export const ResumeEditor: React.FC<ResumeEditorProps> = ({
  documentId,
  initialValue = [{ type: 'paragraph', id: 'initial', children: [{ text: '' }] }],
  readOnly = false,
  className,
  onSelectionChange,
  onContentChange,
}) => {
  const [value, setValue] = useState<Descendant[]>(initialValue)
  const [selection, setSelection] = useState<Range | null>(null)
  
  const { 
    currentDocument, 
    isLoading, 
    updateDocument,
    addToHistory 
  } = useEditorStore()
  
  const { 
    collaborators, 
    isConnected, 
    sendOperation,
    sendCursor 
  } = useCollaborationStore()

  // Create editor with plugins
  const editor = useMemo(() => {
    const baseEditor = withHistory(withReact(createEditor()))
    
    // Apply custom plugins
    return withCollaboration(
      withShortcuts(
        withCustomElements(baseEditor)
      ),
      documentId
    )
  }, [documentId])

  // Handle content changes
  const handleChange = useCallback((newValue: Descendant[]) => {
    setValue(newValue)
    
    // Save to store and trigger callbacks
    if (onContentChange) {
      onContentChange(newValue)
    }
    
    // Update document in store
    if (currentDocument) {
      updateDocument({
        ...currentDocument,
        content: newValue,
        updatedAt: new Date().toISOString(),
      })
    }
    
    // Add to version history
    addToHistory({
      content: newValue,
      timestamp: Date.now(),
      operation: 'content_change',
    })
  }, [currentDocument, updateDocument, addToHistory, onContentChange])

  // Handle selection changes
  const handleSelectionChange = useCallback((newSelection: Range | null) => {
    setSelection(newSelection)
    
    if (onSelectionChange) {
      onSelectionChange(newSelection)
    }
    
    // Send cursor position to collaborators
    if (newSelection && isConnected) {
      sendCursor({
        selection: newSelection,
        timestamp: Date.now(),
      })
    }
  }, [onSelectionChange, isConnected, sendCursor])

  // Keyboard shortcuts
  useHotkeys('mod+b', (e) => {
    e.preventDefault()
    toggleMark(editor, 'bold')
  }, { enableOnFormTags: true })

  useHotkeys('mod+i', (e) => {
    e.preventDefault()
    toggleMark(editor, 'italic')
  }, { enableOnFormTags: true })

  useHotkeys('mod+u', (e) => {
    e.preventDefault()
    toggleMark(editor, 'underline')
  }, { enableOnFormTags: true })

  useHotkeys('mod+z', (e) => {
    e.preventDefault()
    editor.undo()
  }, { enableOnFormTags: true })

  useHotkeys('mod+shift+z', (e) => {
    e.preventDefault()
    editor.redo()
  }, { enableOnFormTags: true })

  // Render element based on type
  const renderElement = useCallback((props: RenderElementProps) => {
    return <ElementRenderer {...props} />
  }, [])

  // Render leaf (text formatting)
  const renderLeaf = useCallback((props: RenderLeafProps) => {
    return <LeafRenderer {...props} />
  }, [])

  // Handle key down events
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    // Handle custom shortcuts and behaviors
    if (event.key === 'Enter') {
      const { selection } = editor
      if (selection && Range.isCollapsed(selection)) {
        const [match] = Editor.nodes(editor, {
          match: n => Editor.isBlock(editor, n),
        })
        
        if (match) {
          const [block] = match
          const element = block as CustomElement
          
          // Handle different element types on Enter
          if (element.type === 'heading') {
            event.preventDefault()
            Transforms.insertNodes(editor, {
              type: 'paragraph',
              id: `paragraph-${Date.now()}`,
              children: [{ text: '' }],
            })
            return
          }
        }
      }
    }
    
    // Handle Tab for indentation in lists
    if (event.key === 'Tab') {
      const { selection } = editor
      if (selection) {
        const [match] = Editor.nodes(editor, {
          match: n => (n as CustomElement).type === 'list-item',
        })
        
        if (match) {
          event.preventDefault()
          if (event.shiftKey) {
            // Decrease indentation
            Transforms.liftNodes(editor)
          } else {
            // Increase indentation
            Transforms.wrapNodes(editor, {
              type: 'bullet-list',
              id: `list-${Date.now()}`,
              children: [],
            })
          }
        }
      }
    }
  }, [editor])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className={cn(
      'resume-editor relative bg-editor-background',
      'border border-editor-border rounded-lg',
      'focus-within:ring-2 focus-within:ring-primary-500 focus-within:border-primary-500',
      className
    )}>
      {/* Collaboration indicators */}
      <AnimatePresence>
        {collaborators.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-2 right-2 flex -space-x-2 z-10"
          >
            {collaborators.slice(0, 5).map((collaborator) => (
              <motion.div
                key={collaborator.id}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
                className="relative"
              >
                <div
                  className="w-8 h-8 rounded-full border-2 border-white shadow-sm flex items-center justify-center text-xs font-medium text-white"
                  style={{ backgroundColor: collaborator.color }}
                  title={`${collaborator.name} (${collaborator.role})`}
                >
                  {collaborator.name.charAt(0).toUpperCase()}
                </div>
                {collaborator.isOnline && (
                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-success-500 border-2 border-white rounded-full"></div>
                )}
              </motion.div>
            ))}
            {collaborators.length > 5 && (
              <div className="w-8 h-8 rounded-full bg-neutral-200 border-2 border-white shadow-sm flex items-center justify-center text-xs font-medium text-neutral-600">
                +{collaborators.length - 5}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Connection status */}
      {!readOnly && (
        <div className="absolute top-2 left-2 z-10">
          <div className={cn(
            'flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium',
            isConnected 
              ? 'bg-success-50 text-success-700 border border-success-200'
              : 'bg-warning-50 text-warning-700 border border-warning-200'
          )}>
            <div className={cn(
              'w-2 h-2 rounded-full',
              isConnected ? 'bg-success-500' : 'bg-warning-500'
            )} />
            {isConnected ? 'Connected' : 'Reconnecting...'}
          </div>
        </div>
      )}

      <Slate
        editor={editor}
        initialValue={value}
        onChange={handleChange}
        onSelectionChange={handleSelectionChange}
      >
        {/* Toolbar */}
        {!readOnly && (
          <EditorToolbar 
            editor={editor}
            selection={selection}
          />
        )}

        {/* Editor content */}
        <div className="relative">
          <Editable
            renderElement={renderElement}
            renderLeaf={renderLeaf}
            onKeyDown={handleKeyDown}
            readOnly={readOnly}
            className={cn(
              'min-h-[600px] p-6 focus:outline-none',
              'prose prose-slate max-w-none',
              'prose-headings:font-display prose-headings:font-semibold',
              'prose-p:text-editor-text prose-p:leading-relaxed',
              readOnly && 'cursor-default'
            )}
            placeholder="Start writing your résumé..."
            spellCheck
            autoFocus={!readOnly}
          />

          {/* Collaboration cursors */}
          <AnimatePresence>
            {collaborators.map((collaborator) => (
              collaborator.cursor && (
                <motion.div
                  key={`cursor-${collaborator.id}`}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="absolute pointer-events-none z-20"
                  style={{
                    // Position would be calculated based on cursor position
                    // This is a simplified version
                    top: 0,
                    left: 0,
                  }}
                >
                  <div
                    className="w-0.5 h-5 animate-pulse"
                    style={{ backgroundColor: collaborator.color }}
                  />
                  <div
                    className="absolute top-0 left-1 px-2 py-1 rounded text-xs font-medium text-white whitespace-nowrap"
                    style={{ backgroundColor: collaborator.color }}
                  >
                    {collaborator.name}
                  </div>
                </motion.div>
              )
            ))}
          </AnimatePresence>
        </div>
      </Slate>
    </div>
  )
}

// Helper function to toggle text marks
const toggleMark = (editor: Editor, format: keyof CustomText) => {
  const isActive = isMarkActive(editor, format)
  
  if (isActive) {
    Editor.removeMark(editor, format)
  } else {
    Editor.addMark(editor, format, true)
  }
}

// Helper function to check if mark is active
const isMarkActive = (editor: Editor, format: keyof CustomText) => {
  const marks = Editor.marks(editor)
  return marks ? marks[format] === true : false
}
