/**
 * Tooltip Component
 * Accessible tooltip with positioning and animation
 */

import React, { useState, useRef, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

const tooltipVariants = cva(
  'absolute z-50 px-3 py-1.5 text-xs font-medium text-white bg-gray-900 rounded-md shadow-md pointer-events-none transition-opacity duration-200',
  {
    variants: {
      variant: {
        default: 'bg-gray-900 text-white',
        light: 'bg-white text-gray-900 border border-gray-200 shadow-lg',
        error: 'bg-red-600 text-white',
        warning: 'bg-yellow-600 text-white',
        success: 'bg-green-600 text-white',
      },
      size: {
        sm: 'px-2 py-1 text-xs',
        md: 'px-3 py-1.5 text-xs',
        lg: 'px-4 py-2 text-sm',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  }
)

type TooltipPosition = 'top' | 'bottom' | 'left' | 'right'

export interface TooltipProps
  extends VariantProps<typeof tooltipVariants> {
  content: React.ReactNode
  position?: TooltipPosition
  delay?: number
  disabled?: boolean
  children: React.ReactElement
  className?: string
  maxWidth?: number
}

export function Tooltip({
  content,
  position = 'top',
  delay = 500,
  disabled = false,
  variant,
  size,
  className,
  maxWidth = 200,
  children,
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 })
  const triggerRef = useRef<HTMLElement>(null)
  const tooltipRef = useRef<HTMLDivElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout>()

  const showTooltip = () => {
    if (disabled) return
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true)
      updatePosition()
    }, delay)
  }

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setIsVisible(false)
  }

  const updatePosition = () => {
    if (!triggerRef.current || !tooltipRef.current) return

    const triggerRect = triggerRef.current.getBoundingClientRect()
    const tooltipRect = tooltipRef.current.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const scrollX = window.scrollX
    const scrollY = window.scrollY

    let x = 0
    let y = 0

    switch (position) {
      case 'top':
        x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2
        y = triggerRect.top - tooltipRect.height - 8
        break
      case 'bottom':
        x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2
        y = triggerRect.bottom + 8
        break
      case 'left':
        x = triggerRect.left - tooltipRect.width - 8
        y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2
        break
      case 'right':
        x = triggerRect.right + 8
        y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2
        break
    }

    // Adjust for viewport boundaries
    if (x < 8) {
      x = 8
    } else if (x + tooltipRect.width > viewportWidth - 8) {
      x = viewportWidth - tooltipRect.width - 8
    }

    if (y < 8) {
      y = 8
    } else if (y + tooltipRect.height > viewportHeight - 8) {
      y = viewportHeight - tooltipRect.height - 8
    }

    setTooltipPosition({
      x: x + scrollX,
      y: y + scrollY,
    })
  }

  useEffect(() => {
    if (isVisible) {
      updatePosition()
      
      const handleScroll = () => updatePosition()
      const handleResize = () => updatePosition()
      
      window.addEventListener('scroll', handleScroll, true)
      window.addEventListener('resize', handleResize)
      
      return () => {
        window.removeEventListener('scroll', handleScroll, true)
        window.removeEventListener('resize', handleResize)
      }
    }
  }, [isVisible, position])

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  const clonedChild = React.cloneElement(children, {
    ref: triggerRef,
    onMouseEnter: (e: React.MouseEvent) => {
      showTooltip()
      children.props.onMouseEnter?.(e)
    },
    onMouseLeave: (e: React.MouseEvent) => {
      hideTooltip()
      children.props.onMouseLeave?.(e)
    },
    onFocus: (e: React.FocusEvent) => {
      showTooltip()
      children.props.onFocus?.(e)
    },
    onBlur: (e: React.FocusEvent) => {
      hideTooltip()
      children.props.onBlur?.(e)
    },
    'aria-describedby': isVisible ? 'tooltip' : undefined,
  })

  const tooltipElement = isVisible && content && (
    <div
      ref={tooltipRef}
      id="tooltip"
      role="tooltip"
      className={cn(
        tooltipVariants({ variant, size }),
        'opacity-0 animate-in fade-in-0 zoom-in-95',
        isVisible && 'opacity-100',
        className
      )}
      style={{
        left: tooltipPosition.x,
        top: tooltipPosition.y,
        maxWidth: maxWidth,
      }}
    >
      {content}
      
      {/* Arrow */}
      <div
        className={cn(
          'absolute w-2 h-2 rotate-45',
          variant === 'light' 
            ? 'bg-white border-l border-t border-gray-200' 
            : 'bg-gray-900',
          position === 'top' && 'bottom-[-4px] left-1/2 -translate-x-1/2',
          position === 'bottom' && 'top-[-4px] left-1/2 -translate-x-1/2',
          position === 'left' && 'right-[-4px] top-1/2 -translate-y-1/2',
          position === 'right' && 'left-[-4px] top-1/2 -translate-y-1/2'
        )}
      />
    </div>
  )

  return (
    <>
      {clonedChild}
      {tooltipElement && createPortal(tooltipElement, document.body)}
    </>
  )
}

// Hook for programmatic tooltip control
export function useTooltip() {
  const [isVisible, setIsVisible] = useState(false)
  const [content, setContent] = useState<React.ReactNode>(null)
  const [position, setPosition] = useState<TooltipPosition>('top')

  const show = (
    newContent: React.ReactNode,
    newPosition: TooltipPosition = 'top'
  ) => {
    setContent(newContent)
    setPosition(newPosition)
    setIsVisible(true)
  }

  const hide = () => {
    setIsVisible(false)
  }

  const toggle = (
    newContent?: React.ReactNode,
    newPosition: TooltipPosition = 'top'
  ) => {
    if (isVisible) {
      hide()
    } else {
      show(newContent || content, newPosition)
    }
  }

  return {
    isVisible,
    content,
    position,
    show,
    hide,
    toggle,
  }
}

Tooltip.displayName = 'Tooltip'
