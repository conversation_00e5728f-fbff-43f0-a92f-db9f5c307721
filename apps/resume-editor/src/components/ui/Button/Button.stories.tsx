import type { <PERSON>a, StoryObj } from '@storybook/react'
import { action } from '@storybook/addon-actions'
import { within, userEvent, expect } from '@storybook/test'
import { Button } from './Button'
import { 
  PlusIcon, 
  TrashIcon, 
  PencilIcon, 
  DownloadIcon,
  ShareIcon,
  EyeIcon 
} from '@heroicons/react/24/outline'

const meta: Meta<typeof Button> = {
  title: 'Components/UI/Button',
  component: Button,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
A versatile button component with multiple variants, sizes, and states.
Supports icons, loading states, and accessibility features.

## Features
- Multiple variants (primary, secondary, outline, ghost, destructive)
- Different sizes (sm, md, lg, xl)
- Icon support (leading and trailing)
- Loading states with spinner
- Disabled states
- Full accessibility support
- Keyboard navigation
- Focus management
        `,
      },
    },
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'outline', 'ghost', 'destructive'],
      description: 'Visual style variant of the button',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg', 'xl'],
      description: 'Size of the button',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the button is disabled',
    },
    loading: {
      control: 'boolean',
      description: 'Whether the button is in loading state',
    },
    fullWidth: {
      control: 'boolean',
      description: 'Whether the button takes full width of container',
    },
    children: {
      control: 'text',
      description: 'Button content',
    },
    onClick: {
      action: 'clicked',
      description: 'Click handler function',
    },
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof Button>

// Basic variants
export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Primary Button',
    onClick: action('primary-clicked'),
  },
}

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Secondary Button',
    onClick: action('secondary-clicked'),
  },
}

export const Outline: Story = {
  args: {
    variant: 'outline',
    children: 'Outline Button',
    onClick: action('outline-clicked'),
  },
}

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    children: 'Ghost Button',
    onClick: action('ghost-clicked'),
  },
}

export const Destructive: Story = {
  args: {
    variant: 'destructive',
    children: 'Delete Item',
    onClick: action('destructive-clicked'),
  },
}

// Sizes
export const Sizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Button size="sm" onClick={action('small-clicked')}>
        Small
      </Button>
      <Button size="md" onClick={action('medium-clicked')}>
        Medium
      </Button>
      <Button size="lg" onClick={action('large-clicked')}>
        Large
      </Button>
      <Button size="xl" onClick={action('extra-large-clicked')}>
        Extra Large
      </Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Different button sizes available',
      },
    },
  },
}

// With icons
export const WithIcons: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <Button 
          variant="primary" 
          leftIcon={<PlusIcon className="w-4 h-4" />}
          onClick={action('add-clicked')}
        >
          Add Item
        </Button>
        <Button 
          variant="secondary" 
          rightIcon={<DownloadIcon className="w-4 h-4" />}
          onClick={action('download-clicked')}
        >
          Download
        </Button>
      </div>
      <div className="flex items-center gap-4">
        <Button 
          variant="outline" 
          leftIcon={<PencilIcon className="w-4 h-4" />}
          onClick={action('edit-clicked')}
        >
          Edit
        </Button>
        <Button 
          variant="destructive" 
          leftIcon={<TrashIcon className="w-4 h-4" />}
          onClick={action('delete-clicked')}
        >
          Delete
        </Button>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Buttons with leading and trailing icons',
      },
    },
  },
}

// Icon only buttons
export const IconOnly: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Button 
        variant="primary" 
        size="sm"
        aria-label="Add item"
        onClick={action('icon-add-clicked')}
      >
        <PlusIcon className="w-4 h-4" />
      </Button>
      <Button 
        variant="secondary" 
        size="md"
        aria-label="Edit item"
        onClick={action('icon-edit-clicked')}
      >
        <PencilIcon className="w-5 h-5" />
      </Button>
      <Button 
        variant="outline" 
        size="lg"
        aria-label="Share item"
        onClick={action('icon-share-clicked')}
      >
        <ShareIcon className="w-6 h-6" />
      </Button>
      <Button 
        variant="ghost" 
        size="xl"
        aria-label="View item"
        onClick={action('icon-view-clicked')}
      >
        <EyeIcon className="w-7 h-7" />
      </Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Icon-only buttons with proper accessibility labels',
      },
    },
  },
}

// States
export const States: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <Button variant="primary" onClick={action('normal-clicked')}>
          Normal
        </Button>
        <Button variant="primary" disabled onClick={action('disabled-clicked')}>
          Disabled
        </Button>
        <Button variant="primary" loading onClick={action('loading-clicked')}>
          Loading
        </Button>
      </div>
      <div className="flex items-center gap-4">
        <Button variant="secondary" onClick={action('normal-secondary-clicked')}>
          Normal
        </Button>
        <Button variant="secondary" disabled onClick={action('disabled-secondary-clicked')}>
          Disabled
        </Button>
        <Button variant="secondary" loading onClick={action('loading-secondary-clicked')}>
          Loading
        </Button>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Different button states: normal, disabled, and loading',
      },
    },
  },
}

// Full width
export const FullWidth: Story = {
  render: () => (
    <div className="w-96 space-y-4">
      <Button variant="primary" fullWidth onClick={action('full-width-primary-clicked')}>
        Full Width Primary
      </Button>
      <Button variant="outline" fullWidth onClick={action('full-width-outline-clicked')}>
        Full Width Outline
      </Button>
      <Button 
        variant="secondary" 
        fullWidth 
        leftIcon={<DownloadIcon className="w-4 h-4" />}
        onClick={action('full-width-icon-clicked')}
      >
        Full Width with Icon
      </Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Buttons that take the full width of their container',
      },
    },
  },
}

// Interactive test
export const InteractiveTest: Story = {
  args: {
    variant: 'primary',
    children: 'Click me!',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    const button = canvas.getByRole('button', { name: /click me/i })
    
    // Test button is visible and enabled
    await expect(button).toBeInTheDocument()
    await expect(button).toBeEnabled()
    
    // Test click interaction
    await userEvent.click(button)
    
    // Test keyboard interaction
    await userEvent.tab()
    await userEvent.keyboard('{Enter}')
  },
  parameters: {
    docs: {
      description: {
        story: 'Interactive test demonstrating button behavior and accessibility',
      },
    },
  },
}

// Accessibility showcase
export const AccessibilityShowcase: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Keyboard Navigation</h3>
        <div className="flex gap-2">
          <Button variant="primary" onClick={action('tab-1-clicked')}>
            Tab 1
          </Button>
          <Button variant="secondary" onClick={action('tab-2-clicked')}>
            Tab 2
          </Button>
          <Button variant="outline" onClick={action('tab-3-clicked')}>
            Tab 3
          </Button>
        </div>
      </div>
      
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Screen Reader Support</h3>
        <div className="flex gap-2">
          <Button 
            variant="primary"
            aria-label="Save document to cloud storage"
            onClick={action('save-clicked')}
          >
            Save
          </Button>
          <Button 
            variant="destructive"
            aria-label="Permanently delete this item"
            onClick={action('delete-clicked')}
          >
            <TrashIcon className="w-4 h-4" />
          </Button>
        </div>
      </div>
      
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Loading States</h3>
        <div className="flex gap-2">
          <Button 
            variant="primary" 
            loading 
            aria-label="Saving document..."
            onClick={action('saving-clicked')}
          >
            Saving...
          </Button>
          <Button 
            variant="secondary" 
            loading 
            disabled
            aria-label="Processing request..."
            onClick={action('processing-clicked')}
          >
            Processing
          </Button>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Accessibility features including keyboard navigation, screen reader support, and loading states',
      },
    },
  },
}

// Design system showcase
export const DesignSystemShowcase: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Primary Actions</h3>
        <div className="flex gap-4">
          <Button variant="primary" onClick={action('create-clicked')}>
            Create Resume
          </Button>
          <Button variant="primary" leftIcon={<DownloadIcon className="w-4 h-4" />} onClick={action('export-clicked')}>
            Export PDF
          </Button>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Secondary Actions</h3>
        <div className="flex gap-4">
          <Button variant="secondary" onClick={action('preview-clicked')}>
            Preview
          </Button>
          <Button variant="outline" onClick={action('share-clicked')}>
            Share
          </Button>
          <Button variant="ghost" onClick={action('cancel-clicked')}>
            Cancel
          </Button>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">Destructive Actions</h3>
        <div className="flex gap-4">
          <Button variant="destructive" leftIcon={<TrashIcon className="w-4 h-4" />} onClick={action('delete-resume-clicked')}>
            Delete Resume
          </Button>
          <Button variant="outline" className="text-red-600 border-red-600 hover:bg-red-50" onClick={action('clear-clicked')}>
            Clear All
          </Button>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Design system usage patterns for different types of actions',
      },
    },
  },
}
