import React, { useState, useCallback } from 'react'
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  DragOverlay,
  DropAnimation,
  defaultDropAnimationSideEffects,
  closestCenter,
  pointerWithin,
  rectIntersection,
  getFirstCollision,
  UniqueIdentifier,
  useSensor,
  useSensors,
  PointerSensor,
  KeyboardSensor,
} from '@dnd-kit/core'
import {
  SortableContext,
  verticalListSortingStrategy,
  horizontalListSortingStrategy,
  rectSortingStrategy,
  sortableKeyboardCoordinates,
} from '@dnd-kit/sortable'
import { motion, AnimatePresence } from 'framer-motion'
import { Editor, Transforms, Path, Node } from 'slate'
import { ReactEditor } from 'slate-react'
import { CustomElement } from '@/types/resume'
import { DraggableElement } from './DraggableElement'
import { DroppableSection } from './DroppableSection'

interface DragDropProviderProps {
  children: React.ReactNode
  editor: Editor
  onReorder?: (sourceId: string, targetId: string, position: 'before' | 'after') => void
}

interface DragState {
  activeId: UniqueIdentifier | null
  activeElement: CustomElement | null
  overId: UniqueIdentifier | null
}

const dropAnimation: DropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: '0.5',
      },
    },
  }),
}

export const DragDropProvider: React.FC<DragDropProviderProps> = ({
  children,
  editor,
  onReorder,
}) => {
  const [dragState, setDragState] = useState<DragState>({
    activeId: null,
    activeElement: null,
    overId: null,
  })

  // Configure sensors for better touch and keyboard support
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px movement required to start drag
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // Custom collision detection for better performance
  const collisionDetection = useCallback((args: any) => {
    // First, let's see if there are any collisions with the pointer
    const pointerCollisions = pointerWithin(args)
    
    if (pointerCollisions.length > 0) {
      return pointerCollisions
    }

    // If there are no pointer collisions, use rectangle intersection
    const intersectionCollisions = rectIntersection(args)
    
    if (intersectionCollisions.length > 0) {
      return intersectionCollisions
    }

    // Fallback to closest center
    return closestCenter(args)
  }, [])

  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event
    
    // Find the element being dragged
    const elementPath = ReactEditor.findPath(editor, { id: active.id } as any)
    const [element] = Editor.node(editor, elementPath)
    
    setDragState({
      activeId: active.id,
      activeElement: element as CustomElement,
      overId: null,
    })

    // Add visual feedback
    document.body.style.cursor = 'grabbing'
  }, [editor])

  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { over } = event
    
    setDragState(prev => ({
      ...prev,
      overId: over?.id || null,
    }))
  }, [])

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event
    
    // Reset drag state
    setDragState({
      activeId: null,
      activeElement: null,
      overId: null,
    })
    
    // Reset cursor
    document.body.style.cursor = ''

    if (!over || active.id === over.id) {
      return
    }

    try {
      // Find source and target paths
      const sourcePath = ReactEditor.findPath(editor, { id: active.id } as any)
      const targetPath = ReactEditor.findPath(editor, { id: over.id } as any)
      
      if (!sourcePath || !targetPath) {
        return
      }

      // Determine if we're moving before or after the target
      const sourceIndex = sourcePath[sourcePath.length - 1]
      const targetIndex = targetPath[targetPath.length - 1]
      const position = sourceIndex < targetIndex ? 'after' : 'before'

      // Perform the move operation
      Editor.withoutNormalizing(editor, () => {
        // Remove the source element
        const [sourceNode] = Editor.node(editor, sourcePath)
        Transforms.removeNodes(editor, { at: sourcePath })
        
        // Calculate new target path after removal
        let newTargetPath = targetPath
        if (Path.isBefore(sourcePath, targetPath)) {
          newTargetPath = Path.previous(targetPath)
        }
        
        // Insert at new position
        const insertPath = position === 'after' 
          ? Path.next(newTargetPath)
          : newTargetPath
          
        Transforms.insertNodes(editor, sourceNode, { at: insertPath })
      })

      // Trigger callback
      if (onReorder) {
        onReorder(active.id.toString(), over.id.toString(), position)
      }

    } catch (error) {
      console.error('Error during drag and drop:', error)
    }
  }, [editor, onReorder])

  const handleDragCancel = useCallback(() => {
    setDragState({
      activeId: null,
      activeElement: null,
      overId: null,
    })
    
    document.body.style.cursor = ''
  }, [])

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={collisionDetection}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
      onDragCancel={handleDragCancel}
    >
      {children}
      
      {/* Drag overlay for smooth animations */}
      <DragOverlay dropAnimation={dropAnimation}>
        <AnimatePresence>
          {dragState.activeElement && (
            <motion.div
              initial={{ scale: 1, rotate: 0 }}
              animate={{ scale: 1.05, rotate: 2 }}
              exit={{ scale: 1, rotate: 0 }}
              className="bg-white shadow-lg rounded-lg border-2 border-primary-300 opacity-90"
              style={{
                width: 'auto',
                maxWidth: '400px',
              }}
            >
              <DragPreview element={dragState.activeElement} />
            </motion.div>
          )}
        </AnimatePresence>
      </DragOverlay>
    </DndContext>
  )
}

// Preview component for drag overlay
const DragPreview: React.FC<{ element: CustomElement }> = ({ element }) => {
  const getPreviewContent = () => {
    switch (element.type) {
      case 'heading':
        return (
          <div className="p-4">
            <div className="text-lg font-semibold text-gray-800">
              📝 Heading {(element as any).level}
            </div>
            <div className="text-sm text-gray-500 mt-1">
              Drag to reorder sections
            </div>
          </div>
        )
      
      case 'experience':
        const exp = element as any
        return (
          <div className="p-4">
            <div className="text-lg font-semibold text-gray-800">
              💼 {exp.position || 'Experience'}
            </div>
            <div className="text-sm text-gray-500 mt-1">
              {exp.company || 'Company'} • {exp.startDate || 'Date'}
            </div>
          </div>
        )
      
      case 'education':
        const edu = element as any
        return (
          <div className="p-4">
            <div className="text-lg font-semibold text-gray-800">
              🎓 {edu.degree || 'Education'}
            </div>
            <div className="text-sm text-gray-500 mt-1">
              {edu.institution || 'Institution'} • {edu.startDate || 'Date'}
            </div>
          </div>
        )
      
      case 'project':
        const proj = element as any
        return (
          <div className="p-4">
            <div className="text-lg font-semibold text-gray-800">
              🚀 {proj.name || 'Project'}
            </div>
            <div className="text-sm text-gray-500 mt-1">
              {proj.technologies?.slice(0, 3).join(', ') || 'Technologies'}
            </div>
          </div>
        )
      
      case 'skill':
        const skill = element as any
        return (
          <div className="p-4">
            <div className="text-lg font-semibold text-gray-800">
              ⚡ {skill.category || 'Skills'}
            </div>
            <div className="text-sm text-gray-500 mt-1">
              {skill.skills?.slice(0, 3).join(', ') || 'Skill list'}
            </div>
          </div>
        )
      
      case 'section':
        const section = element as any
        return (
          <div className="p-4">
            <div className="text-lg font-semibold text-gray-800">
              📋 {section.title || 'Section'}
            </div>
            <div className="text-sm text-gray-500 mt-1">
              Drag to reorder sections
            </div>
          </div>
        )
      
      default:
        return (
          <div className="p-4">
            <div className="text-lg font-semibold text-gray-800">
              📄 {element.type}
            </div>
            <div className="text-sm text-gray-500 mt-1">
              Drag to reorder
            </div>
          </div>
        )
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200">
      {getPreviewContent()}
    </div>
  )
}

// Hook for sortable context
export const useSortableContext = (items: string[], strategy: 'vertical' | 'horizontal' | 'grid' = 'vertical') => {
  const sortingStrategy = {
    vertical: verticalListSortingStrategy,
    horizontal: horizontalListSortingStrategy,
    grid: rectSortingStrategy,
  }[strategy]

  return {
    SortableContext: ({ children }: { children: React.ReactNode }) => (
      <SortableContext items={items} strategy={sortingStrategy}>
        {children}
      </SortableContext>
    ),
  }
}
