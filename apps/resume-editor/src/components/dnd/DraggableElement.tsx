import React, { forwardRef } from 'react'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { motion } from 'framer-motion'
import { GripVertical, Eye, EyeOff, MoreVertical, Copy, Trash2 } from 'lucide-react'
import { cn } from '@/utils/cn'
import { CustomElement } from '@/types/resume'

interface DraggableElementProps {
  id: string
  element: CustomElement
  children: React.ReactNode
  disabled?: boolean
  className?: string
  onToggleVisibility?: (id: string) => void
  onDuplicate?: (id: string) => void
  onDelete?: (id: string) => void
}

export const DraggableElement = forwardRef<HTMLDivElement, DraggableElementProps>(
  ({ id, element, children, disabled = false, className, onToggleVisibility, onDuplicate, onDelete }, ref) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging,
      isOver,
    } = useSortable({
      id,
      disabled,
      data: {
        type: 'element',
        element,
      },
    })

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
    }

    const [isHovered, setIsHovered] = React.useState(false)
    const [showActions, setShowActions] = React.useState(false)

    return (
      <motion.div
        ref={setNodeRef}
        style={style}
        className={cn(
          'group relative',
          'transition-all duration-200 ease-out',
          isDragging && 'opacity-50 scale-105 rotate-1 z-50',
          isOver && 'ring-2 ring-primary-400 ring-opacity-50',
          className
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => {
          setIsHovered(false)
          setShowActions(false)
        }}
        layout
        layoutId={id}
      >
        {/* Drag handle and controls */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ 
            opacity: isHovered || isDragging ? 1 : 0,
            x: isHovered || isDragging ? 0 : -20
          }}
          transition={{ duration: 0.2 }}
          className="absolute left-0 top-0 -ml-12 flex flex-col items-center space-y-1 z-10"
        >
          {/* Drag handle */}
          <button
            {...attributes}
            {...listeners}
            className={cn(
              'flex items-center justify-center w-8 h-8',
              'bg-white border border-gray-200 rounded-md shadow-sm',
              'hover:bg-gray-50 hover:border-gray-300',
              'focus:outline-none focus:ring-2 focus:ring-primary-500',
              'cursor-grab active:cursor-grabbing',
              'transition-colors duration-150',
              disabled && 'opacity-50 cursor-not-allowed'
            )}
            disabled={disabled}
            title="Drag to reorder"
          >
            <GripVertical className="w-4 h-4 text-gray-400" />
          </button>

          {/* Actions menu */}
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              className={cn(
                'flex items-center justify-center w-8 h-8',
                'bg-white border border-gray-200 rounded-md shadow-sm',
                'hover:bg-gray-50 hover:border-gray-300',
                'focus:outline-none focus:ring-2 focus:ring-primary-500',
                'transition-colors duration-150'
              )}
              title="More actions"
            >
              <MoreVertical className="w-4 h-4 text-gray-400" />
            </button>

            {/* Actions dropdown */}
            {showActions && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -10 }}
                className={cn(
                  'absolute left-0 top-full mt-1 w-40',
                  'bg-white border border-gray-200 rounded-md shadow-lg',
                  'py-1 z-20'
                )}
              >
                <button
                  onClick={() => onToggleVisibility?.(id)}
                  className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Toggle Visibility
                </button>
                
                <button
                  onClick={() => onDuplicate?.(id)}
                  className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Duplicate
                </button>
                
                <hr className="my-1 border-gray-100" />
                
                <button
                  onClick={() => onDelete?.(id)}
                  className="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </button>
              </motion.div>
            )}
          </div>
        </motion.div>

        {/* Drop indicator */}
        {isOver && (
          <motion.div
            initial={{ scaleY: 0 }}
            animate={{ scaleY: 1 }}
            className="absolute inset-0 bg-primary-100 border-2 border-primary-400 border-dashed rounded-lg -z-10"
          />
        )}

        {/* Element content */}
        <div
          className={cn(
            'relative',
            'transition-all duration-200',
            isHovered && 'ring-1 ring-gray-200 rounded-lg',
            isDragging && 'pointer-events-none'
          )}
        >
          {children}
        </div>

        {/* Element type indicator */}
        {isHovered && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="absolute top-2 right-2 z-10"
          >
            <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">
              {getElementTypeLabel(element.type)}
            </span>
          </motion.div>
        )}
      </motion.div>
    )
  }
)

DraggableElement.displayName = 'DraggableElement'

// Helper function to get user-friendly element type labels
const getElementTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    'paragraph': 'Text',
    'heading': 'Heading',
    'bullet-list': 'List',
    'contact-info': 'Contact',
    'section': 'Section',
    'experience': 'Experience',
    'education': 'Education',
    'project': 'Project',
    'skill': 'Skills',
  }
  
  return labels[type] || type
}

// Hook for drag and drop functionality
export const useDragAndDrop = () => {
  const [draggedElement, setDraggedElement] = React.useState<CustomElement | null>(null)
  const [dropTarget, setDropTarget] = React.useState<string | null>(null)

  const handleDragStart = React.useCallback((element: CustomElement) => {
    setDraggedElement(element)
  }, [])

  const handleDragEnd = React.useCallback(() => {
    setDraggedElement(null)
    setDropTarget(null)
  }, [])

  const handleDragOver = React.useCallback((targetId: string) => {
    setDropTarget(targetId)
  }, [])

  return {
    draggedElement,
    dropTarget,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
  }
}

// Performance optimization: Memoize the component
export default React.memo(DraggableElement)
