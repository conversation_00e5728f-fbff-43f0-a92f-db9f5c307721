import { z } from 'zod'

// Template DSL Schema Definition
export const TemplateVariableSchema = z.object({
  name: z.string(),
  type: z.enum(['text', 'date', 'list', 'object', 'boolean', 'number']),
  required: z.boolean().default(false),
  default: z.any().optional(),
  description: z.string().optional(),
  validation: z.object({
    minLength: z.number().optional(),
    maxLength: z.number().optional(),
    pattern: z.string().optional(),
    format: z.enum(['email', 'phone', 'url', 'date']).optional(),
  }).optional(),
})

export const TemplateStyleSchema = z.object({
  selector: z.string(),
  properties: z.record(z.string(), z.string()),
  responsive: z.object({
    mobile: z.record(z.string(), z.string()).optional(),
    tablet: z.record(z.string(), z.string()).optional(),
    desktop: z.record(z.string(), z.string()).optional(),
  }).optional(),
})

export const TemplateLayoutSchema = z.object({
  type: z.enum(['single-column', 'two-column', 'three-column', 'sidebar', 'grid']),
  columns: z.array(z.object({
    width: z.string(), // CSS width value
    content: z.array(z.string()), // Section IDs
    order: z.number().optional(),
  })),
  spacing: z.object({
    margin: z.string().default('1rem'),
    padding: z.string().default('1rem'),
    gap: z.string().default('1rem'),
  }),
  breakpoints: z.object({
    mobile: z.string().default('768px'),
    tablet: z.string().default('1024px'),
    desktop: z.string().default('1200px'),
  }),
})

export const TemplateSectionSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum([
    'header', 'contact', 'summary', 'experience', 'education', 
    'skills', 'projects', 'certifications', 'languages', 'custom'
  ]),
  required: z.boolean().default(false),
  order: z.number(),
  template: z.string(), // HTML template with variables
  styles: z.array(TemplateStyleSchema).default([]),
  variables: z.array(TemplateVariableSchema).default([]),
  conditions: z.object({
    show: z.string().optional(), // JavaScript expression
    hide: z.string().optional(), // JavaScript expression
  }).optional(),
})

export const TemplateThemeSchema = z.object({
  name: z.string(),
  colors: z.object({
    primary: z.string(),
    secondary: z.string(),
    accent: z.string(),
    text: z.string(),
    textLight: z.string(),
    background: z.string(),
    surface: z.string(),
    border: z.string(),
    success: z.string(),
    warning: z.string(),
    error: z.string(),
  }),
  typography: z.object({
    fontFamily: z.object({
      primary: z.string(),
      secondary: z.string().optional(),
      monospace: z.string().optional(),
    }),
    fontSize: z.object({
      xs: z.string(),
      sm: z.string(),
      base: z.string(),
      lg: z.string(),
      xl: z.string(),
      '2xl': z.string(),
      '3xl': z.string(),
      '4xl': z.string(),
    }),
    fontWeight: z.object({
      normal: z.string(),
      medium: z.string(),
      semibold: z.string(),
      bold: z.string(),
    }),
    lineHeight: z.object({
      tight: z.string(),
      normal: z.string(),
      relaxed: z.string(),
    }),
  }),
  spacing: z.object({
    xs: z.string(),
    sm: z.string(),
    md: z.string(),
    lg: z.string(),
    xl: z.string(),
    '2xl': z.string(),
    '3xl': z.string(),
  }),
  borderRadius: z.object({
    none: z.string(),
    sm: z.string(),
    md: z.string(),
    lg: z.string(),
    full: z.string(),
  }),
  shadows: z.object({
    sm: z.string(),
    md: z.string(),
    lg: z.string(),
    xl: z.string(),
  }),
})

export const TemplateConfigSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  version: z.string(),
  author: z.object({
    name: z.string(),
    email: z.string().optional(),
    url: z.string().optional(),
  }),
  category: z.enum(['modern', 'classic', 'creative', 'minimal', 'academic', 'executive']),
  tags: z.array(z.string()).default([]),
  
  // Template structure
  layout: TemplateLayoutSchema,
  sections: z.array(TemplateSectionSchema),
  theme: TemplateThemeSchema,
  
  // Global template settings
  settings: z.object({
    pageSize: z.enum(['A4', 'Letter', 'Legal']).default('A4'),
    orientation: z.enum(['portrait', 'landscape']).default('portrait'),
    margins: z.object({
      top: z.string().default('1in'),
      right: z.string().default('1in'),
      bottom: z.string().default('1in'),
      left: z.string().default('1in'),
    }),
    dpi: z.number().default(300),
    quality: z.enum(['draft', 'standard', 'high']).default('standard'),
  }),
  
  // ATS optimization
  ats: z.object({
    friendly: z.boolean().default(true),
    keywords: z.array(z.string()).default([]),
    structure: z.object({
      useSemanticHTML: z.boolean().default(true),
      includeMetadata: z.boolean().default(true),
      optimizeForParsing: z.boolean().default(true),
    }),
  }),
  
  // Accessibility
  accessibility: z.object({
    wcagLevel: z.enum(['A', 'AA', 'AAA']).default('AA'),
    highContrast: z.boolean().default(false),
    screenReaderOptimized: z.boolean().default(true),
    keyboardNavigation: z.boolean().default(true),
  }),
  
  // Internationalization
  i18n: z.object({
    defaultLanguage: z.string().default('en'),
    supportedLanguages: z.array(z.string()).default(['en']),
    rtlSupport: z.boolean().default(false),
    dateFormat: z.string().default('MM/yyyy'),
    numberFormat: z.string().default('en-US'),
  }),
  
  // Preview and export
  preview: z.object({
    thumbnail: z.string().optional(), // Base64 or URL
    screenshots: z.array(z.string()).default([]),
    demoData: z.record(z.string(), z.any()).optional(),
  }),
  
  // Metadata
  metadata: z.object({
    createdAt: z.string(),
    updatedAt: z.string(),
    downloads: z.number().default(0),
    rating: z.number().min(0).max(5).default(0),
    featured: z.boolean().default(false),
    premium: z.boolean().default(false),
  }),
})

// Type exports
export type TemplateVariable = z.infer<typeof TemplateVariableSchema>
export type TemplateStyle = z.infer<typeof TemplateStyleSchema>
export type TemplateLayout = z.infer<typeof TemplateLayoutSchema>
export type TemplateSection = z.infer<typeof TemplateSectionSchema>
export type TemplateTheme = z.infer<typeof TemplateThemeSchema>
export type TemplateConfig = z.infer<typeof TemplateConfigSchema>

// Template validation function
export const validateTemplate = (template: unknown): TemplateConfig => {
  return TemplateConfigSchema.parse(template)
}

// Template builder helpers
export const createTemplateSection = (
  id: string,
  name: string,
  type: TemplateSection['type'],
  template: string,
  order: number = 0
): TemplateSection => ({
  id,
  name,
  type,
  template,
  order,
  required: false,
  styles: [],
  variables: [],
})

export const createTemplateTheme = (name: string): TemplateTheme => ({
  name,
  colors: {
    primary: '#3b82f6',
    secondary: '#64748b',
    accent: '#f59e0b',
    text: '#1f2937',
    textLight: '#6b7280',
    background: '#ffffff',
    surface: '#f9fafb',
    border: '#e5e7eb',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
  },
  typography: {
    fontFamily: {
      primary: 'Inter, system-ui, sans-serif',
      secondary: 'Georgia, serif',
      monospace: 'JetBrains Mono, monospace',
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
    lineHeight: {
      tight: '1.25',
      normal: '1.5',
      relaxed: '1.75',
    },
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
  },
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
    full: '9999px',
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1)',
  },
})

// Default template layout
export const createDefaultLayout = (): TemplateLayout => ({
  type: 'single-column',
  columns: [
    {
      width: '100%',
      content: ['header', 'summary', 'experience', 'education', 'skills'],
    },
  ],
  spacing: {
    margin: '1rem',
    padding: '1rem',
    gap: '1rem',
  },
  breakpoints: {
    mobile: '768px',
    tablet: '1024px',
    desktop: '1200px',
  },
})
