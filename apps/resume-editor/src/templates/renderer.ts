import { Descendant, Node, Text } from 'slate'
import { TemplateConfig, TemplateSection, TemplateTheme } from './schema'
import { ResumeDocument, CustomElement } from '@/types/resume'

interface RenderContext {
  template: TemplateConfig
  document: ResumeDocument
  data: Record<string, any>
  theme: TemplateTheme
  options: RenderOptions
}

interface RenderOptions {
  format: 'html' | 'pdf' | 'print'
  quality: 'draft' | 'standard' | 'high'
  includeStyles: boolean
  minify: boolean
  atsOptimized: boolean
}

export class TemplateRenderer {
  private context: RenderContext
  private variableCache = new Map<string, any>()
  private styleCache = new Map<string, string>()

  constructor(context: RenderContext) {
    this.context = context
  }

  // Main render method
  async render(): Promise<string> {
    const { template, document, options } = this.context

    // Generate CSS
    const styles = this.generateStyles()
    
    // Generate HTML structure
    const html = this.generateHTML()
    
    // Combine into complete document
    const fullDocument = this.wrapDocument(html, styles)
    
    // Apply optimizations
    return options.minify ? this.minifyHTML(fullDocument) : fullDocument
  }

  // Generate CSS from template theme and styles
  private generateStyles(): string {
    const { template, theme, options } = this.context
    
    let css = ''
    
    // Base styles
    css += this.generateBaseStyles(theme)
    
    // Layout styles
    css += this.generateLayoutStyles(template.layout)
    
    // Section styles
    for (const section of template.sections) {
      css += this.generateSectionStyles(section)
    }
    
    // Responsive styles
    css += this.generateResponsiveStyles()
    
    // ATS optimization styles
    if (options.atsOptimized) {
      css += this.generateATSStyles()
    }
    
    return css
  }

  // Generate base CSS from theme
  private generateBaseStyles(theme: TemplateTheme): string {
    return `
      :root {
        --color-primary: ${theme.colors.primary};
        --color-secondary: ${theme.colors.secondary};
        --color-accent: ${theme.colors.accent};
        --color-text: ${theme.colors.text};
        --color-text-light: ${theme.colors.textLight};
        --color-background: ${theme.colors.background};
        --color-surface: ${theme.colors.surface};
        --color-border: ${theme.colors.border};
        --color-success: ${theme.colors.success};
        --color-warning: ${theme.colors.warning};
        --color-error: ${theme.colors.error};
        
        --font-primary: ${theme.typography.fontFamily.primary};
        --font-secondary: ${theme.typography.fontFamily.secondary || theme.typography.fontFamily.primary};
        --font-monospace: ${theme.typography.fontFamily.monospace || 'monospace'};
        
        --spacing-xs: ${theme.spacing.xs};
        --spacing-sm: ${theme.spacing.sm};
        --spacing-md: ${theme.spacing.md};
        --spacing-lg: ${theme.spacing.lg};
        --spacing-xl: ${theme.spacing.xl};
        --spacing-2xl: ${theme.spacing['2xl']};
        --spacing-3xl: ${theme.spacing['3xl']};
        
        --radius-sm: ${theme.borderRadius.sm};
        --radius-md: ${theme.borderRadius.md};
        --radius-lg: ${theme.borderRadius.lg};
        
        --shadow-sm: ${theme.shadows.sm};
        --shadow-md: ${theme.shadows.md};
        --shadow-lg: ${theme.shadows.lg};
        --shadow-xl: ${theme.shadows.xl};
      }
      
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }
      
      body {
        font-family: var(--font-primary);
        font-size: ${theme.typography.fontSize.base};
        line-height: ${theme.typography.lineHeight.normal};
        color: var(--color-text);
        background-color: var(--color-background);
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      .resume-container {
        max-width: 8.5in;
        margin: 0 auto;
        background: var(--color-background);
        box-shadow: var(--shadow-lg);
        min-height: 11in;
      }
      
      @media print {
        .resume-container {
          box-shadow: none;
          margin: 0;
          max-width: none;
        }
        
        body {
          background: white;
        }
      }
      
      h1, h2, h3, h4, h5, h6 {
        font-weight: ${theme.typography.fontWeight.semibold};
        line-height: ${theme.typography.lineHeight.tight};
        margin-bottom: var(--spacing-sm);
      }
      
      h1 { font-size: ${theme.typography.fontSize['4xl']}; }
      h2 { font-size: ${theme.typography.fontSize['3xl']}; }
      h3 { font-size: ${theme.typography.fontSize['2xl']}; }
      h4 { font-size: ${theme.typography.fontSize.xl}; }
      h5 { font-size: ${theme.typography.fontSize.lg}; }
      h6 { font-size: ${theme.typography.fontSize.base}; }
      
      p {
        margin-bottom: var(--spacing-md);
        line-height: ${theme.typography.lineHeight.relaxed};
      }
      
      ul, ol {
        margin-bottom: var(--spacing-md);
        padding-left: var(--spacing-lg);
      }
      
      li {
        margin-bottom: var(--spacing-xs);
      }
      
      a {
        color: var(--color-primary);
        text-decoration: none;
      }
      
      a:hover {
        text-decoration: underline;
      }
      
      strong {
        font-weight: ${theme.typography.fontWeight.semibold};
      }
      
      em {
        font-style: italic;
      }
    `
  }

  // Generate layout-specific CSS
  private generateLayoutStyles(layout: any): string {
    const { type, columns, spacing } = layout
    
    let css = `
      .resume-layout {
        padding: ${spacing.padding};
        gap: ${spacing.gap};
      }
    `
    
    switch (type) {
      case 'single-column':
        css += `
          .resume-layout {
            display: flex;
            flex-direction: column;
          }
        `
        break
        
      case 'two-column':
        css += `
          .resume-layout {
            display: grid;
            grid-template-columns: ${columns[0]?.width || '1fr'} ${columns[1]?.width || '1fr'};
          }
        `
        break
        
      case 'sidebar':
        css += `
          .resume-layout {
            display: grid;
            grid-template-columns: 300px 1fr;
          }
          
          @media (max-width: 768px) {
            .resume-layout {
              grid-template-columns: 1fr;
            }
          }
        `
        break
        
      default:
        css += `
          .resume-layout {
            display: flex;
            flex-direction: column;
          }
        `
    }
    
    return css
  }

  // Generate section-specific CSS
  private generateSectionStyles(section: TemplateSection): string {
    let css = `
      .section-${section.id} {
        margin-bottom: var(--spacing-lg);
      }
      
      .section-${section.id} .section-title {
        font-size: var(--font-size-2xl);
        font-weight: var(--font-weight-semibold);
        color: var(--color-primary);
        margin-bottom: var(--spacing-md);
        padding-bottom: var(--spacing-xs);
        border-bottom: 2px solid var(--color-border);
      }
    `
    
    // Add custom styles from section definition
    for (const style of section.styles) {
      css += `
        ${style.selector} {
          ${Object.entries(style.properties)
            .map(([prop, value]) => `${prop}: ${value};`)
            .join('\n          ')}
        }
      `
      
      // Add responsive styles
      if (style.responsive) {
        if (style.responsive.mobile) {
          css += `
            @media (max-width: 768px) {
              ${style.selector} {
                ${Object.entries(style.responsive.mobile)
                  .map(([prop, value]) => `${prop}: ${value};`)
                  .join('\n                ')}
              }
            }
          `
        }
        
        if (style.responsive.tablet) {
          css += `
            @media (min-width: 769px) and (max-width: 1024px) {
              ${style.selector} {
                ${Object.entries(style.responsive.tablet)
                  .map(([prop, value]) => `${prop}: ${value};`)
                  .join('\n                ')}
              }
            }
          `
        }
      }
    }
    
    return css
  }

  // Generate responsive CSS
  private generateResponsiveStyles(): string {
    return `
      @media print {
        .resume-container {
          box-shadow: none;
          margin: 0;
        }
        
        .no-print {
          display: none !important;
        }
      }
      
      @media (max-width: 768px) {
        .resume-container {
          margin: var(--spacing-sm);
          box-shadow: none;
        }
        
        .mobile-hidden {
          display: none;
        }
        
        .mobile-full-width {
          width: 100%;
        }
      }
    `
  }

  // Generate ATS-optimized CSS
  private generateATSStyles(): string {
    return `
      /* ATS-friendly styles */
      .ats-optimized {
        font-family: Arial, sans-serif;
        font-size: 11pt;
        line-height: 1.2;
      }
      
      .ats-optimized h1,
      .ats-optimized h2,
      .ats-optimized h3 {
        font-weight: bold;
        text-transform: uppercase;
      }
      
      .ats-optimized .contact-info {
        text-align: center;
        margin-bottom: 20pt;
      }
      
      .ats-optimized .section-title {
        font-weight: bold;
        text-transform: uppercase;
        border-bottom: 1pt solid black;
        margin-bottom: 10pt;
      }
      
      .ats-optimized ul {
        list-style-type: disc;
        margin-left: 20pt;
      }
      
      .ats-optimized .date-range {
        float: right;
        font-weight: normal;
      }
    `
  }

  // Generate HTML structure
  private generateHTML(): string {
    const { template, document } = this.context
    
    let html = '<div class="resume-container">'
    html += '<div class="resume-layout">'
    
    // Render sections in order
    const sortedSections = template.sections.sort((a, b) => a.order - b.order)
    
    for (const section of sortedSections) {
      // Check conditions
      if (!this.shouldRenderSection(section)) {
        continue
      }
      
      html += this.renderSection(section)
    }
    
    html += '</div>'
    html += '</div>'
    
    return html
  }

  // Check if section should be rendered based on conditions
  private shouldRenderSection(section: TemplateSection): boolean {
    if (!section.conditions) {
      return true
    }
    
    const { data } = this.context
    
    // Simple condition evaluation (in production, use a safe evaluator)
    if (section.conditions.show) {
      try {
        return new Function('data', `return ${section.conditions.show}`)(data)
      } catch {
        return true
      }
    }
    
    if (section.conditions.hide) {
      try {
        return !new Function('data', `return ${section.conditions.hide}`)(data)
      } catch {
        return true
      }
    }
    
    return true
  }

  // Render individual section
  private renderSection(section: TemplateSection): string {
    const { data } = this.context
    
    // Process template variables
    let html = section.template
    
    // Replace variables with actual data
    html = this.processVariables(html, data)
    
    // Wrap in section container
    return `
      <section class="section-${section.id}" data-section="${section.type}">
        ${html}
      </section>
    `
  }

  // Process template variables
  private processVariables(template: string, data: Record<string, any>): string {
    // Simple variable replacement ({{variable}})
    return template.replace(/\{\{([^}]+)\}\}/g, (match, variable) => {
      const value = this.getNestedValue(data, variable.trim())
      return value !== undefined ? String(value) : ''
    })
  }

  // Get nested object value by path
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined
    }, obj)
  }

  // Wrap HTML in complete document
  private wrapDocument(html: string, styles: string): string {
    const { template, options } = this.context
    
    return `
      <!DOCTYPE html>
      <html lang="${template.i18n.defaultLanguage}" dir="${template.i18n.rtlSupport ? 'rtl' : 'ltr'}">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${this.context.document.title}</title>
        <style>${styles}</style>
      </head>
      <body>
        ${html}
      </body>
      </html>
    `
  }

  // Minify HTML
  private minifyHTML(html: string): string {
    return html
      .replace(/\s+/g, ' ')
      .replace(/>\s+</g, '><')
      .trim()
  }
}
