/**
 * Template DSL Engine with JSON Schema Validation
 * Renders HTML/CSS from template configurations with responsive layouts
 */

import { z } from 'zod'
import { ResumeDocument, CustomElement } from '@/types/resume'

// Template DSL Schema
const ColorSchema = z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Must be a valid hex color')

const TypographySchema = z.object({
  fontFamily: z.object({
    primary: z.string(),
    secondary: z.string().optional(),
  }),
  fontSize: z.object({
    xs: z.string(),
    sm: z.string(),
    base: z.string(),
    lg: z.string(),
    xl: z.string(),
    '2xl': z.string(),
    '3xl': z.string(),
    '4xl': z.string(),
  }),
  fontWeight: z.object({
    normal: z.string(),
    medium: z.string(),
    semibold: z.string(),
    bold: z.string(),
  }),
  lineHeight: z.object({
    tight: z.string(),
    normal: z.string(),
    relaxed: z.string(),
  }),
})

const SpacingSchema = z.object({
  xs: z.string(),
  sm: z.string(),
  md: z.string(),
  lg: z.string(),
  xl: z.string(),
  '2xl': z.string(),
  '3xl': z.string(),
})

const ThemeSchema = z.object({
  name: z.string(),
  colors: z.object({
    primary: ColorSchema,
    secondary: ColorSchema,
    accent: ColorSchema,
    text: ColorSchema,
    textLight: ColorSchema,
    background: ColorSchema,
    surface: ColorSchema,
    border: ColorSchema,
    success: ColorSchema,
    warning: ColorSchema,
    error: ColorSchema,
  }),
  typography: TypographySchema,
  spacing: SpacingSchema,
  borderRadius: z.object({
    none: z.string(),
    sm: z.string(),
    md: z.string(),
    lg: z.string(),
    full: z.string(),
  }),
  shadows: z.object({
    sm: z.string(),
    md: z.string(),
    lg: z.string(),
    xl: z.string(),
  }),
})

const LayoutColumnSchema = z.object({
  width: z.string(),
  content: z.array(z.string()),
  gap: z.string().optional(),
  padding: z.string().optional(),
})

const LayoutSchema = z.object({
  type: z.enum(['single-column', 'two-column', 'three-column', 'sidebar-left', 'sidebar-right']),
  columns: z.array(LayoutColumnSchema),
  spacing: z.object({
    margin: z.string(),
    padding: z.string(),
    gap: z.string(),
  }),
  breakpoints: z.object({
    mobile: z.string(),
    tablet: z.string(),
    desktop: z.string(),
  }),
})

const SectionConfigSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  required: z.boolean().default(false),
  order: z.number(),
  styling: z.object({
    marginTop: z.string().optional(),
    marginBottom: z.string().optional(),
    padding: z.string().optional(),
    backgroundColor: z.string().optional(),
    borderRadius: z.string().optional(),
    border: z.string().optional(),
  }).optional(),
  layout: z.object({
    display: z.enum(['block', 'flex', 'grid']).default('block'),
    flexDirection: z.enum(['row', 'column']).optional(),
    justifyContent: z.string().optional(),
    alignItems: z.string().optional(),
    gridColumns: z.string().optional(),
    gap: z.string().optional(),
  }).optional(),
})

const TemplateConfigSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  version: z.string(),
  author: z.object({
    name: z.string(),
    email: z.string().email(),
    url: z.string().url().optional(),
  }),
  category: z.enum(['modern', 'classic', 'creative', 'minimal', 'professional']),
  tags: z.array(z.string()),
  layout: LayoutSchema,
  sections: z.array(SectionConfigSchema),
  theme: ThemeSchema,
  settings: z.object({
    pageSize: z.enum(['A4', 'Letter', 'Legal']),
    orientation: z.enum(['portrait', 'landscape']),
    margins: z.object({
      top: z.string(),
      right: z.string(),
      bottom: z.string(),
      left: z.string(),
    }),
    dpi: z.number().min(72).max(600),
    quality: z.enum(['draft', 'standard', 'high']),
  }),
  ats: z.object({
    friendly: z.boolean(),
    keywords: z.array(z.string()),
    structure: z.object({
      useSemanticHTML: z.boolean(),
      includeMetadata: z.boolean(),
      optimizeForParsing: z.boolean(),
    }),
  }),
  accessibility: z.object({
    wcagLevel: z.enum(['A', 'AA', 'AAA']),
    highContrast: z.boolean(),
    screenReaderOptimized: z.boolean(),
    keyboardNavigation: z.boolean(),
  }),
  i18n: z.object({
    defaultLanguage: z.string(),
    supportedLanguages: z.array(z.string()),
    rtlSupport: z.boolean(),
    dateFormat: z.string(),
    numberFormat: z.string(),
  }),
  preview: z.object({
    demoData: z.record(z.any()),
  }),
  metadata: z.object({
    createdAt: z.string(),
    updatedAt: z.string(),
    downloads: z.number(),
    rating: z.number().min(0).max(5),
    featured: z.boolean(),
    premium: z.boolean(),
  }),
})

export type TemplateConfig = z.infer<typeof TemplateConfigSchema>
export type ThemeConfig = z.infer<typeof ThemeSchema>
export type LayoutConfig = z.infer<typeof LayoutSchema>
export type SectionConfig = z.infer<typeof SectionConfigSchema>

export class TemplateEngine {
  private templates: Map<string, TemplateConfig> = new Map()
  private compiledCSS: Map<string, string> = new Map()

  constructor() {
    this.loadDefaultTemplates()
  }

  // Template management
  async loadTemplate(config: unknown): Promise<TemplateConfig> {
    const validatedConfig = TemplateConfigSchema.parse(config)
    this.templates.set(validatedConfig.id, validatedConfig)
    
    // Pre-compile CSS for performance
    const css = this.generateCSS(validatedConfig)
    this.compiledCSS.set(validatedConfig.id, css)
    
    return validatedConfig
  }

  getTemplate(id: string): TemplateConfig | null {
    return this.templates.get(id) || null
  }

  getAllTemplates(): TemplateConfig[] {
    return Array.from(this.templates.values())
  }

  getTemplatesByCategory(category: string): TemplateConfig[] {
    return this.getAllTemplates().filter(template => template.category === category)
  }

  // Rendering engine
  renderDocument(document: ResumeDocument, templateId: string): { html: string; css: string } {
    const template = this.getTemplate(templateId)
    if (!template) {
      throw new Error(`Template ${templateId} not found`)
    }

    const html = this.generateHTML(document, template)
    const css = this.compiledCSS.get(templateId) || this.generateCSS(template)

    return { html, css }
  }

  private generateHTML(document: ResumeDocument, template: TemplateConfig): string {
    const { layout, sections, theme, settings } = template
    
    // Generate document structure
    const documentHTML = `
      <!DOCTYPE html>
      <html lang="${document.metadata.language}" dir="${document.metadata.direction}">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${document.title}</title>
          <style>${this.compiledCSS.get(template.id)}</style>
        </head>
        <body class="resume-document ${template.category}">
          <div class="resume-container">
            ${this.generateLayoutHTML(document, template)}
          </div>
        </body>
      </html>
    `

    return this.minifyHTML(documentHTML)
  }

  private generateLayoutHTML(document: ResumeDocument, template: TemplateConfig): string {
    const { layout } = template
    
    switch (layout.type) {
      case 'single-column':
        return this.generateSingleColumnLayout(document, template)
      case 'two-column':
        return this.generateTwoColumnLayout(document, template)
      case 'three-column':
        return this.generateThreeColumnLayout(document, template)
      case 'sidebar-left':
        return this.generateSidebarLayout(document, template, 'left')
      case 'sidebar-right':
        return this.generateSidebarLayout(document, template, 'right')
      default:
        return this.generateSingleColumnLayout(document, template)
    }
  }

  private generateSingleColumnLayout(document: ResumeDocument, template: TemplateConfig): string {
    const sections = this.organizeSections(document.content, template.sections)
    
    return `
      <div class="layout-single-column">
        ${sections.map(section => this.renderSection(section, template)).join('')}
      </div>
    `
  }

  private generateTwoColumnLayout(document: ResumeDocument, template: TemplateConfig): string {
    const { layout } = template
    const sections = this.organizeSections(document.content, template.sections)
    
    const leftColumn = layout.columns[0]
    const rightColumn = layout.columns[1]
    
    const leftSections = sections.filter(section => 
      leftColumn.content.includes(section.type)
    )
    const rightSections = sections.filter(section => 
      rightColumn.content.includes(section.type)
    )

    return `
      <div class="layout-two-column">
        <div class="column-left" style="width: ${leftColumn.width}">
          ${leftSections.map(section => this.renderSection(section, template)).join('')}
        </div>
        <div class="column-right" style="width: ${rightColumn.width}">
          ${rightSections.map(section => this.renderSection(section, template)).join('')}
        </div>
      </div>
    `
  }

  private generateThreeColumnLayout(document: ResumeDocument, template: TemplateConfig): string {
    const { layout } = template
    const sections = this.organizeSections(document.content, template.sections)
    
    return `
      <div class="layout-three-column">
        ${layout.columns.map((column, index) => {
          const columnSections = sections.filter(section => 
            column.content.includes(section.type)
          )
          
          return `
            <div class="column-${index}" style="width: ${column.width}">
              ${columnSections.map(section => this.renderSection(section, template)).join('')}
            </div>
          `
        }).join('')}
      </div>
    `
  }

  private generateSidebarLayout(document: ResumeDocument, template: TemplateConfig, side: 'left' | 'right'): string {
    const { layout } = template
    const sections = this.organizeSections(document.content, template.sections)
    
    const sidebarColumn = side === 'left' ? layout.columns[0] : layout.columns[1]
    const mainColumn = side === 'left' ? layout.columns[1] : layout.columns[0]
    
    const sidebarSections = sections.filter(section => 
      sidebarColumn.content.includes(section.type)
    )
    const mainSections = sections.filter(section => 
      mainColumn.content.includes(section.type)
    )

    const sidebarHTML = `
      <div class="sidebar sidebar-${side}" style="width: ${sidebarColumn.width}">
        ${sidebarSections.map(section => this.renderSection(section, template)).join('')}
      </div>
    `
    
    const mainHTML = `
      <div class="main-content" style="width: ${mainColumn.width}">
        ${mainSections.map(section => this.renderSection(section, template)).join('')}
      </div>
    `

    return `
      <div class="layout-sidebar layout-sidebar-${side}">
        ${side === 'left' ? sidebarHTML + mainHTML : mainHTML + sidebarHTML}
      </div>
    `
  }

  private renderSection(section: CustomElement, template: TemplateConfig): string {
    const sectionConfig = template.sections.find(s => s.type === section.type)
    const styling = sectionConfig?.styling || {}
    const layout = sectionConfig?.layout || {}
    
    const styleAttr = this.generateInlineStyles(styling)
    const layoutClasses = this.generateLayoutClasses(layout)
    
    return `
      <section class="resume-section section-${section.type} ${layoutClasses}" ${styleAttr} data-section-id="${section.id}">
        ${this.renderSectionContent(section, template)}
      </section>
    `
  }

  private renderSectionContent(section: CustomElement, template: TemplateConfig): string {
    switch (section.type) {
      case 'header':
        return this.renderHeaderSection(section, template)
      case 'summary':
        return this.renderSummarySection(section, template)
      case 'experience':
        return this.renderExperienceSection(section, template)
      case 'education':
        return this.renderEducationSection(section, template)
      case 'skills':
        return this.renderSkillsSection(section, template)
      case 'projects':
        return this.renderProjectsSection(section, template)
      default:
        return this.renderGenericSection(section, template)
    }
  }

  private renderHeaderSection(section: CustomElement, template: TemplateConfig): string {
    // Extract header information from section content
    const name = this.extractText(section, 'name') || 'Name'
    const title = this.extractText(section, 'title') || 'Title'
    const contact = this.extractContactInfo(section)
    
    return `
      <div class="header-content">
        <h1 class="name">${name}</h1>
        <h2 class="title">${title}</h2>
        <div class="contact-info">
          ${contact.map(item => `<span class="contact-item">${item}</span>`).join('')}
        </div>
      </div>
    `
  }

  private renderExperienceSection(section: CustomElement, template: TemplateConfig): string {
    const experiences = this.extractExperiences(section)
    
    return `
      <div class="experience-content">
        <h3 class="section-title">Experience</h3>
        ${experiences.map(exp => `
          <div class="experience-item">
            <div class="experience-header">
              <h4 class="position">${exp.position}</h4>
              <span class="company">${exp.company}</span>
              <span class="duration">${exp.duration}</span>
            </div>
            <div class="experience-description">
              ${exp.description}
            </div>
          </div>
        `).join('')}
      </div>
    `
  }

  private generateCSS(template: TemplateConfig): string {
    const { theme, layout, settings } = template
    
    return `
      /* Reset and base styles */
      * { margin: 0; padding: 0; box-sizing: border-box; }
      
      /* Document styles */
      .resume-document {
        font-family: ${theme.typography.fontFamily.primary};
        font-size: ${theme.typography.fontSize.base};
        line-height: ${theme.typography.lineHeight.normal};
        color: ${theme.colors.text};
        background-color: ${theme.colors.background};
      }
      
      /* Container styles */
      .resume-container {
        max-width: 8.5in;
        margin: 0 auto;
        padding: ${settings.margins.top} ${settings.margins.right} ${settings.margins.bottom} ${settings.margins.left};
        background: ${theme.colors.surface};
        box-shadow: ${theme.shadows.lg};
      }
      
      /* Layout styles */
      ${this.generateLayoutCSS(layout, theme)}
      
      /* Typography styles */
      ${this.generateTypographyCSS(theme)}
      
      /* Section styles */
      ${this.generateSectionCSS(template)}
      
      /* Responsive styles */
      ${this.generateResponsiveCSS(layout, theme)}
      
      /* Print styles */
      ${this.generatePrintCSS(settings)}
    `
  }

  private generateLayoutCSS(layout: LayoutConfig, theme: ThemeConfig): string {
    const baseCSS = `
      .layout-single-column {
        display: block;
      }
      
      .layout-two-column,
      .layout-three-column,
      .layout-sidebar {
        display: flex;
        gap: ${layout.spacing.gap};
      }
      
      .column-left,
      .column-right,
      .sidebar,
      .main-content {
        flex-shrink: 0;
      }
    `
    
    return baseCSS
  }

  private generateTypographyCSS(theme: ThemeConfig): string {
    return `
      h1, h2, h3, h4, h5, h6 {
        font-weight: ${theme.typography.fontWeight.bold};
        margin-bottom: ${theme.spacing.sm};
      }
      
      h1 { font-size: ${theme.typography.fontSize['3xl']}; }
      h2 { font-size: ${theme.typography.fontSize['2xl']}; }
      h3 { font-size: ${theme.typography.fontSize.xl}; }
      h4 { font-size: ${theme.typography.fontSize.lg}; }
      
      p {
        margin-bottom: ${theme.spacing.md};
        line-height: ${theme.typography.lineHeight.relaxed};
      }
      
      .text-primary { color: ${theme.colors.primary}; }
      .text-secondary { color: ${theme.colors.secondary}; }
      .text-light { color: ${theme.colors.textLight}; }
    `
  }

  private generateSectionCSS(template: TemplateConfig): string {
    return template.sections.map(section => `
      .section-${section.type} {
        margin-bottom: ${template.theme.spacing.lg};
      }
      
      .section-${section.type} .section-title {
        color: ${template.theme.colors.primary};
        border-bottom: 2px solid ${template.theme.colors.border};
        padding-bottom: ${template.theme.spacing.xs};
        margin-bottom: ${template.theme.spacing.md};
      }
    `).join('')
  }

  private generateResponsiveCSS(layout: LayoutConfig, theme: ThemeConfig): string {
    return `
      @media (max-width: ${layout.breakpoints.tablet}) {
        .layout-two-column,
        .layout-three-column,
        .layout-sidebar {
          flex-direction: column;
        }
        
        .column-left,
        .column-right,
        .sidebar,
        .main-content {
          width: 100% !important;
        }
      }
      
      @media (max-width: ${layout.breakpoints.mobile}) {
        .resume-container {
          padding: ${theme.spacing.md};
        }
        
        h1 { font-size: ${theme.typography.fontSize['2xl']}; }
        h2 { font-size: ${theme.typography.fontSize.xl}; }
      }
    `
  }

  private generatePrintCSS(settings: any): string {
    return `
      @media print {
        .resume-container {
          box-shadow: none;
          margin: 0;
          padding: 0;
        }
        
        @page {
          size: ${settings.pageSize} ${settings.orientation};
          margin: ${settings.margins.top} ${settings.margins.right} ${settings.margins.bottom} ${settings.margins.left};
        }
      }
    `
  }

  // Utility methods
  private organizeSections(content: CustomElement[], sectionConfigs: SectionConfig[]): CustomElement[] {
    return content.sort((a, b) => {
      const aConfig = sectionConfigs.find(s => s.type === a.type)
      const bConfig = sectionConfigs.find(s => s.type === b.type)
      
      const aOrder = aConfig?.order || 999
      const bOrder = bConfig?.order || 999
      
      return aOrder - bOrder
    })
  }

  private extractText(section: CustomElement, field: string): string {
    // Extract text from section based on field type
    if (section.type === 'header') {
      switch (field) {
        case 'name':
          return this.findTextInChildren(section, 'name') || 'Your Name'
        case 'title':
          return this.findTextInChildren(section, 'title') || 'Professional Title'
        default:
          return ''
      }
    }
    return this.findTextInChildren(section, field) || ''
  }

  private extractContactInfo(section: CustomElement): string[] {
    const contact: string[] = []

    // Extract email
    const email = this.findTextInChildren(section, 'email')
    if (email) contact.push(`📧 ${email}`)

    // Extract phone
    const phone = this.findTextInChildren(section, 'phone')
    if (phone) contact.push(`📞 ${phone}`)

    // Extract location
    const location = this.findTextInChildren(section, 'location')
    if (location) contact.push(`📍 ${location}`)

    // Extract LinkedIn
    const linkedin = this.findTextInChildren(section, 'linkedin')
    if (linkedin) contact.push(`💼 ${linkedin}`)

    return contact
  }

  private extractExperiences(section: CustomElement): any[] {
    const experiences: any[] = []

    if (section.children && Array.isArray(section.children)) {
      for (const child of section.children) {
        if (child.type === 'experience-item') {
          experiences.push({
            position: this.findTextInChildren(child, 'position') || 'Position',
            company: this.findTextInChildren(child, 'company') || 'Company',
            duration: this.findTextInChildren(child, 'duration') || 'Duration',
            description: this.findTextInChildren(child, 'description') || 'Job description and achievements'
          })
        }
      }
    }

    // Fallback for simple text content
    if (experiences.length === 0) {
      experiences.push({
        position: 'Senior Software Engineer',
        company: 'Tech Company',
        duration: '2020 - Present',
        description: 'Led development of scalable web applications and mentored junior developers.'
      })
    }

    return experiences
  }

  private findTextInChildren(element: any, field: string): string | null {
    // Recursive search for text content in element children
    if (!element) return null

    // Check if element has the field directly
    if (element[field] && typeof element[field] === 'string') {
      return element[field]
    }

    // Check children array
    if (element.children && Array.isArray(element.children)) {
      for (const child of element.children) {
        const result = this.findTextInChildren(child, field)
        if (result) return result
      }
    }

    // Check text content
    if (element.text && typeof element.text === 'string') {
      return element.text
    }

    return null
  }

  private generateInlineStyles(styling: any): string {
    const styles = Object.entries(styling)
      .map(([key, value]) => `${this.camelToKebab(key)}: ${value}`)
      .join('; ')
    
    return styles ? `style="${styles}"` : ''
  }

  private generateLayoutClasses(layout: any): string {
    const classes = []
    
    if (layout.display) classes.push(`display-${layout.display}`)
    if (layout.flexDirection) classes.push(`flex-${layout.flexDirection}`)
    if (layout.justifyContent) classes.push(`justify-${layout.justifyContent}`)
    if (layout.alignItems) classes.push(`items-${layout.alignItems}`)
    
    return classes.join(' ')
  }

  private camelToKebab(str: string): string {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase()
  }

  private minifyHTML(html: string): string {
    return html
      .replace(/\s+/g, ' ')
      .replace(/>\s+</g, '><')
      .trim()
  }

  private loadDefaultTemplates(): void {
    // Load comprehensive template collection
    const templates = [
      this.createModernProfessionalTemplate(),
      this.createClassicTemplate(),
      this.createCreativeTemplate(),
      this.createMinimalTemplate(),
      this.createExecutiveTemplate()
    ]

    templates.forEach(template => this.loadTemplate(template))
  }

  private createModernProfessionalTemplate(): TemplateConfig {
    return {
      id: 'modern-professional',
      name: 'Modern Professional',
      description: 'Clean, modern design perfect for tech professionals',
      version: '1.0.0',
      author: { name: 'CVLeap Team', email: '<EMAIL>' },
      category: 'modern',
      tags: ['professional', 'clean', 'modern'],
      layout: {
        type: 'two-column',
        columns: [
          { width: '35%', content: ['header', 'skills', 'education'] },
          { width: '65%', content: ['summary', 'experience', 'projects'] }
        ],
        spacing: { margin: '1rem', padding: '1rem', gap: '2rem' },
        breakpoints: { mobile: '768px', tablet: '1024px', desktop: '1200px' }
      },
      sections: [
        { id: 'header', name: 'Header', type: 'header', required: true, order: 1 },
        { id: 'summary', name: 'Professional Summary', type: 'summary', required: false, order: 2 },
        { id: 'experience', name: 'Work Experience', type: 'experience', required: true, order: 3 },
        { id: 'education', name: 'Education', type: 'education', required: true, order: 4 },
        { id: 'skills', name: 'Skills', type: 'skills', required: false, order: 5 },
        { id: 'projects', name: 'Projects', type: 'projects', required: false, order: 6 }
      ],
      theme: {
        name: 'Modern Blue',
        colors: {
          primary: '#2563eb', secondary: '#64748b', accent: '#f59e0b',
          text: '#1f2937', textLight: '#6b7280', background: '#ffffff',
          surface: '#f9fafb', border: '#e5e7eb', success: '#10b981',
          warning: '#f59e0b', error: '#ef4444'
        },
        typography: {
          fontFamily: { primary: 'Inter, sans-serif', secondary: 'Georgia, serif' },
          fontSize: {
            xs: '0.75rem', sm: '0.875rem', base: '1rem', lg: '1.125rem',
            xl: '1.25rem', '2xl': '1.5rem', '3xl': '1.875rem', '4xl': '2.25rem'
          },
          fontWeight: { normal: '400', medium: '500', semibold: '600', bold: '700' },
          lineHeight: { tight: '1.25', normal: '1.5', relaxed: '1.75' }
        },
        spacing: {
          xs: '0.25rem', sm: '0.5rem', md: '1rem', lg: '1.5rem',
          xl: '2rem', '2xl': '3rem', '3xl': '4rem'
        },
        borderRadius: { none: '0', sm: '0.125rem', md: '0.375rem', lg: '0.5rem', full: '9999px' },
        shadows: {
          sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)', md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
          lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)', xl: '0 20px 25px -5px rgb(0 0 0 / 0.1)'
        }
      },
      settings: {
        pageSize: 'A4', orientation: 'portrait',
        margins: { top: '1in', right: '1in', bottom: '1in', left: '1in' },
        dpi: 300, quality: 'standard'
      },
      ats: {
        friendly: true, keywords: [],
        structure: { useSemanticHTML: true, includeMetadata: true, optimizeForParsing: true }
      },
      accessibility: { wcagLevel: 'AA', highContrast: false, screenReaderOptimized: true, keyboardNavigation: true },
      i18n: { defaultLanguage: 'en', supportedLanguages: ['en'], rtlSupport: false, dateFormat: 'MM/yyyy', numberFormat: 'en-US' },
      preview: { demoData: {} },
      metadata: {
        createdAt: '2024-01-01T00:00:00Z', updatedAt: '2024-01-01T00:00:00Z',
        downloads: 0, rating: 0, featured: true, premium: false
      }
    }
  }

  private createClassicTemplate(): TemplateConfig {
    return {
      id: 'classic-traditional',
      name: 'Classic Traditional',
      description: 'Timeless design suitable for conservative industries',
      version: '1.0.0',
      author: { name: 'CVLeap Team', email: '<EMAIL>' },
      category: 'classic',
      tags: ['traditional', 'conservative', 'formal'],
      layout: {
        type: 'single-column',
        columns: [{ width: '100%', content: ['header', 'summary', 'experience', 'education', 'skills'] }],
        spacing: { margin: '1.5rem', padding: '1.5rem', gap: '1.5rem' },
        breakpoints: { mobile: '768px', tablet: '1024px', desktop: '1200px' }
      },
      sections: [
        { id: 'header', name: 'Header', type: 'header', required: true, order: 1 },
        { id: 'summary', name: 'Objective', type: 'summary', required: false, order: 2 },
        { id: 'experience', name: 'Professional Experience', type: 'experience', required: true, order: 3 },
        { id: 'education', name: 'Education', type: 'education', required: true, order: 4 },
        { id: 'skills', name: 'Core Competencies', type: 'skills', required: false, order: 5 }
      ],
      theme: {
        name: 'Classic Black',
        colors: {
          primary: '#000000', secondary: '#4a5568', accent: '#2d3748',
          text: '#1a202c', textLight: '#4a5568', background: '#ffffff',
          surface: '#ffffff', border: '#e2e8f0', success: '#38a169',
          warning: '#d69e2e', error: '#e53e3e'
        },
        typography: {
          fontFamily: { primary: 'Times New Roman, serif', secondary: 'Arial, sans-serif' },
          fontSize: {
            xs: '0.75rem', sm: '0.875rem', base: '1rem', lg: '1.125rem',
            xl: '1.25rem', '2xl': '1.5rem', '3xl': '1.875rem', '4xl': '2.25rem'
          },
          fontWeight: { normal: '400', medium: '500', semibold: '600', bold: '700' },
          lineHeight: { tight: '1.25', normal: '1.5', relaxed: '1.75' }
        },
        spacing: {
          xs: '0.25rem', sm: '0.5rem', md: '1rem', lg: '1.5rem',
          xl: '2rem', '2xl': '3rem', '3xl': '4rem'
        },
        borderRadius: { none: '0', sm: '0.125rem', md: '0.375rem', lg: '0.5rem', full: '9999px' },
        shadows: {
          sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)', md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
          lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)', xl: '0 20px 25px -5px rgb(0 0 0 / 0.1)'
        }
      },
      settings: {
        pageSize: 'A4', orientation: 'portrait',
        margins: { top: '1in', right: '1in', bottom: '1in', left: '1in' },
        dpi: 300, quality: 'standard'
      },
      ats: {
        friendly: true, keywords: [],
        structure: { useSemanticHTML: true, includeMetadata: true, optimizeForParsing: true }
      },
      accessibility: { wcagLevel: 'AA', highContrast: true, screenReaderOptimized: true, keyboardNavigation: true },
      i18n: { defaultLanguage: 'en', supportedLanguages: ['en'], rtlSupport: false, dateFormat: 'MM/yyyy', numberFormat: 'en-US' },
      preview: { demoData: {} },
      metadata: {
        createdAt: '2024-01-01T00:00:00Z', updatedAt: '2024-01-01T00:00:00Z',
        downloads: 0, rating: 0, featured: false, premium: false
      }
    }
  }

  private createCreativeTemplate(): TemplateConfig {
    return {
      id: 'creative-designer',
      name: 'Creative Designer',
      description: 'Bold, colorful design for creative professionals',
      version: '1.0.0',
      author: { name: 'CVLeap Team', email: '<EMAIL>' },
      category: 'creative',
      tags: ['creative', 'colorful', 'designer'],
      layout: {
        type: 'sidebar-left',
        columns: [
          { width: '30%', content: ['header', 'skills', 'education'] },
          { width: '70%', content: ['summary', 'experience', 'projects'] }
        ],
        spacing: { margin: '0.5rem', padding: '1rem', gap: '1rem' },
        breakpoints: { mobile: '768px', tablet: '1024px', desktop: '1200px' }
      },
      sections: [
        { id: 'header', name: 'Profile', type: 'header', required: true, order: 1 },
        { id: 'summary', name: 'About Me', type: 'summary', required: false, order: 2 },
        { id: 'experience', name: 'Experience', type: 'experience', required: true, order: 3 },
        { id: 'projects', name: 'Portfolio', type: 'projects', required: false, order: 4 },
        { id: 'education', name: 'Education', type: 'education', required: true, order: 5 },
        { id: 'skills', name: 'Expertise', type: 'skills', required: false, order: 6 }
      ],
      theme: {
        name: 'Creative Purple',
        colors: {
          primary: '#8b5cf6', secondary: '#06b6d4', accent: '#f59e0b',
          text: '#1f2937', textLight: '#6b7280', background: '#ffffff',
          surface: '#f8fafc', border: '#e5e7eb', success: '#10b981',
          warning: '#f59e0b', error: '#ef4444'
        },
        typography: {
          fontFamily: { primary: 'Poppins, sans-serif', secondary: 'Open Sans, sans-serif' },
          fontSize: {
            xs: '0.75rem', sm: '0.875rem', base: '1rem', lg: '1.125rem',
            xl: '1.25rem', '2xl': '1.5rem', '3xl': '1.875rem', '4xl': '2.25rem'
          },
          fontWeight: { normal: '400', medium: '500', semibold: '600', bold: '700' },
          lineHeight: { tight: '1.25', normal: '1.5', relaxed: '1.75' }
        },
        spacing: {
          xs: '0.25rem', sm: '0.5rem', md: '1rem', lg: '1.5rem',
          xl: '2rem', '2xl': '3rem', '3xl': '4rem'
        },
        borderRadius: { none: '0', sm: '0.125rem', md: '0.375rem', lg: '0.5rem', full: '9999px' },
        shadows: {
          sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)', md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
          lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)', xl: '0 20px 25px -5px rgb(0 0 0 / 0.1)'
        }
      },
      settings: {
        pageSize: 'A4', orientation: 'portrait',
        margins: { top: '0.75in', right: '0.75in', bottom: '0.75in', left: '0.75in' },
        dpi: 300, quality: 'high'
      },
      ats: {
        friendly: false, keywords: [],
        structure: { useSemanticHTML: true, includeMetadata: false, optimizeForParsing: false }
      },
      accessibility: { wcagLevel: 'AA', highContrast: false, screenReaderOptimized: true, keyboardNavigation: true },
      i18n: { defaultLanguage: 'en', supportedLanguages: ['en'], rtlSupport: false, dateFormat: 'MM/yyyy', numberFormat: 'en-US' },
      preview: { demoData: {} },
      metadata: {
        createdAt: '2024-01-01T00:00:00Z', updatedAt: '2024-01-01T00:00:00Z',
        downloads: 0, rating: 0, featured: false, premium: true
      }
    }
  }

  private createMinimalTemplate(): TemplateConfig {
    return {
      id: 'minimal-clean',
      name: 'Minimal Clean',
      description: 'Ultra-clean design with maximum white space',
      version: '1.0.0',
      author: { name: 'CVLeap Team', email: '<EMAIL>' },
      category: 'minimal',
      tags: ['minimal', 'clean', 'simple'],
      layout: {
        type: 'single-column',
        columns: [{ width: '100%', content: ['header', 'summary', 'experience', 'education', 'skills'] }],
        spacing: { margin: '2rem', padding: '2rem', gap: '3rem' },
        breakpoints: { mobile: '768px', tablet: '1024px', desktop: '1200px' }
      },
      sections: [
        { id: 'header', name: 'Contact', type: 'header', required: true, order: 1 },
        { id: 'summary', name: 'Summary', type: 'summary', required: false, order: 2 },
        { id: 'experience', name: 'Experience', type: 'experience', required: true, order: 3 },
        { id: 'education', name: 'Education', type: 'education', required: true, order: 4 },
        { id: 'skills', name: 'Skills', type: 'skills', required: false, order: 5 }
      ],
      theme: {
        name: 'Minimal Gray',
        colors: {
          primary: '#374151', secondary: '#9ca3af', accent: '#6b7280',
          text: '#111827', textLight: '#6b7280', background: '#ffffff',
          surface: '#ffffff', border: '#f3f4f6', success: '#059669',
          warning: '#d97706', error: '#dc2626'
        },
        typography: {
          fontFamily: { primary: 'Helvetica Neue, sans-serif', secondary: 'Arial, sans-serif' },
          fontSize: {
            xs: '0.75rem', sm: '0.875rem', base: '1rem', lg: '1.125rem',
            xl: '1.25rem', '2xl': '1.5rem', '3xl': '1.875rem', '4xl': '2.25rem'
          },
          fontWeight: { normal: '300', medium: '400', semibold: '500', bold: '600' },
          lineHeight: { tight: '1.25', normal: '1.6', relaxed: '1.8' }
        },
        spacing: {
          xs: '0.5rem', sm: '1rem', md: '1.5rem', lg: '2rem',
          xl: '3rem', '2xl': '4rem', '3xl': '5rem'
        },
        borderRadius: { none: '0', sm: '0.125rem', md: '0.375rem', lg: '0.5rem', full: '9999px' },
        shadows: {
          sm: 'none', md: 'none', lg: 'none', xl: 'none'
        }
      },
      settings: {
        pageSize: 'A4', orientation: 'portrait',
        margins: { top: '1.5in', right: '1.5in', bottom: '1.5in', left: '1.5in' },
        dpi: 300, quality: 'standard'
      },
      ats: {
        friendly: true, keywords: [],
        structure: { useSemanticHTML: true, includeMetadata: true, optimizeForParsing: true }
      },
      accessibility: { wcagLevel: 'AAA', highContrast: false, screenReaderOptimized: true, keyboardNavigation: true },
      i18n: { defaultLanguage: 'en', supportedLanguages: ['en'], rtlSupport: false, dateFormat: 'MM/yyyy', numberFormat: 'en-US' },
      preview: { demoData: {} },
      metadata: {
        createdAt: '2024-01-01T00:00:00Z', updatedAt: '2024-01-01T00:00:00Z',
        downloads: 0, rating: 0, featured: false, premium: false
      }
    }
  }

  private createExecutiveTemplate(): TemplateConfig {
    return {
      id: 'executive-premium',
      name: 'Executive Premium',
      description: 'Sophisticated design for senior executives and C-level positions',
      version: '1.0.0',
      author: { name: 'CVLeap Team', email: '<EMAIL>' },
      category: 'professional',
      tags: ['executive', 'premium', 'sophisticated'],
      layout: {
        type: 'two-column',
        columns: [
          { width: '40%', content: ['header', 'summary', 'education'] },
          { width: '60%', content: ['experience', 'skills', 'projects'] }
        ],
        spacing: { margin: '1rem', padding: '1.5rem', gap: '2rem' },
        breakpoints: { mobile: '768px', tablet: '1024px', desktop: '1200px' }
      },
      sections: [
        { id: 'header', name: 'Executive Profile', type: 'header', required: true, order: 1 },
        { id: 'summary', name: 'Executive Summary', type: 'summary', required: true, order: 2 },
        { id: 'experience', name: 'Leadership Experience', type: 'experience', required: true, order: 3 },
        { id: 'education', name: 'Education & Credentials', type: 'education', required: true, order: 4 },
        { id: 'skills', name: 'Core Competencies', type: 'skills', required: false, order: 5 },
        { id: 'projects', name: 'Key Achievements', type: 'projects', required: false, order: 6 }
      ],
      theme: {
        name: 'Executive Navy',
        colors: {
          primary: '#1e3a8a', secondary: '#64748b', accent: '#dc2626',
          text: '#0f172a', textLight: '#475569', background: '#ffffff',
          surface: '#f8fafc', border: '#cbd5e1', success: '#059669',
          warning: '#d97706', error: '#dc2626'
        },
        typography: {
          fontFamily: { primary: 'Playfair Display, serif', secondary: 'Source Sans Pro, sans-serif' },
          fontSize: {
            xs: '0.75rem', sm: '0.875rem', base: '1rem', lg: '1.125rem',
            xl: '1.25rem', '2xl': '1.5rem', '3xl': '1.875rem', '4xl': '2.25rem'
          },
          fontWeight: { normal: '400', medium: '500', semibold: '600', bold: '700' },
          lineHeight: { tight: '1.25', normal: '1.5', relaxed: '1.75' }
        },
        spacing: {
          xs: '0.25rem', sm: '0.5rem', md: '1rem', lg: '1.5rem',
          xl: '2rem', '2xl': '3rem', '3xl': '4rem'
        },
        borderRadius: { none: '0', sm: '0.125rem', md: '0.375rem', lg: '0.5rem', full: '9999px' },
        shadows: {
          sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)', md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
          lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)', xl: '0 20px 25px -5px rgb(0 0 0 / 0.1)'
        }
      },
      settings: {
        pageSize: 'A4', orientation: 'portrait',
        margins: { top: '1in', right: '1in', bottom: '1in', left: '1in' },
        dpi: 300, quality: 'high'
      },
      ats: {
        friendly: true, keywords: [],
        structure: { useSemanticHTML: true, includeMetadata: true, optimizeForParsing: true }
      },
      accessibility: { wcagLevel: 'AA', highContrast: false, screenReaderOptimized: true, keyboardNavigation: true },
      i18n: { defaultLanguage: 'en', supportedLanguages: ['en'], rtlSupport: false, dateFormat: 'MM/yyyy', numberFormat: 'en-US' },
      preview: { demoData: {} },
      metadata: {
        createdAt: '2024-01-01T00:00:00Z', updatedAt: '2024-01-01T00:00:00Z',
        downloads: 0, rating: 0, featured: true, premium: true
      }
    }
  }
}
