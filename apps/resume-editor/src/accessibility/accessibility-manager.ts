/**
 * Accessibility Manager
 * WCAG 2.2 AA compliance, keyboard navigation, and screen reader support
 */

export interface AccessibilityOptions {
  enableKeyboardNavigation: boolean
  enableScreenReaderSupport: boolean
  enableHighContrast: boolean
  enableReducedMotion: boolean
  enableFocusVisible: boolean
  announceChanges: boolean
}

export interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  metaKey?: boolean
  action: () => void
  description: string
  category: string
}

export interface AccessibilityAnnouncement {
  message: string
  priority: 'polite' | 'assertive'
  delay?: number
}

export class AccessibilityManager {
  private options: AccessibilityOptions
  private shortcuts: Map<string, KeyboardShortcut> = new Map()
  private announcer: HTMLElement | null = null
  private focusHistory: HTMLElement[] = []
  private currentFocusIndex = -1

  constructor(options: Partial<AccessibilityOptions> = {}) {
    this.options = {
      enableKeyboardNavigation: true,
      enableScreenReaderSupport: true,
      enableHighContrast: false,
      enableReducedMotion: false,
      enableFocusVisible: true,
      announceChanges: true,
      ...options
    }

    this.initialize()
  }

  private initialize(): void {
    this.setupScreenReaderSupport()
    this.setupKeyboardNavigation()
    this.setupFocusManagement()
    this.setupAccessibilityPreferences()
    this.registerDefaultShortcuts()
  }

  // Screen Reader Support
  private setupScreenReaderSupport(): void {
    if (!this.options.enableScreenReaderSupport) return

    // Create live region for announcements
    this.announcer = document.createElement('div')
    this.announcer.setAttribute('aria-live', 'polite')
    this.announcer.setAttribute('aria-atomic', 'true')
    this.announcer.className = 'sr-only'
    this.announcer.style.cssText = `
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    `
    document.body.appendChild(this.announcer)

    // Add skip links
    this.addSkipLinks()
  }

  private addSkipLinks(): void {
    const skipLinks = document.createElement('div')
    skipLinks.className = 'skip-links'
    skipLinks.innerHTML = `
      <a href="#main-content" class="skip-link">Skip to main content</a>
      <a href="#navigation" class="skip-link">Skip to navigation</a>
      <a href="#footer" class="skip-link">Skip to footer</a>
    `
    
    // Style skip links
    const style = document.createElement('style')
    style.textContent = `
      .skip-links {
        position: absolute;
        top: -40px;
        left: 6px;
        z-index: 1000;
      }
      .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: #000;
        color: #fff;
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1001;
      }
      .skip-link:focus {
        top: 6px;
      }
    `
    document.head.appendChild(style)
    document.body.insertBefore(skipLinks, document.body.firstChild)
  }

  // Keyboard Navigation
  private setupKeyboardNavigation(): void {
    if (!this.options.enableKeyboardNavigation) return

    document.addEventListener('keydown', this.handleKeyDown.bind(this))
    document.addEventListener('keyup', this.handleKeyUp.bind(this))
  }

  private handleKeyDown(event: KeyboardEvent): void {
    const shortcutKey = this.getShortcutKey(event)
    const shortcut = this.shortcuts.get(shortcutKey)

    if (shortcut) {
      event.preventDefault()
      shortcut.action()
      this.announce(`Executed: ${shortcut.description}`)
      return
    }

    // Handle arrow key navigation
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
      this.handleArrowNavigation(event)
    }

    // Handle Tab navigation
    if (event.key === 'Tab') {
      this.handleTabNavigation(event)
    }

    // Handle Escape key
    if (event.key === 'Escape') {
      this.handleEscape()
    }
  }

  private handleKeyUp(event: KeyboardEvent): void {
    // Handle any key up events if needed
  }

  private handleArrowNavigation(event: KeyboardEvent): void {
    const focusableElements = this.getFocusableElements()
    const currentIndex = focusableElements.indexOf(document.activeElement as HTMLElement)

    if (currentIndex === -1) return

    let nextIndex = currentIndex

    switch (event.key) {
      case 'ArrowUp':
        nextIndex = Math.max(0, currentIndex - 1)
        break
      case 'ArrowDown':
        nextIndex = Math.min(focusableElements.length - 1, currentIndex + 1)
        break
      case 'ArrowLeft':
        // Handle left navigation in specific contexts
        break
      case 'ArrowRight':
        // Handle right navigation in specific contexts
        break
    }

    if (nextIndex !== currentIndex) {
      event.preventDefault()
      focusableElements[nextIndex]?.focus()
    }
  }

  private handleTabNavigation(event: KeyboardEvent): void {
    const focusableElements = this.getFocusableElements()
    
    if (focusableElements.length === 0) return

    const currentIndex = focusableElements.indexOf(document.activeElement as HTMLElement)
    
    if (event.shiftKey) {
      // Shift+Tab (backward)
      if (currentIndex <= 0) {
        event.preventDefault()
        focusableElements[focusableElements.length - 1]?.focus()
      }
    } else {
      // Tab (forward)
      if (currentIndex >= focusableElements.length - 1) {
        event.preventDefault()
        focusableElements[0]?.focus()
      }
    }
  }

  private handleEscape(): void {
    // Close modals, dropdowns, etc.
    const modals = document.querySelectorAll('[role="dialog"][aria-modal="true"]')
    const dropdowns = document.querySelectorAll('[aria-expanded="true"]')
    
    if (modals.length > 0) {
      const lastModal = modals[modals.length - 1] as HTMLElement
      const closeButton = lastModal.querySelector('[aria-label*="close"], [aria-label*="Close"]') as HTMLElement
      closeButton?.click()
    } else if (dropdowns.length > 0) {
      dropdowns.forEach(dropdown => {
        dropdown.setAttribute('aria-expanded', 'false')
      })
    }
  }

  // Focus Management
  private setupFocusManagement(): void {
    if (!this.options.enableFocusVisible) return

    document.addEventListener('focusin', this.handleFocusIn.bind(this))
    document.addEventListener('focusout', this.handleFocusOut.bind(this))
  }

  private handleFocusIn(event: FocusEvent): void {
    const target = event.target as HTMLElement
    
    // Add to focus history
    this.focusHistory.push(target)
    this.currentFocusIndex = this.focusHistory.length - 1

    // Announce focus change for screen readers
    if (this.options.announceChanges) {
      const label = this.getElementLabel(target)
      if (label) {
        this.announce(`Focused: ${label}`, 'polite')
      }
    }
  }

  private handleFocusOut(event: FocusEvent): void {
    // Handle focus out if needed
  }

  // Accessibility Preferences
  private setupAccessibilityPreferences(): void {
    // Detect user preferences
    this.detectReducedMotion()
    this.detectHighContrast()
    this.detectColorScheme()
  }

  private detectReducedMotion(): void {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches
    
    if (prefersReducedMotion) {
      this.options.enableReducedMotion = true
      document.documentElement.classList.add('reduce-motion')
    }

    // Listen for changes
    window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
      this.options.enableReducedMotion = e.matches
      document.documentElement.classList.toggle('reduce-motion', e.matches)
    })
  }

  private detectHighContrast(): void {
    const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches
    
    if (prefersHighContrast) {
      this.options.enableHighContrast = true
      document.documentElement.classList.add('high-contrast')
    }

    // Listen for changes
    window.matchMedia('(prefers-contrast: high)').addEventListener('change', (e) => {
      this.options.enableHighContrast = e.matches
      document.documentElement.classList.toggle('high-contrast', e.matches)
    })
  }

  private detectColorScheme(): void {
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    
    document.documentElement.classList.toggle('dark', prefersDark)

    // Listen for changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      document.documentElement.classList.toggle('dark', e.matches)
    })
  }

  // Keyboard Shortcuts
  registerShortcut(shortcut: KeyboardShortcut): void {
    const key = this.getShortcutKey({
      key: shortcut.key,
      ctrlKey: shortcut.ctrlKey || false,
      altKey: shortcut.altKey || false,
      shiftKey: shortcut.shiftKey || false,
      metaKey: shortcut.metaKey || false
    } as KeyboardEvent)

    this.shortcuts.set(key, shortcut)
  }

  unregisterShortcut(key: string): void {
    this.shortcuts.delete(key)
  }

  getShortcuts(): KeyboardShortcut[] {
    return Array.from(this.shortcuts.values())
  }

  private registerDefaultShortcuts(): void {
    // Navigation shortcuts
    this.registerShortcut({
      key: 'h',
      altKey: true,
      action: () => this.focusElement('#main-navigation'),
      description: 'Go to main navigation',
      category: 'Navigation'
    })

    this.registerShortcut({
      key: 'm',
      altKey: true,
      action: () => this.focusElement('#main-content'),
      description: 'Go to main content',
      category: 'Navigation'
    })

    // Editor shortcuts
    this.registerShortcut({
      key: 's',
      ctrlKey: true,
      action: () => this.triggerSave(),
      description: 'Save document',
      category: 'Editor'
    })

    this.registerShortcut({
      key: 'z',
      ctrlKey: true,
      action: () => this.triggerUndo(),
      description: 'Undo last action',
      category: 'Editor'
    })

    this.registerShortcut({
      key: 'y',
      ctrlKey: true,
      action: () => this.triggerRedo(),
      description: 'Redo last action',
      category: 'Editor'
    })

    // Accessibility shortcuts
    this.registerShortcut({
      key: '?',
      shiftKey: true,
      action: () => this.showKeyboardShortcuts(),
      description: 'Show keyboard shortcuts',
      category: 'Help'
    })
  }

  // Public Methods
  announce(message: string, priority: 'polite' | 'assertive' = 'polite', delay: number = 100): void {
    if (!this.announcer || !this.options.announceChanges) return

    setTimeout(() => {
      if (this.announcer) {
        this.announcer.setAttribute('aria-live', priority)
        this.announcer.textContent = message
        
        // Clear after announcement
        setTimeout(() => {
          if (this.announcer) {
            this.announcer.textContent = ''
          }
        }, 1000)
      }
    }, delay)
  }

  focusElement(selector: string): void {
    const element = document.querySelector(selector) as HTMLElement
    if (element) {
      element.focus()
      this.announce(`Focused: ${this.getElementLabel(element)}`)
    }
  }

  getFocusableElements(): HTMLElement[] {
    const selector = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ')

    return Array.from(document.querySelectorAll(selector)) as HTMLElement[]
  }

  // Utility Methods
  private getShortcutKey(event: KeyboardEvent): string {
    const parts = []
    if (event.ctrlKey) parts.push('ctrl')
    if (event.altKey) parts.push('alt')
    if (event.shiftKey) parts.push('shift')
    if (event.metaKey) parts.push('meta')
    parts.push(event.key.toLowerCase())
    return parts.join('+')
  }

  private getElementLabel(element: HTMLElement): string {
    return (
      element.getAttribute('aria-label') ||
      element.getAttribute('aria-labelledby') ||
      element.getAttribute('title') ||
      element.textContent?.trim() ||
      element.tagName.toLowerCase()
    )
  }

  private triggerSave(): void {
    const saveButton = document.querySelector('[data-action="save"]') as HTMLElement
    saveButton?.click()
  }

  private triggerUndo(): void {
    const undoButton = document.querySelector('[data-action="undo"]') as HTMLElement
    undoButton?.click()
  }

  private triggerRedo(): void {
    const redoButton = document.querySelector('[data-action="redo"]') as HTMLElement
    redoButton?.click()
  }

  private showKeyboardShortcuts(): void {
    // Trigger keyboard shortcuts modal
    const shortcutsButton = document.querySelector('[data-action="show-shortcuts"]') as HTMLElement
    shortcutsButton?.click()
  }

  // Cleanup
  destroy(): void {
    document.removeEventListener('keydown', this.handleKeyDown.bind(this))
    document.removeEventListener('keyup', this.handleKeyUp.bind(this))
    document.removeEventListener('focusin', this.handleFocusIn.bind(this))
    document.removeEventListener('focusout', this.handleFocusOut.bind(this))

    if (this.announcer) {
      document.body.removeChild(this.announcer)
      this.announcer = null
    }

    this.shortcuts.clear()
    this.focusHistory = []
  }
}
