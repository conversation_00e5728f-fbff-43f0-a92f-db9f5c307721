/**
 * Live Content Linting System
 * Real-time grammar, consistency, and ATS optimization analysis
 */

import { ResumeDocument, CustomElement } from '@/types/resume'
import { debounce } from 'lodash-es'

export interface LintIssue {
  id: string
  type: 'grammar' | 'spelling' | 'consistency' | 'ats' | 'style' | 'structure'
  severity: 'error' | 'warning' | 'info' | 'suggestion'
  message: string
  description: string
  suggestion?: string
  position: {
    path: number[]
    start: number
    end: number
  }
  rule: string
  category: string
  fixable: boolean
  confidence: number
}

export interface LintingResult {
  issues: LintIssue[]
  summary: {
    errors: number
    warnings: number
    suggestions: number
    score: number
  }
  metrics: {
    readabilityScore: number
    atsScore: number
    consistencyScore: number
    grammarScore: number
  }
  processingTime: number
}

export interface LintingRule {
  id: string
  name: string
  description: string
  category: string
  severity: LintIssue['severity']
  enabled: boolean
  pattern?: RegExp
  check: (text: string, context: LintingContext) => LintIssue[]
}

export interface LintingContext {
  document: ResumeDocument
  section: CustomElement
  sectionIndex: number
  allText: string
  previousSections: string[]
  targetRole?: string
  industry?: string
}

export class LiveLinter {
  private rules: Map<string, LintingRule> = new Map()
  private isInitialized = false
  private debouncedLint = debounce(this.performLinting.bind(this), 300)
  private cache = new Map<string, LintingResult>()
  
  // External services
  private grammarChecker: any = null
  private spellChecker: any = null
  private atsAnalyzer: any = null

  constructor() {
    this.initializeRules()
  }

  async initialize(): Promise<void> {
    try {
      // Initialize external services
      await this.initializeGrammarChecker()
      await this.initializeSpellChecker()
      await this.initializeATSAnalyzer()
      
      this.isInitialized = true
      console.log('✅ Live Linter initialized')
    } catch (error) {
      console.warn('⚠️ Some linting services unavailable, using basic rules only')
      this.isInitialized = true
    }
  }

  async lintDocument(document: ResumeDocument, targetRole?: string): Promise<LintingResult> {
    const cacheKey = this.generateCacheKey(document, targetRole)
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!
    }

    const result = await this.performLinting(document, targetRole)
    this.cache.set(cacheKey, result)
    
    // Clean cache if it gets too large
    if (this.cache.size > 100) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }

    return result
  }

  async lintDocumentDebounced(document: ResumeDocument, targetRole?: string): Promise<void> {
    this.debouncedLint(document, targetRole)
  }

  private async performLinting(document: ResumeDocument, targetRole?: string): Promise<LintingResult> {
    const startTime = performance.now()
    const allIssues: LintIssue[] = []

    // Lint each section
    for (let sectionIndex = 0; sectionIndex < document.content.length; sectionIndex++) {
      const section = document.content[sectionIndex]
      const context: LintingContext = {
        document,
        section,
        sectionIndex,
        allText: this.extractAllText(document),
        previousSections: document.content.slice(0, sectionIndex).map(s => this.extractSectionText(s)),
        targetRole,
        industry: document.metadata.industry
      }

      const sectionIssues = await this.lintSection(section, context)
      allIssues.push(...sectionIssues)
    }

    // Document-level checks
    const documentIssues = await this.lintDocumentStructure(document, targetRole)
    allIssues.push(...documentIssues)

    // Calculate metrics
    const metrics = this.calculateMetrics(document, allIssues)
    const summary = this.calculateSummary(allIssues, metrics)

    const processingTime = performance.now() - startTime

    return {
      issues: allIssues,
      summary,
      metrics,
      processingTime
    }
  }

  private async lintSection(section: CustomElement, context: LintingContext): Promise<LintIssue[]> {
    const issues: LintIssue[] = []
    const sectionText = this.extractSectionText(section)

    // Apply all enabled rules
    for (const rule of this.rules.values()) {
      if (!rule.enabled) continue

      try {
        const ruleIssues = rule.check(sectionText, context)
        issues.push(...ruleIssues)
      } catch (error) {
        console.warn(`Rule ${rule.id} failed:`, error)
      }
    }

    // External service checks
    if (this.grammarChecker) {
      const grammarIssues = await this.checkGrammar(sectionText, context)
      issues.push(...grammarIssues)
    }

    if (this.spellChecker) {
      const spellingIssues = await this.checkSpelling(sectionText, context)
      issues.push(...spellingIssues)
    }

    return issues
  }

  private async lintDocumentStructure(document: ResumeDocument, targetRole?: string): Promise<LintIssue[]> {
    const issues: LintIssue[] = []

    // Check section order
    const sectionOrder = document.content.map(s => s.type)
    const recommendedOrder = this.getRecommendedSectionOrder(targetRole)
    
    if (!this.isOptimalOrder(sectionOrder, recommendedOrder)) {
      issues.push({
        id: `structure-order-${Date.now()}`,
        type: 'structure',
        severity: 'suggestion',
        message: 'Section order could be optimized',
        description: 'Consider reordering sections for better impact',
        position: { path: [], start: 0, end: 0 },
        rule: 'section-order',
        category: 'structure',
        fixable: true,
        confidence: 0.7
      })
    }

    // Check for missing essential sections
    const essentialSections = ['experience', 'education', 'skills']
    const missingSections = essentialSections.filter(section => 
      !sectionOrder.includes(section)
    )

    for (const missing of missingSections) {
      issues.push({
        id: `missing-${missing}-${Date.now()}`,
        type: 'structure',
        severity: 'warning',
        message: `Missing ${missing} section`,
        description: `Consider adding a ${missing} section for completeness`,
        position: { path: [], start: 0, end: 0 },
        rule: 'essential-sections',
        category: 'structure',
        fixable: false,
        confidence: 0.9
      })
    }

    return issues
  }

  private initializeRules(): void {
    // Grammar and style rules
    this.addRule({
      id: 'passive-voice',
      name: 'Passive Voice Detection',
      description: 'Detects passive voice usage',
      category: 'style',
      severity: 'suggestion',
      enabled: true,
      pattern: /\b(was|were|been|being)\s+\w+ed\b/gi,
      check: (text, context) => {
        const issues: LintIssue[] = []
        const matches = text.matchAll(/\b(was|were|been|being)\s+(\w+ed)\b/gi)
        
        for (const match of matches) {
          if (match.index !== undefined) {
            issues.push({
              id: `passive-voice-${match.index}`,
              type: 'style',
              severity: 'suggestion',
              message: 'Consider using active voice',
              description: 'Active voice is more engaging and direct',
              suggestion: `Consider rephrasing to use active voice`,
              position: {
                path: [context.sectionIndex],
                start: match.index,
                end: match.index + match[0].length
              },
              rule: 'passive-voice',
              category: 'style',
              fixable: false,
              confidence: 0.8
            })
          }
        }
        
        return issues
      }
    })

    // Consistency rules
    this.addRule({
      id: 'date-format-consistency',
      name: 'Date Format Consistency',
      description: 'Ensures consistent date formatting',
      category: 'consistency',
      severity: 'warning',
      enabled: true,
      check: (text, context) => {
        const issues: LintIssue[] = []
        const datePatterns = [
          /\b\d{1,2}\/\d{4}\b/g,  // MM/YYYY
          /\b\w+\s+\d{4}\b/g,     // Month YYYY
          /\b\d{4}-\d{2}\b/g,     // YYYY-MM
          /\b\d{4}\b/g            // YYYY only
        ]

        const foundFormats = new Set<string>()
        
        for (const pattern of datePatterns) {
          const matches = text.matchAll(pattern)
          for (const match of matches) {
            foundFormats.add(this.classifyDateFormat(match[0]))
          }
        }

        if (foundFormats.size > 1) {
          issues.push({
            id: `date-inconsistency-${context.sectionIndex}`,
            type: 'consistency',
            severity: 'warning',
            message: 'Inconsistent date formats detected',
            description: 'Use consistent date formatting throughout the resume',
            position: { path: [context.sectionIndex], start: 0, end: text.length },
            rule: 'date-format-consistency',
            category: 'consistency',
            fixable: true,
            confidence: 0.9
          })
        }

        return issues
      }
    })

    // ATS optimization rules
    this.addRule({
      id: 'ats-keywords',
      name: 'ATS Keyword Optimization',
      description: 'Checks for relevant keywords',
      category: 'ats',
      severity: 'info',
      enabled: true,
      check: (text, context) => {
        const issues: LintIssue[] = []
        
        if (!context.targetRole) return issues

        const roleKeywords = this.getRoleKeywords(context.targetRole)
        const textLower = text.toLowerCase()
        const missingKeywords = roleKeywords.filter(keyword => 
          !textLower.includes(keyword.toLowerCase())
        )

        if (missingKeywords.length > 0 && context.section.type === 'skills') {
          issues.push({
            id: `missing-keywords-${context.sectionIndex}`,
            type: 'ats',
            severity: 'info',
            message: 'Consider adding relevant keywords',
            description: `Missing keywords: ${missingKeywords.slice(0, 3).join(', ')}`,
            position: { path: [context.sectionIndex], start: 0, end: text.length },
            rule: 'ats-keywords',
            category: 'ats',
            fixable: false,
            confidence: 0.6
          })
        }

        return issues
      }
    })

    // Action verb rules
    this.addRule({
      id: 'action-verbs',
      name: 'Action Verb Usage',
      description: 'Encourages strong action verbs',
      category: 'style',
      severity: 'suggestion',
      enabled: true,
      check: (text, context) => {
        const issues: LintIssue[] = []
        
        if (context.section.type !== 'experience') return issues

        const weakVerbs = ['responsible for', 'worked on', 'helped with', 'involved in']
        const textLower = text.toLowerCase()
        
        for (const weakVerb of weakVerbs) {
          if (textLower.includes(weakVerb)) {
            const index = textLower.indexOf(weakVerb)
            issues.push({
              id: `weak-verb-${index}`,
              type: 'style',
              severity: 'suggestion',
              message: 'Consider using stronger action verbs',
              description: `"${weakVerb}" could be replaced with more impactful language`,
              suggestion: 'Use specific action verbs like "managed", "developed", "implemented"',
              position: {
                path: [context.sectionIndex],
                start: index,
                end: index + weakVerb.length
              },
              rule: 'action-verbs',
              category: 'style',
              fixable: false,
              confidence: 0.7
            })
          }
        }

        return issues
      }
    })

    // Quantification rules
    this.addRule({
      id: 'quantify-achievements',
      name: 'Quantify Achievements',
      description: 'Encourages quantifiable results',
      category: 'content',
      severity: 'suggestion',
      enabled: true,
      check: (text, context) => {
        const issues: LintIssue[] = []
        
        if (context.section.type !== 'experience') return issues

        const hasNumbers = /\d+/.test(text)
        const hasPercentage = /%/.test(text)
        const hasCurrency = /\$/.test(text)
        
        if (!hasNumbers && !hasPercentage && !hasCurrency && text.length > 100) {
          issues.push({
            id: `quantify-${context.sectionIndex}`,
            type: 'style',
            severity: 'suggestion',
            message: 'Consider adding quantifiable results',
            description: 'Numbers, percentages, and metrics make achievements more impactful',
            position: { path: [context.sectionIndex], start: 0, end: text.length },
            rule: 'quantify-achievements',
            category: 'content',
            fixable: false,
            confidence: 0.6
          })
        }

        return issues
      }
    })
  }

  private addRule(rule: LintingRule): void {
    this.rules.set(rule.id, rule)
  }

  private async initializeGrammarChecker(): Promise<void> {
    // In production, integrate with services like Grammarly API or LanguageTool
    // For now, use a mock implementation
    this.grammarChecker = {
      check: async (text: string) => {
        // Mock grammar checking
        return []
      }
    }
  }

  private async initializeSpellChecker(): Promise<void> {
    // In production, integrate with spell checking services
    this.spellChecker = {
      check: async (text: string) => {
        // Mock spell checking
        return []
      }
    }
  }

  private async initializeATSAnalyzer(): Promise<void> {
    // In production, integrate with ATS analysis services
    this.atsAnalyzer = {
      analyze: async (document: ResumeDocument) => {
        // Mock ATS analysis
        return { score: 0.8, issues: [] }
      }
    }
  }

  private async checkGrammar(text: string, context: LintingContext): Promise<LintIssue[]> {
    if (!this.grammarChecker) return []

    try {
      const grammarErrors = await this.grammarChecker.check(text)
      return grammarErrors.map((error: any) => ({
        id: `grammar-${error.offset}`,
        type: 'grammar' as const,
        severity: 'error' as const,
        message: error.message,
        description: error.description || 'Grammar error detected',
        suggestion: error.suggestion,
        position: {
          path: [context.sectionIndex],
          start: error.offset,
          end: error.offset + error.length
        },
        rule: 'grammar-check',
        category: 'grammar',
        fixable: !!error.suggestion,
        confidence: error.confidence || 0.9
      }))
    } catch (error) {
      console.warn('Grammar check failed:', error)
      return []
    }
  }

  private async checkSpelling(text: string, context: LintingContext): Promise<LintIssue[]> {
    if (!this.spellChecker) return []

    try {
      const spellingErrors = await this.spellChecker.check(text)
      return spellingErrors.map((error: any) => ({
        id: `spelling-${error.offset}`,
        type: 'spelling' as const,
        severity: 'error' as const,
        message: `Possible spelling error: "${error.word}"`,
        description: 'Spelling error detected',
        suggestion: error.suggestions?.[0],
        position: {
          path: [context.sectionIndex],
          start: error.offset,
          end: error.offset + error.word.length
        },
        rule: 'spell-check',
        category: 'spelling',
        fixable: !!error.suggestions?.length,
        confidence: 0.95
      }))
    } catch (error) {
      console.warn('Spell check failed:', error)
      return []
    }
  }

  private calculateMetrics(document: ResumeDocument, issues: LintIssue[]): LintingResult['metrics'] {
    const allText = this.extractAllText(document)
    
    return {
      readabilityScore: this.calculateReadabilityScore(allText),
      atsScore: this.calculateATSScore(document, issues),
      consistencyScore: this.calculateConsistencyScore(issues),
      grammarScore: this.calculateGrammarScore(issues)
    }
  }

  private calculateSummary(issues: LintIssue[], metrics: LintingResult['metrics']): LintingResult['summary'] {
    const errors = issues.filter(i => i.severity === 'error').length
    const warnings = issues.filter(i => i.severity === 'warning').length
    const suggestions = issues.filter(i => i.severity === 'suggestion' || i.severity === 'info').length
    
    // Calculate overall score (0-100)
    const score = Math.round(
      (metrics.readabilityScore * 0.25 +
       metrics.atsScore * 0.25 +
       metrics.consistencyScore * 0.25 +
       metrics.grammarScore * 0.25) * 100
    )

    return { errors, warnings, suggestions, score }
  }

  private extractAllText(document: ResumeDocument): string {
    return document.content.map(section => this.extractSectionText(section)).join(' ')
  }

  private extractSectionText(section: CustomElement): string {
    // Simplified text extraction - in production, implement proper content parsing
    return JSON.stringify(section).replace(/[{}"[\],]/g, ' ').replace(/\s+/g, ' ').trim()
  }

  private generateCacheKey(document: ResumeDocument, targetRole?: string): string {
    const contentHash = this.hashContent(document.content)
    return `${contentHash}-${targetRole || 'general'}`
  }

  private hashContent(content: any): string {
    return btoa(JSON.stringify(content)).slice(0, 16)
  }

  private getRecommendedSectionOrder(targetRole?: string): string[] {
    // Default order, can be customized based on role
    return ['header', 'summary', 'experience', 'education', 'skills', 'projects', 'certifications']
  }

  private isOptimalOrder(current: string[], recommended: string[]): boolean {
    const currentFiltered = current.filter(s => recommended.includes(s))
    const recommendedFiltered = recommended.filter(s => current.includes(s))
    
    return JSON.stringify(currentFiltered) === JSON.stringify(recommendedFiltered)
  }

  private getRoleKeywords(role: string): string[] {
    const keywordMap: Record<string, string[]> = {
      'software engineer': ['JavaScript', 'Python', 'React', 'Node.js', 'Git', 'API', 'Database'],
      'data scientist': ['Python', 'R', 'Machine Learning', 'SQL', 'Statistics', 'Pandas', 'TensorFlow'],
      'product manager': ['Agile', 'Scrum', 'Roadmap', 'Analytics', 'Stakeholder', 'Strategy'],
      'marketing manager': ['Campaign', 'Analytics', 'SEO', 'Content', 'Brand', 'Digital Marketing']
    }
    
    return keywordMap[role.toLowerCase()] || []
  }

  private classifyDateFormat(date: string): string {
    if (/\d{1,2}\/\d{4}/.test(date)) return 'MM/YYYY'
    if (/\w+\s+\d{4}/.test(date)) return 'Month YYYY'
    if (/\d{4}-\d{2}/.test(date)) return 'YYYY-MM'
    if (/^\d{4}$/.test(date)) return 'YYYY'
    return 'other'
  }

  private calculateReadabilityScore(text: string): number {
    // Simplified Flesch Reading Ease calculation
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const words = text.split(/\s+/).filter(w => w.length > 0)
    
    if (sentences.length === 0 || words.length === 0) return 0

    const avgWordsPerSentence = words.length / sentences.length
    const avgSyllablesPerWord = words.reduce((acc, word) => 
      acc + this.countSyllables(word), 0) / words.length
    
    const fleschScore = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord)
    
    // Normalize to 0-1 scale
    return Math.max(0, Math.min(1, (fleschScore - 30) / 70))
  }

  private calculateATSScore(document: ResumeDocument, issues: LintIssue[]): number {
    const atsIssues = issues.filter(i => i.category === 'ats')
    const maxPenalty = 0.5
    const penalty = Math.min(maxPenalty, atsIssues.length * 0.1)
    
    return Math.max(0, 1 - penalty)
  }

  private calculateConsistencyScore(issues: LintIssue[]): number {
    const consistencyIssues = issues.filter(i => i.category === 'consistency')
    const maxPenalty = 0.4
    const penalty = Math.min(maxPenalty, consistencyIssues.length * 0.1)
    
    return Math.max(0, 1 - penalty)
  }

  private calculateGrammarScore(issues: LintIssue[]): number {
    const grammarIssues = issues.filter(i => i.type === 'grammar' || i.type === 'spelling')
    const maxPenalty = 0.6
    const penalty = Math.min(maxPenalty, grammarIssues.length * 0.05)
    
    return Math.max(0, 1 - penalty)
  }

  private countSyllables(word: string): number {
    // Simplified syllable counting
    const vowels = word.toLowerCase().match(/[aeiouy]+/g)
    return vowels ? vowels.length : 1
  }

  // Public API
  enableRule(ruleId: string): void {
    const rule = this.rules.get(ruleId)
    if (rule) {
      rule.enabled = true
    }
  }

  disableRule(ruleId: string): void {
    const rule = this.rules.get(ruleId)
    if (rule) {
      rule.enabled = false
    }
  }

  getRules(): LintingRule[] {
    return Array.from(this.rules.values())
  }

  clearCache(): void {
    this.cache.clear()
  }
}
