/**
 * React hook for Magic Layout AI integration
 * Provides real-time layout optimization suggestions
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { MagicLayoutEngine, LayoutSuggestion, LayoutAnalysis } from '@/ai/magic-layout-engine'
import { ResumeDocument } from '@/types/resume'
import { TemplateConfig } from '@/templates/template-engine'
import { debounce } from 'lodash-es'

export interface UseMagicLayoutOptions {
  enabled?: boolean
  debounceMs?: number
  autoApply?: boolean
  targetRole?: string
  maxSuggestions?: number
}

export interface MagicLayoutState {
  isLoading: boolean
  isAnalyzing: boolean
  analysis: LayoutAnalysis | null
  suggestions: LayoutSuggestion[]
  appliedSuggestions: Set<string>
  error: string | null
  isAvailable: boolean
}

export interface MagicLayoutActions {
  analyzeLa yout: (document: ResumeDocument, template?: TemplateConfig) => Promise<void>
  generateSuggestions: (document: ResumeDocument, targetRole?: string) => Promise<void>
  applySuggestion: (suggestionId: string) => Promise<ResumeDocument | null>
  applySuggestions: (suggestionIds: string[]) => Promise<ResumeDocument | null>
  dismissSuggestion: (suggestionId: string) => void
  clearSuggestions: () => void
  refreshAnalysis: () => Promise<void>
}

export function useMagicLayout(
  document: ResumeDocument | null,
  options: UseMagicLayoutOptions = {}
): [MagicLayoutState, MagicLayoutActions] {
  const {
    enabled = true,
    debounceMs = 1000,
    autoApply = false,
    targetRole,
    maxSuggestions = 10
  } = options

  // State
  const [state, setState] = useState<MagicLayoutState>({
    isLoading: false,
    isAnalyzing: false,
    analysis: null,
    suggestions: [],
    appliedSuggestions: new Set(),
    error: null,
    isAvailable: false
  })

  // Refs
  const engineRef = useRef<MagicLayoutEngine | null>(null)
  const documentRef = useRef<ResumeDocument | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Initialize engine
  useEffect(() => {
    const initializeEngine = async () => {
      try {
        setState(prev => ({ ...prev, isLoading: true, error: null }))
        
        const engine = MagicLayoutEngine.getInstance()
        await engine.initialize()
        
        engineRef.current = engine
        setState(prev => ({ 
          ...prev, 
          isLoading: false, 
          isAvailable: engine.isAvailable() 
        }))
      } catch (error) {
        setState(prev => ({ 
          ...prev, 
          isLoading: false, 
          error: error instanceof Error ? error.message : 'Failed to initialize AI engine',
          isAvailable: false
        }))
      }
    }

    initializeEngine()
  }, [])

  // Debounced analysis
  const debouncedAnalysis = useCallback(
    debounce(async (doc: ResumeDocument, template?: TemplateConfig) => {
      if (!engineRef.current || !enabled) return

      try {
        setState(prev => ({ ...prev, isAnalyzing: true, error: null }))
        
        const analysis = await engineRef.current.analyzeLayout(doc, template)
        
        setState(prev => ({ 
          ...prev, 
          analysis, 
          isAnalyzing: false 
        }))
      } catch (error) {
        setState(prev => ({ 
          ...prev, 
          isAnalyzing: false,
          error: error instanceof Error ? error.message : 'Analysis failed'
        }))
      }
    }, debounceMs),
    [enabled, debounceMs]
  )

  // Auto-analyze when document changes
  useEffect(() => {
    if (document && document !== documentRef.current) {
      documentRef.current = document
      debouncedAnalysis(document)
    }
  }, [document, debouncedAnalysis])

  // Actions
  const analyzeLayout = useCallback(async (
    doc: ResumeDocument, 
    template?: TemplateConfig
  ): Promise<void> => {
    if (!engineRef.current) {
      throw new Error('AI engine not initialized')
    }

    setState(prev => ({ ...prev, isAnalyzing: true, error: null }))

    try {
      const analysis = await engineRef.current.analyzeLayout(doc, template)
      setState(prev => ({ ...prev, analysis, isAnalyzing: false }))
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isAnalyzing: false,
        error: error instanceof Error ? error.message : 'Analysis failed'
      }))
      throw error
    }
  }, [])

  const generateSuggestions = useCallback(async (
    doc: ResumeDocument,
    role?: string
  ): Promise<void> => {
    if (!engineRef.current) {
      throw new Error('AI engine not initialized')
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      const suggestions = await engineRef.current.generateSuggestions(
        doc, 
        role || targetRole
      )

      // Filter and limit suggestions
      const filteredSuggestions = suggestions
        .filter(s => !state.appliedSuggestions.has(s.id))
        .slice(0, maxSuggestions)

      setState(prev => ({ 
        ...prev, 
        suggestions: filteredSuggestions, 
        isLoading: false 
      }))

      // Auto-apply high-confidence suggestions if enabled
      if (autoApply) {
        const highConfidenceSuggestions = filteredSuggestions
          .filter(s => s.confidence > 0.8 && s.impact === 'high')
          .slice(0, 3) // Limit auto-apply

        if (highConfidenceSuggestions.length > 0) {
          await applySuggestions(highConfidenceSuggestions.map(s => s.id))
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        setState(prev => ({ 
          ...prev, 
          isLoading: false,
          error: error.message
        }))
      }
    }
  }, [targetRole, maxSuggestions, autoApply, state.appliedSuggestions])

  const applySuggestion = useCallback(async (
    suggestionId: string
  ): Promise<ResumeDocument | null> => {
    if (!engineRef.current || !document) {
      return null
    }

    const suggestion = state.suggestions.find(s => s.id === suggestionId)
    if (!suggestion) {
      throw new Error('Suggestion not found')
    }

    try {
      const optimizedDocument = await engineRef.current.optimizeLayout(
        document,
        [suggestion],
        [suggestionId]
      )

      setState(prev => ({
        ...prev,
        appliedSuggestions: new Set([...prev.appliedSuggestions, suggestionId]),
        suggestions: prev.suggestions.filter(s => s.id !== suggestionId)
      }))

      return optimizedDocument
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to apply suggestion'
      }))
      throw error
    }
  }, [document, state.suggestions])

  const applySuggestions = useCallback(async (
    suggestionIds: string[]
  ): Promise<ResumeDocument | null> => {
    if (!engineRef.current || !document) {
      return null
    }

    const suggestions = state.suggestions.filter(s => suggestionIds.includes(s.id))
    if (suggestions.length === 0) {
      return document
    }

    try {
      const optimizedDocument = await engineRef.current.optimizeLayout(
        document,
        suggestions,
        suggestionIds
      )

      setState(prev => ({
        ...prev,
        appliedSuggestions: new Set([...prev.appliedSuggestions, ...suggestionIds]),
        suggestions: prev.suggestions.filter(s => !suggestionIds.includes(s.id))
      }))

      return optimizedDocument
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to apply suggestions'
      }))
      throw error
    }
  }, [document, state.suggestions])

  const dismissSuggestion = useCallback((suggestionId: string) => {
    setState(prev => ({
      ...prev,
      suggestions: prev.suggestions.filter(s => s.id !== suggestionId)
    }))
  }, [])

  const clearSuggestions = useCallback(() => {
    setState(prev => ({
      ...prev,
      suggestions: [],
      appliedSuggestions: new Set()
    }))
  }, [])

  const refreshAnalysis = useCallback(async (): Promise<void> => {
    if (document) {
      await analyzeLayout(document)
      await generateSuggestions(document, targetRole)
    }
  }, [document, targetRole, analyzeLayout, generateSuggestions])

  // Cleanup
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      debouncedAnalysis.cancel()
    }
  }, [debouncedAnalysis])

  const actions: MagicLayoutActions = {
    analyzeLayout,
    generateSuggestions,
    applySuggestion,
    applySuggestions,
    dismissSuggestion,
    clearSuggestions,
    refreshAnalysis
  }

  return [state, actions]
}

// Utility hook for suggestion filtering
export function useSuggestionFilters(suggestions: LayoutSuggestion[]) {
  const [filters, setFilters] = useState({
    category: 'all' as string,
    impact: 'all' as string,
    type: 'all' as string,
    minConfidence: 0
  })

  const filteredSuggestions = suggestions.filter(suggestion => {
    if (filters.category !== 'all' && suggestion.category !== filters.category) {
      return false
    }
    if (filters.impact !== 'all' && suggestion.impact !== filters.impact) {
      return false
    }
    if (filters.type !== 'all' && suggestion.type !== filters.type) {
      return false
    }
    if (suggestion.confidence < filters.minConfidence) {
      return false
    }
    return true
  })

  return {
    filters,
    setFilters,
    filteredSuggestions,
    categories: [...new Set(suggestions.map(s => s.category))],
    impacts: [...new Set(suggestions.map(s => s.impact))],
    types: [...new Set(suggestions.map(s => s.type))]
  }
}

// Hook for suggestion analytics
export function useSuggestionAnalytics(suggestions: LayoutSuggestion[]) {
  const analytics = {
    totalSuggestions: suggestions.length,
    highImpactCount: suggestions.filter(s => s.impact === 'high').length,
    highConfidenceCount: suggestions.filter(s => s.confidence > 0.8).length,
    averageConfidence: suggestions.length > 0 
      ? suggestions.reduce((sum, s) => sum + s.confidence, 0) / suggestions.length 
      : 0,
    categoryBreakdown: suggestions.reduce((acc, s) => {
      acc[s.category] = (acc[s.category] || 0) + 1
      return acc
    }, {} as Record<string, number>),
    impactBreakdown: suggestions.reduce((acc, s) => {
      acc[s.impact] = (acc[s.impact] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  }

  return analytics
}
