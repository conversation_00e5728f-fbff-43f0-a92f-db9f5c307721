/**
 * React hook for Git-style version control
 * Provides commit, branch, merge, and rollback functionality
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  VersionManager, 
  VersionCommit, 
  VersionBranch, 
  VersionDiff,
  VersionChange 
} from '@/version-control/version-manager'
import { ResumeDocument } from '@/types/resume'

export interface UseVersionControlOptions {
  autoCommit?: boolean
  autoCommitInterval?: number
  maxHistorySize?: number
}

export interface VersionControlState {
  currentCommit: VersionCommit | null
  currentBranch: string
  branches: VersionBranch[]
  history: VersionCommit[]
  isLoading: boolean
  hasUnsavedChanges: boolean
  error: string | null
}

export interface VersionControlActions {
  commit: (message: string, author?: VersionCommit['author']) => Promise<VersionCommit>
  amendCommit: (message?: string) => Promise<VersionCommit | null>
  createBranch: (name: string, description?: string) => VersionBranch
  switchBranch: (branchName: string) => Promise<ResumeDocument | null>
  mergeBranch: (sourceBranch: string, message?: string) => Promise<VersionCommit>
  rollbackToCommit: (commitId: string) => Promise<ResumeDocument | null>
  revertCommit: (commitId: string) => Promise<VersionCommit>
  getDiff: (fromCommitId: string, toCommitId: string) => VersionDiff
  getCommitHistory: (limit?: number) => VersionCommit[]
  getBranchHistory: (branchName: string, limit?: number) => VersionCommit[]
  refreshHistory: () => void
}

export function useVersionControl(
  document: ResumeDocument | null,
  options: UseVersionControlOptions = {}
): [VersionControlState, VersionControlActions] {
  const {
    autoCommit = false,
    autoCommitInterval = 30000,
    maxHistorySize = 100
  } = options

  // State
  const [state, setState] = useState<VersionControlState>({
    currentCommit: null,
    currentBranch: 'main',
    branches: [],
    history: [],
    isLoading: false,
    hasUnsavedChanges: false,
    error: null
  })

  // Refs
  const versionManagerRef = useRef<VersionManager | null>(null)
  const lastDocumentRef = useRef<ResumeDocument | null>(null)
  const autoCommitTimerRef = useRef<NodeJS.Timeout | null>(null)
  const currentUserRef = useRef({
    id: 'current-user',
    name: 'Current User',
    email: '<EMAIL>'
  })

  // Initialize version manager
  useEffect(() => {
    versionManagerRef.current = new VersionManager()
    refreshState()
  }, [])

  // Track document changes
  useEffect(() => {
    if (document && document !== lastDocumentRef.current) {
      lastDocumentRef.current = document
      setState(prev => ({ ...prev, hasUnsavedChanges: true }))

      // Setup auto-commit
      if (autoCommit) {
        if (autoCommitTimerRef.current) {
          clearTimeout(autoCommitTimerRef.current)
        }

        autoCommitTimerRef.current = setTimeout(() => {
          commit('Auto-commit: Document changes')
        }, autoCommitInterval)
      }
    }
  }, [document, autoCommit, autoCommitInterval])

  // Cleanup
  useEffect(() => {
    return () => {
      if (autoCommitTimerRef.current) {
        clearTimeout(autoCommitTimerRef.current)
      }
    }
  }, [])

  const refreshState = useCallback(() => {
    if (!versionManagerRef.current) return

    const manager = versionManagerRef.current
    
    setState(prev => ({
      ...prev,
      currentCommit: manager.getCurrentCommit(),
      currentBranch: manager.getCurrentBranch(),
      branches: manager.getBranches(),
      history: manager.getCommitHistory(20)
    }))
  }, [])

  // Actions
  const commit = useCallback(async (
    message: string,
    author?: VersionCommit['author']
  ): Promise<VersionCommit> => {
    if (!versionManagerRef.current || !document) {
      throw new Error('Version manager not initialized or no document')
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      const currentDoc = versionManagerRef.current.getCurrentDocument()
      const newCommit = await versionManagerRef.current.createCommit(
        document,
        message,
        author || currentUserRef.current,
        currentDoc || undefined
      )

      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        hasUnsavedChanges: false 
      }))
      
      refreshState()
      return newCommit
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: error instanceof Error ? error.message : 'Commit failed'
      }))
      throw error
    }
  }, [document, refreshState])

  const amendCommit = useCallback(async (message?: string): Promise<VersionCommit | null> => {
    if (!versionManagerRef.current || !document) {
      return null
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      const amendedCommit = await versionManagerRef.current.amendCommit(document, message)
      
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        hasUnsavedChanges: false 
      }))
      
      refreshState()
      return amendedCommit
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: error instanceof Error ? error.message : 'Amend failed'
      }))
      throw error
    }
  }, [document, refreshState])

  const createBranch = useCallback((
    name: string, 
    description?: string
  ): VersionBranch => {
    if (!versionManagerRef.current) {
      throw new Error('Version manager not initialized')
    }

    try {
      const branch = versionManagerRef.current.createBranch(name, description)
      refreshState()
      return branch
    } catch (error) {
      setState(prev => ({ 
        ...prev,
        error: error instanceof Error ? error.message : 'Branch creation failed'
      }))
      throw error
    }
  }, [refreshState])

  const switchBranch = useCallback(async (branchName: string): Promise<ResumeDocument | null> => {
    if (!versionManagerRef.current) {
      throw new Error('Version manager not initialized')
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      const branchDocument = versionManagerRef.current.switchBranch(branchName)
      
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        hasUnsavedChanges: false 
      }))
      
      refreshState()
      return branchDocument
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: error instanceof Error ? error.message : 'Branch switch failed'
      }))
      throw error
    }
  }, [refreshState])

  const mergeBranch = useCallback(async (
    sourceBranch: string,
    message?: string
  ): Promise<VersionCommit> => {
    if (!versionManagerRef.current) {
      throw new Error('Version manager not initialized')
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      const mergeCommit = await versionManagerRef.current.mergeBranch(
        sourceBranch,
        state.currentBranch,
        currentUserRef.current,
        message
      )

      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        hasUnsavedChanges: false 
      }))
      
      refreshState()
      return mergeCommit
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: error instanceof Error ? error.message : 'Merge failed'
      }))
      throw error
    }
  }, [state.currentBranch, refreshState])

  const rollbackToCommit = useCallback(async (commitId: string): Promise<ResumeDocument | null> => {
    if (!versionManagerRef.current) {
      throw new Error('Version manager not initialized')
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      const rolledBackDocument = versionManagerRef.current.rollbackToCommit(commitId)
      
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        hasUnsavedChanges: false 
      }))
      
      refreshState()
      return rolledBackDocument
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: error instanceof Error ? error.message : 'Rollback failed'
      }))
      throw error
    }
  }, [refreshState])

  const revertCommit = useCallback(async (commitId: string): Promise<VersionCommit> => {
    if (!versionManagerRef.current) {
      throw new Error('Version manager not initialized')
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      const revertCommit = await versionManagerRef.current.revertCommit(
        commitId,
        currentUserRef.current
      )

      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        hasUnsavedChanges: false 
      }))
      
      refreshState()
      return revertCommit
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: error instanceof Error ? error.message : 'Revert failed'
      }))
      throw error
    }
  }, [refreshState])

  const getDiff = useCallback((fromCommitId: string, toCommitId: string): VersionDiff => {
    if (!versionManagerRef.current) {
      throw new Error('Version manager not initialized')
    }

    return versionManagerRef.current.getDiff(fromCommitId, toCommitId)
  }, [])

  const getCommitHistory = useCallback((limit?: number): VersionCommit[] => {
    if (!versionManagerRef.current) {
      return []
    }

    return versionManagerRef.current.getCommitHistory(limit)
  }, [])

  const getBranchHistory = useCallback((branchName: string, limit?: number): VersionCommit[] => {
    if (!versionManagerRef.current) {
      return []
    }

    return versionManagerRef.current.getBranchHistory(branchName, limit)
  }, [])

  const actions: VersionControlActions = {
    commit,
    amendCommit,
    createBranch,
    switchBranch,
    mergeBranch,
    rollbackToCommit,
    revertCommit,
    getDiff,
    getCommitHistory,
    getBranchHistory,
    refreshHistory: refreshState
  }

  return [state, actions]
}

// Utility hook for commit formatting
export function useCommitFormatting() {
  const formatCommitMessage = useCallback((commit: VersionCommit): string => {
    const { message, timestamp, author } = commit
    const date = new Date(timestamp).toLocaleDateString()
    return `${message} - ${author.name} (${date})`
  }, [])

  const formatCommitStats = useCallback((commit: VersionCommit): string => {
    const { stats } = commit.metadata
    const parts = []
    
    if (stats.additions > 0) parts.push(`+${stats.additions}`)
    if (stats.deletions > 0) parts.push(`-${stats.deletions}`)
    if (stats.modifications > 0) parts.push(`~${stats.modifications}`)
    
    return parts.join(' ')
  }, [])

  const getCommitIcon = useCallback((commit: VersionCommit): string => {
    if (commit.metadata.tags.includes('merge')) return '🔀'
    if (commit.metadata.tags.includes('revert')) return '↩️'
    if (commit.message.startsWith('Auto-commit')) return '🤖'
    return '📝'
  }, [])

  return {
    formatCommitMessage,
    formatCommitStats,
    getCommitIcon
  }
}

// Hook for diff visualization
export function useDiffVisualization(diff: VersionDiff | null) {
  const formatChange = useCallback((change: VersionChange): string => {
    const pathStr = change.path.join('.')
    
    switch (change.type) {
      case 'add':
        return `+ Added ${pathStr}`
      case 'remove':
        return `- Removed ${pathStr}`
      case 'modify':
        return `~ Modified ${pathStr}`
      default:
        return change.description
    }
  }, [])

  const getChangeColor = useCallback((change: VersionChange): string => {
    switch (change.type) {
      case 'add':
        return 'text-green-600'
      case 'remove':
        return 'text-red-600'
      case 'modify':
        return 'text-blue-600'
      default:
        return 'text-gray-600'
    }
  }, [])

  const getDiffSummary = useCallback((diff: VersionDiff): string => {
    const { summary } = diff
    return `${summary.totalChanges} changes: +${summary.additionsCount} -${summary.deletionsCount} ~${summary.modificationsCount}`
  }, [])

  return {
    formatChange,
    getChangeColor,
    getDiffSummary
  }
}
