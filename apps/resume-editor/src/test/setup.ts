import '@testing-library/jest-dom'
import { configure } from '@testing-library/react'
import { server } from './mocks/server'

// Configure testing library
configure({
  testIdAttribute: 'data-testid',
})

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mocked-url')
global.URL.revokeObjectURL = jest.fn()

// Mock Blob
global.Blob = class Blob {
  constructor(parts: any[], options?: any) {
    this.size = 0
    this.type = options?.type || ''
  }
  size: number
  type: string
  slice() { return new Blob([]) }
  stream() { return new ReadableStream() }
  text() { return Promise.resolve('') }
  arrayBuffer() { return Promise.resolve(new ArrayBuffer(0)) }
}

// Mock File
global.File = class File extends Blob {
  constructor(parts: any[], name: string, options?: any) {
    super(parts, options)
    this.name = name
    this.lastModified = Date.now()
  }
  name: string
  lastModified: number
}

// Mock clipboard API
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: jest.fn().mockResolvedValue(undefined),
    readText: jest.fn().mockResolvedValue(''),
  },
})

// Mock getSelection
global.getSelection = jest.fn().mockReturnValue({
  removeAllRanges: jest.fn(),
  addRange: jest.fn(),
  toString: jest.fn().mockReturnValue(''),
})

// Mock Range
global.Range = class Range {
  setStart() {}
  setEnd() {}
  selectNodeContents() {}
  cloneContents() { return document.createDocumentFragment() }
  deleteContents() {}
  insertNode() {}
  collapse() {}
  getBoundingClientRect() {
    return {
      x: 0, y: 0, width: 0, height: 0,
      top: 0, right: 0, bottom: 0, left: 0,
      toJSON: () => ({})
    }
  }
}

// Mock DOMRect
global.DOMRect = class DOMRect {
  constructor(x = 0, y = 0, width = 0, height = 0) {
    this.x = x
    this.y = y
    this.width = width
    this.height = height
    this.top = y
    this.right = x + width
    this.bottom = y + height
    this.left = x
  }
  x: number
  y: number
  width: number
  height: number
  top: number
  right: number
  bottom: number
  left: number
  toJSON() {
    return {
      x: this.x, y: this.y, width: this.width, height: this.height,
      top: this.top, right: this.right, bottom: this.bottom, left: this.left
    }
  }
}

// Mock getBoundingClientRect for all elements
Element.prototype.getBoundingClientRect = jest.fn(() => new DOMRect())

// Mock scrollIntoView
Element.prototype.scrollIntoView = jest.fn()

// Mock focus and blur
HTMLElement.prototype.focus = jest.fn()
HTMLElement.prototype.blur = jest.fn()

// Mock contentEditable
Object.defineProperty(HTMLElement.prototype, 'contentEditable', {
  set: jest.fn(),
  get: jest.fn(() => 'false'),
})

// Mock execCommand
document.execCommand = jest.fn()

// Mock fonts API
Object.defineProperty(document, 'fonts', {
  value: {
    ready: Promise.resolve(),
    load: jest.fn().mockResolvedValue([]),
    check: jest.fn().mockReturnValue(true),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
  },
})

// Mock crypto for UUID generation
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: jest.fn(() => 'mock-uuid-1234'),
    getRandomValues: jest.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256)
      }
      return arr
    }),
  },
})

// Mock performance API
Object.defineProperty(global, 'performance', {
  value: {
    now: jest.fn(() => Date.now()),
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByType: jest.fn(() => []),
    getEntriesByName: jest.fn(() => []),
  },
})

// Mock requestAnimationFrame
global.requestAnimationFrame = jest.fn(cb => setTimeout(cb, 16))
global.cancelAnimationFrame = jest.fn(id => clearTimeout(id))

// Mock requestIdleCallback
global.requestIdleCallback = jest.fn(cb => setTimeout(cb, 1))
global.cancelIdleCallback = jest.fn(id => clearTimeout(id))

// Setup MSW server
beforeAll(() => server.listen())
afterEach(() => server.resetHandlers())
afterAll(() => server.close())

// Console error suppression for expected errors in tests
const originalError = console.error
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: ReactDOM.render is no longer supported') ||
       args[0].includes('Warning: An invalid form control') ||
       args[0].includes('Not implemented: HTMLCanvasElement.prototype.getContext'))
    ) {
      return
    }
    originalError.call(console, ...args)
  }
})

afterAll(() => {
  console.error = originalError
})

// Global test utilities
export const createMockFile = (name: string, content: string, type = 'text/plain') => {
  return new File([content], name, { type })
}

export const createMockEvent = (type: string, properties = {}) => {
  const event = new Event(type, { bubbles: true, cancelable: true })
  Object.assign(event, properties)
  return event
}

export const createMockKeyboardEvent = (key: string, properties = {}) => {
  return new KeyboardEvent('keydown', {
    key,
    bubbles: true,
    cancelable: true,
    ...properties,
  })
}

export const createMockMouseEvent = (type: string, properties = {}) => {
  return new MouseEvent(type, {
    bubbles: true,
    cancelable: true,
    ...properties,
  })
}

export const createMockDragEvent = (type: string, dataTransfer = {}) => {
  const event = new Event(type, { bubbles: true, cancelable: true }) as any
  event.dataTransfer = {
    dropEffect: 'none',
    effectAllowed: 'all',
    files: [],
    items: [],
    types: [],
    clearData: jest.fn(),
    getData: jest.fn(),
    setData: jest.fn(),
    setDragImage: jest.fn(),
    ...dataTransfer,
  }
  return event
}

// Mock Slate editor for tests
export const createMockEditor = () => ({
  children: [{ type: 'paragraph', children: [{ text: '' }] }],
  selection: null,
  operations: [],
  marks: null,
  isInline: jest.fn(() => false),
  isVoid: jest.fn(() => false),
  normalizeNode: jest.fn(),
  onChange: jest.fn(),
  apply: jest.fn(),
  addMark: jest.fn(),
  removeMark: jest.fn(),
  insertText: jest.fn(),
  insertNode: jest.fn(),
  removeNodes: jest.fn(),
  moveNodes: jest.fn(),
  setNodes: jest.fn(),
  splitNodes: jest.fn(),
  mergeNodes: jest.fn(),
  liftNodes: jest.fn(),
  wrapNodes: jest.fn(),
  unwrapNodes: jest.fn(),
  undo: jest.fn(),
  redo: jest.fn(),
})

// Mock resume document for tests
export const createMockResumeDocument = () => ({
  id: 'mock-document-id',
  title: 'Test Resume',
  content: [
    {
      type: 'paragraph',
      id: 'mock-paragraph-1',
      children: [{ text: 'Test content' }],
    },
  ],
  metadata: {
    language: 'en',
    direction: 'ltr' as const,
    pageSize: 'A4' as const,
    margins: { top: 1, right: 1, bottom: 1, left: 1 },
    fonts: {
      primary: 'Inter, sans-serif',
    },
    colors: {
      primary: '#3b82f6',
      text: '#1f2937',
      background: '#ffffff',
    },
    spacing: {
      lineHeight: 1.5,
      paragraphSpacing: 1,
      sectionSpacing: 2,
    },
  },
  template: {
    id: 'mock-template',
    name: 'Mock Template',
    category: 'modern' as const,
    layout: {
      columns: 1,
    },
    styling: {
      typography: {
        headingFont: 'Inter',
        bodyFont: 'Inter',
        headingSizes: {},
        bodySize: '16px',
        lineHeight: 1.5,
      },
      colors: {
        primary: '#3b82f6',
        secondary: '#64748b',
        accent: '#f59e0b',
        text: '#1f2937',
        textLight: '#6b7280',
        background: '#ffffff',
        border: '#e5e7eb',
      },
      spacing: {
        section: '2rem',
        element: '1rem',
        compact: '0.5rem',
      },
      borders: {
        width: '1px',
        style: 'solid' as const,
        radius: '0.375rem',
      },
    },
    sections: [],
  },
  version: 1,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  collaborators: [],
})

// Mock template config for tests
export const createMockTemplateConfig = () => ({
  id: 'mock-template',
  name: 'Mock Template',
  description: 'A mock template for testing',
  version: '1.0.0',
  author: {
    name: 'Test Author',
    email: '<EMAIL>',
  },
  category: 'modern' as const,
  tags: ['test', 'mock'],
  layout: {
    type: 'single-column' as const,
    columns: [{ width: '100%', content: ['header', 'summary'] }],
    spacing: { margin: '1rem', padding: '1rem', gap: '1rem' },
    breakpoints: { mobile: '768px', tablet: '1024px', desktop: '1200px' },
  },
  sections: [],
  theme: {
    name: 'Mock Theme',
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#f59e0b',
      text: '#1f2937',
      textLight: '#6b7280',
      background: '#ffffff',
      surface: '#f9fafb',
      border: '#e5e7eb',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
    },
    typography: {
      fontFamily: { primary: 'Inter, sans-serif' },
      fontSize: {
        xs: '0.75rem', sm: '0.875rem', base: '1rem', lg: '1.125rem',
        xl: '1.25rem', '2xl': '1.5rem', '3xl': '1.875rem', '4xl': '2.25rem',
      },
      fontWeight: { normal: '400', medium: '500', semibold: '600', bold: '700' },
      lineHeight: { tight: '1.25', normal: '1.5', relaxed: '1.75' },
    },
    spacing: {
      xs: '0.25rem', sm: '0.5rem', md: '1rem', lg: '1.5rem',
      xl: '2rem', '2xl': '3rem', '3xl': '4rem',
    },
    borderRadius: { none: '0', sm: '0.125rem', md: '0.375rem', lg: '0.5rem', full: '9999px' },
    shadows: {
      sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
      md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
      lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
      xl: '0 20px 25px -5px rgb(0 0 0 / 0.1)',
    },
  },
  settings: {
    pageSize: 'A4' as const,
    orientation: 'portrait' as const,
    margins: { top: '1in', right: '1in', bottom: '1in', left: '1in' },
    dpi: 300,
    quality: 'standard' as const,
  },
  ats: {
    friendly: true,
    keywords: [],
    structure: {
      useSemanticHTML: true,
      includeMetadata: true,
      optimizeForParsing: true,
    },
  },
  accessibility: {
    wcagLevel: 'AA' as const,
    highContrast: false,
    screenReaderOptimized: true,
    keyboardNavigation: true,
  },
  i18n: {
    defaultLanguage: 'en',
    supportedLanguages: ['en'],
    rtlSupport: false,
    dateFormat: 'MM/yyyy',
    numberFormat: 'en-US',
  },
  preview: {
    demoData: {},
  },
  metadata: {
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    downloads: 0,
    rating: 0,
    featured: false,
    premium: false,
  },
})
