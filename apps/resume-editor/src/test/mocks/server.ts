import { setupServer } from 'msw/node'
import { rest } from 'msw'

// Mock API responses
export const handlers = [
  // Collaboration WebSocket mock
  rest.get('/api/collaboration/connect', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        roomId: 'mock-room-id',
        userId: 'mock-user-id',
        token: 'mock-jwt-token',
      })
    )
  }),

  // Document API mocks
  rest.get('/api/documents/:id', (req, res, ctx) => {
    const { id } = req.params
    return res(
      ctx.status(200),
      ctx.json({
        id,
        title: 'Mock Resume',
        content: [
          {
            type: 'paragraph',
            id: 'mock-paragraph',
            children: [{ text: 'Mock content' }],
          },
        ],
        metadata: {
          language: 'en',
          direction: 'ltr',
          pageSize: 'A4',
          margins: { top: 1, right: 1, bottom: 1, left: 1 },
          fonts: { primary: 'Inter, sans-serif' },
          colors: { primary: '#3b82f6', text: '#1f2937', background: '#ffffff' },
          spacing: { lineHeight: 1.5, paragraphSpacing: 1, sectionSpacing: 2 },
        },
        template: { id: 'mock-template', name: 'Mock Template' },
        version: 1,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        collaborators: [],
      })
    )
  }),

  rest.put('/api/documents/:id', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({ success: true, version: 2 })
    )
  }),

  rest.post('/api/documents', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        id: 'new-document-id',
        title: 'New Resume',
        version: 1,
        createdAt: new Date().toISOString(),
      })
    )
  }),

  // Template API mocks
  rest.get('/api/templates', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        templates: [
          {
            id: 'modern-template',
            name: 'Modern Professional',
            category: 'modern',
            thumbnail: 'mock-thumbnail-url',
            featured: true,
            premium: false,
          },
          {
            id: 'classic-template',
            name: 'Classic Traditional',
            category: 'classic',
            thumbnail: 'mock-thumbnail-url',
            featured: false,
            premium: true,
          },
        ],
        total: 2,
      })
    )
  }),

  rest.get('/api/templates/:id', (req, res, ctx) => {
    const { id } = req.params
    return res(
      ctx.status(200),
      ctx.json({
        id,
        name: 'Mock Template',
        description: 'A mock template for testing',
        version: '1.0.0',
        author: { name: 'Test Author' },
        category: 'modern',
        layout: {
          type: 'single-column',
          columns: [{ width: '100%', content: ['header'] }],
          spacing: { margin: '1rem', padding: '1rem', gap: '1rem' },
          breakpoints: { mobile: '768px', tablet: '1024px', desktop: '1200px' },
        },
        sections: [],
        theme: {
          name: 'Mock Theme',
          colors: { primary: '#3b82f6', text: '#1f2937', background: '#ffffff' },
          typography: {
            fontFamily: { primary: 'Inter, sans-serif' },
            fontSize: { base: '1rem' },
            fontWeight: { normal: '400' },
            lineHeight: { normal: '1.5' },
          },
          spacing: { md: '1rem' },
          borderRadius: { md: '0.375rem' },
          shadows: { md: '0 4px 6px -1px rgb(0 0 0 / 0.1)' },
        },
        settings: {
          pageSize: 'A4',
          orientation: 'portrait',
          margins: { top: '1in', right: '1in', bottom: '1in', left: '1in' },
          dpi: 300,
          quality: 'standard',
        },
        ats: { friendly: true, keywords: [], structure: {} },
        accessibility: { wcagLevel: 'AA' },
        i18n: { defaultLanguage: 'en', supportedLanguages: ['en'] },
        preview: { demoData: {} },
        metadata: {
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
          downloads: 0,
          rating: 0,
          featured: false,
          premium: false,
        },
      })
    )
  }),

  // Export API mocks
  rest.post('/api/export/pdf', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        url: 'mock-pdf-url',
        metadata: {
          format: 'pdf',
          size: 1024000,
          pages: 1,
          generatedAt: new Date().toISOString(),
        },
      })
    )
  }),

  rest.post('/api/export/docx', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        url: 'mock-docx-url',
        metadata: {
          format: 'docx',
          size: 512000,
          pages: 1,
          generatedAt: new Date().toISOString(),
        },
      })
    )
  }),

  // AI/Magic Layout API mocks
  rest.post('/api/ai/layout-suggestions', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        suggestions: [
          {
            id: 'suggestion-1',
            type: 'reorder',
            title: 'Move Experience Section Up',
            description: 'Place work experience near the top for better visibility.',
            confidence: 0.8,
            changes: [
              {
                path: [2],
                type: 'move',
                before: {},
                after: {},
                reason: 'Better section ordering',
              },
            ],
          },
        ],
      })
    )
  }),

  // Linting API mocks
  rest.post('/api/lint', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        issues: [
          {
            id: 'issue-1',
            ruleId: 'consistent-tense',
            severity: 'warning',
            message: 'Use consistent verb tense in bullet points',
            path: [1, 0],
            suggestions: [
              {
                message: 'Change to past tense',
                fix: { type: 'replace', text: 'Developed' },
              },
            ],
          },
        ],
      })
    )
  }),

  // Version control API mocks
  rest.get('/api/documents/:id/versions', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        versions: [
          {
            id: 'version-1',
            version: 1,
            title: 'Initial version',
            author: { id: 'user-1', name: 'Test User' },
            createdAt: '2024-01-01T00:00:00Z',
            message: 'Initial resume creation',
            isPublished: false,
          },
        ],
      })
    )
  }),

  rest.post('/api/documents/:id/versions', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        id: 'new-version-id',
        version: 2,
        createdAt: new Date().toISOString(),
      })
    )
  }),

  // User preferences API mocks
  rest.get('/api/user/preferences', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        theme: 'light',
        language: 'en',
        autoSave: true,
        collaborationNotifications: true,
        keyboardShortcuts: true,
      })
    )
  }),

  rest.put('/api/user/preferences', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({ success: true })
    )
  }),

  // Error scenarios for testing
  rest.get('/api/error/500', (req, res, ctx) => {
    return res(
      ctx.status(500),
      ctx.json({ error: 'Internal server error' })
    )
  }),

  rest.get('/api/error/404', (req, res, ctx) => {
    return res(
      ctx.status(404),
      ctx.json({ error: 'Not found' })
    )
  }),

  rest.get('/api/error/network', (req, res, ctx) => {
    return res.networkError('Network error')
  }),
]

export const server = setupServer(...handlers)
