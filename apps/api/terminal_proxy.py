#!/usr/bin/env python3
"""
API proxy for terminal WebSocket connections.
Handles RBAC verification, JWT minting, and WebSocket redirects.
"""

import os
import time
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

import jwt
from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.responses import RedirectResponse
from pydantic import BaseModel
import httpx


class TerminalAccessRequest(BaseModel):
    session_id: str
    user_id: str
    user_role: str


class JWTClaims(BaseModel):
    user_id: str
    session_id: str
    ip: str
    role: str
    exp: int
    iat: int


app = FastAPI(
    title="CVLeap Terminal Proxy",
    description="API proxy for secure terminal access",
    version="1.0.0"
)

# Configuration
JWT_SECRET = os.getenv("JWT_SECRET", "your-secret-key")
JWT_EXPIRY_SECONDS = int(os.getenv("JWT_EXPIRY_SECONDS", "60"))  # 60 seconds
KUBERNETES_NAMESPACE = os.getenv("KUBERNETES_NAMESPACE", "cvleap")


def get_client_ip(request: Request) -> str:
    """Extract client IP from request headers."""
    # Check X-Forwarded-For header (from load balancer/proxy)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # Take the first IP in the chain
        return forwarded_for.split(",")[0].strip()
    
    # Check X-Real-IP header
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    # Fall back to client host
    return request.client.host if request.client else "unknown"


def verify_user_rbac(user_id: str, user_role: str, session_id: str) -> bool:
    """Verify user has permission to access the terminal session."""
    # In production, this would check against your RBAC system
    # For now, implement basic role-based access
    
    allowed_roles = ["admin", "developer", "Advanced ✚", "user"]
    if user_role not in allowed_roles:
        return False
    
    # Additional checks could include:
    # - User owns the session
    # - User has terminal access permission
    # - Session is in valid state
    
    return True


def get_pod_ip_for_session(session_id: str) -> Optional[str]:
    """Get the pod IP for a given session ID."""
    # In production, this would query Kubernetes API to find the pod
    # For demo purposes, return localhost
    return "127.0.0.1"


def mint_terminal_jwt(user_id: str, session_id: str, client_ip: str, role: str) -> str:
    """Generate a single-use JWT token for terminal access."""
    now = datetime.utcnow()
    exp = now + timedelta(seconds=JWT_EXPIRY_SECONDS)
    
    claims = {
        "user_id": user_id,
        "session_id": session_id,
        "ip": client_ip,
        "role": role,
        "exp": int(exp.timestamp()),
        "iat": int(now.timestamp()),
        "jti": f"{session_id}-{int(time.time())}"  # JWT ID for single-use tracking
    }
    
    return jwt.encode(claims, JWT_SECRET, algorithm="HS256")


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "terminal-proxy"}


@app.post("/api/sandbox/{session_id}/watch")
async def create_terminal_session(
    session_id: str,
    access_request: TerminalAccessRequest,
    request: Request
):
    """Create a terminal session and return WebSocket connection details."""
    # Verify the session ID matches the request
    if session_id != access_request.session_id:
        raise HTTPException(status_code=400, detail="Session ID mismatch")
    
    # Get client IP
    client_ip = get_client_ip(request)
    
    # Verify RBAC permissions
    if not verify_user_rbac(
        access_request.user_id,
        access_request.user_role,
        session_id
    ):
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions for terminal access"
        )
    
    # Get pod IP for the session
    pod_ip = get_pod_ip_for_session(session_id)
    if not pod_ip:
        raise HTTPException(
            status_code=404,
            detail="Session not found or not ready"
        )
    
    # Check if terminal is enabled for this session
    try:
        async with httpx.AsyncClient() as client:
            health_response = await client.get(
                f"http://{pod_ip}:7000/health",
                timeout=5.0
            )
            if health_response.status_code != 200:
                raise HTTPException(
                    status_code=503,
                    detail="Terminal service not available"
                )
    except httpx.RequestError:
        raise HTTPException(
            status_code=503,
            detail="Terminal service not reachable"
        )
    
    # Mint single-use JWT token
    token = mint_terminal_jwt(
        access_request.user_id,
        session_id,
        client_ip,
        access_request.user_role
    )
    
    # Return WebSocket connection details
    return {
        "websocket_url": f"ws://{pod_ip}:7000/ws",
        "token": token,
        "expires_in": JWT_EXPIRY_SECONDS,
        "protocol": "terminal",
        "session_id": session_id
    }


@app.get("/api/sandbox/{session_id}/watch")
async def get_terminal_redirect(
    session_id: str,
    user_id: str,
    user_role: str = "user",
    request: Request = None
):
    """GET endpoint that redirects to WebSocket URL (for browser compatibility)."""
    # Create access request
    access_request = TerminalAccessRequest(
        session_id=session_id,
        user_id=user_id,
        user_role=user_role
    )
    
    # Get WebSocket details
    ws_details = await create_terminal_session(session_id, access_request, request)
    
    # For GET requests, return a 302 redirect to the WebSocket URL with token
    ws_url = ws_details["websocket_url"]
    token = ws_details["token"]
    
    # Construct WebSocket URL with token in protocol header
    redirect_url = f"{ws_url}?token={token}"
    
    return RedirectResponse(
        url=redirect_url,
        status_code=302,
        headers={
            "X-Terminal-Token": token,
            "X-Terminal-Protocol": "terminal"
        }
    )


@app.get("/api/sandbox/{session_id}/status")
async def get_session_status(session_id: str):
    """Get the status of a sandbox session."""
    pod_ip = get_pod_ip_for_session(session_id)
    if not pod_ip:
        raise HTTPException(status_code=404, detail="Session not found")
    
    try:
        async with httpx.AsyncClient() as client:
            # Check main application status
            app_response = await client.get(
                f"http://{pod_ip}:8000/rpc/status/{session_id}",
                timeout=5.0
            )
            
            # Check terminal status if enabled
            terminal_status = None
            try:
                terminal_response = await client.get(
                    f"http://{pod_ip}:7000/sessions",
                    timeout=2.0
                )
                if terminal_response.status_code == 200:
                    terminal_data = terminal_response.json()
                    # Find our session in the terminal sessions
                    for session in terminal_data.get("sessions", []):
                        if session["id"] == session_id:
                            terminal_status = {
                                "available": True,
                                "clients": session["clients"],
                                "created": session["created"]
                            }
                            break
                    
                    if not terminal_status:
                        terminal_status = {"available": True, "clients": 0}
            except httpx.RequestError:
                terminal_status = {"available": False}
            
            return {
                "session_id": session_id,
                "application": app_response.json() if app_response.status_code == 200 else None,
                "terminal": terminal_status,
                "pod_ip": pod_ip
            }
    
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=503,
            detail=f"Unable to connect to session: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv("PORT", "9000"))
    uvicorn.run(app, host="0.0.0.0", port=port)
