package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"github.com/cvleap/terminald/internal/auth"
	"github.com/cvleap/terminald/internal/pty"
	"github.com/cvleap/terminald/internal/websocket"
	"github.com/cvleap/terminald/pkg/types"
)

func main() {
	// Load configuration from environment
	config := loadConfig()

	// Check if terminal is enabled
	if !config.EnableTerminal {
		log.Println("Terminal feature is disabled")
		// Run minimal health check server
		runHealthServer(config.Port)
		return
	}

	log.Printf("Starting terminald on port %d", config.Port)

	// Initialize services
	authService := auth.NewService(config.JWTSecret)
	terminalManager := pty.NewManager(config)
	wsHandler := websocket.NewHandler(terminalManager, authService)

	// Setup HTTP routes
	mux := http.NewServeMux()
	
	// Health check endpoint
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})

	// WebSocket endpoint
	mux.HandleFunc("/ws", wsHandler.HandleWebSocket)

	// Sessions management endpoint
	mux.HandleFunc("/sessions", func(w http.ResponseWriter, r *http.Request) {
		sessions := terminalManager.ListSessions()
		w.Header().Set("Content-Type", "application/json")
		fmt.Fprintf(w, `{"count": %d, "sessions": [`, len(sessions))
		for i, session := range sessions {
			if i > 0 {
				w.Write([]byte(","))
			}
			fmt.Fprintf(w, `{"id": "%s", "clients": %d, "created": "%s"}`,
				session.ID, len(session.Clients), session.CreatedAt.Format(time.RFC3339))
		}
		w.Write([]byte("]}")
	})

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", config.Port),
		Handler:      mux,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start server in goroutine
	go func() {
		log.Printf("terminald listening on :%d", config.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server failed to start: %v", err)
		}
	}()

	// Wait for interrupt signal
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	<-c

	log.Println("Shutting down terminald...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Close all terminal sessions
	for _, session := range terminalManager.ListSessions() {
		terminalManager.CloseSession(session.ID)
	}

	// Shutdown HTTP server
	if err := server.Shutdown(ctx); err != nil {
		log.Printf("Server shutdown error: %v", err)
	}

	log.Println("terminald stopped")
}

// loadConfig loads configuration from environment variables
func loadConfig() *types.Config {
	config := &types.Config{
		Port:           7000,
		RingBufferSize: 10485760, // 10MB
		EnableTerminal: false,
		TmuxSession:    "cvleap",
	}

	if port := os.Getenv("PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Port = p
		}
	}

	if secret := os.Getenv("JWT_SECRET"); secret != "" {
		config.JWTSecret = secret
	} else {
		log.Fatal("JWT_SECRET environment variable is required")
	}

	if size := os.Getenv("RING_BUFFER_SIZE"); size != "" {
		if s, err := strconv.Atoi(size); err == nil {
			config.RingBufferSize = s
		}
	}

	if enable := os.Getenv("ENABLE_TERMINAL"); enable != "" {
		config.EnableTerminal = enable == "true" || enable == "1"
	}

	if session := os.Getenv("TMUX_SESSION"); session != "" {
		config.TmuxSession = session
	}

	return config
}

// runHealthServer runs a minimal health check server when terminal is disabled
func runHealthServer(port int) {
	mux := http.NewServeMux()
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK - Terminal disabled"))
	})

	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", port),
		Handler: mux,
	}

	log.Printf("Health server listening on :%d (terminal disabled)", port)
	log.Fatal(server.ListenAndServe())
}
