#!/bin/bash
set -euo pipefail

# CVLeap Autonomous Job Submission - Development Environment Setup
# Sets up the complete development environment with all dependencies

echo "🛠️  Setting up CVLeap Job Submission Development Environment..."

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_status $BLUE "📁 Project root: $PROJECT_ROOT"
echo ""

# Check operating system
OS="$(uname -s)"
print_status $BLUE "🖥️  Operating System: $OS"

# Function to install dependencies based on OS
install_dependencies() {
    case "$OS" in
        "Darwin")
            print_status $BLUE "🍎 Setting up for macOS..."
            
            # Check if Homebrew is installed
            if ! command -v brew &> /dev/null; then
                print_status $YELLOW "📦 Installing Homebrew..."
                /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            fi
            
            # Install dependencies
            print_status $BLUE "📦 Installing dependencies via Homebrew..."
            brew update
            brew install node python@3.12 go tmux curl jq
            ;;
            
        "Linux")
            print_status $BLUE "🐧 Setting up for Linux..."
            
            # Detect Linux distribution
            if command -v apt-get &> /dev/null; then
                print_status $BLUE "📦 Installing dependencies via apt..."
                sudo apt-get update
                sudo apt-get install -y nodejs npm python3.12 python3.12-venv golang-go tmux curl jq
                
            elif command -v yum &> /dev/null; then
                print_status $BLUE "📦 Installing dependencies via yum..."
                sudo yum update -y
                sudo yum install -y nodejs npm python3.12 golang tmux curl jq
                
            elif command -v pacman &> /dev/null; then
                print_status $BLUE "📦 Installing dependencies via pacman..."
                sudo pacman -Syu --noconfirm nodejs npm python go tmux curl jq
                
            else
                print_status $RED "❌ Unsupported Linux distribution"
                print_status $YELLOW "Please install manually: Node.js 18+, Python 3.12+, Go 1.21+, tmux, curl, jq"
                exit 1
            fi
            ;;
            
        *)
            print_status $RED "❌ Unsupported operating system: $OS"
            print_status $YELLOW "Please install manually: Node.js 18+, Python 3.12+, Go 1.21+, tmux, curl, jq"
            exit 1
            ;;
    esac
}

# Check if dependencies are already installed
print_status $BLUE "🔍 Checking existing dependencies..."

missing_deps=()

if ! command -v node &> /dev/null; then
    missing_deps+=("Node.js")
fi

if ! command -v python3 &> /dev/null; then
    missing_deps+=("Python 3")
fi

if ! command -v go &> /dev/null; then
    missing_deps+=("Go")
fi

if ! command -v tmux &> /dev/null; then
    missing_deps+=("tmux")
fi

if ! command -v curl &> /dev/null; then
    missing_deps+=("curl")
fi

if ! command -v jq &> /dev/null; then
    missing_deps+=("jq")
fi

if [[ ${#missing_deps[@]} -gt 0 ]]; then
    print_status $YELLOW "⚠️  Missing dependencies: ${missing_deps[*]}"
    read -p "Install missing dependencies? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        install_dependencies
    else
        print_status $RED "❌ Cannot proceed without required dependencies"
        exit 1
    fi
else
    print_status $GREEN "✅ All dependencies found"
fi

# Display versions
print_status $BLUE "📊 Dependency Versions:"
echo "   Node.js: $(node --version 2>/dev/null || echo 'Not found')"
echo "   npm: $(npm --version 2>/dev/null || echo 'Not found')"
echo "   Python: $(python3 --version 2>/dev/null || echo 'Not found')"
echo "   Go: $(go version 2>/dev/null | cut -d' ' -f3 || echo 'Not found')"
echo "   tmux: $(tmux -V 2>/dev/null || echo 'Not found')"
echo ""

# Set up Python virtual environment
print_status $BLUE "🐍 Setting up Python virtual environment..."
cd "$PROJECT_ROOT"

if [[ ! -d "venv" ]]; then
    python3 -m venv venv
    print_status $GREEN "✅ Python virtual environment created"
else
    print_status $YELLOW "⚠️  Virtual environment already exists"
fi

# Activate virtual environment and install dependencies
source venv/bin/activate
print_status $BLUE "📦 Installing Python dependencies..."
pip install --upgrade pip
pip install -r agent-core/requirements.txt

# Install Playwright browsers
print_status $BLUE "🎭 Installing Playwright browsers..."
playwright install chromium

# Set up Go module for terminald
print_status $BLUE "🔧 Setting up Go module for terminald..."
cd terminald

if [[ ! -f "go.mod" ]]; then
    go mod init terminald
    go get github.com/creack/pty@v1.1.21
    go get github.com/golang-jwt/jwt/v5@v5.2.0
    go get github.com/gorilla/websocket@v1.5.1
    print_status $GREEN "✅ Go module initialized"
else
    print_status $YELLOW "⚠️  Go module already exists"
    go mod tidy
fi

# Build terminald
print_status $BLUE "🔨 Building terminald..."
go build -o terminald .
print_status $GREEN "✅ terminald built successfully"

cd "$PROJECT_ROOT"

# Create necessary directories
print_status $BLUE "📁 Creating project directories..."
mkdir -p logs tmp/artifacts tmp/screenshots

# Create environment file
print_status $BLUE "⚙️  Creating environment configuration..."
cat > .env << EOF
# CVLeap Job Submission Environment Configuration

# Core Settings
ENABLE_TERMINAL=1
TERMINAL_PORT=7000
SIDECAR_PORT=8080
FRONTEND_PORT=3000
API_PROXY_PORT=3001
LOG_LEVEL=INFO

# Security
JWT_SECRET=cvleap-dev-secret-$(date +%s)
NEXTAUTH_SECRET=cvleap-frontend-secret-$(date +%s)

# Development URLs
BACKEND_URL=http://localhost:8080
NEXTAUTH_URL=http://localhost:3001

# Optional: Add your API keys here
# OPENAI_API_KEY=your_openai_key_here
# BRIGHTDATA_USERNAME=your_brightdata_username
# BRIGHTDATA_PASSWORD=your_brightdata_password

# Sandbox Configuration
SANDBOX_ID=dev-sandbox-$(date +%s)
RING_BUFFER_SIZE=10485760
EOF

print_status $GREEN "✅ Environment file created: .env"

# Make scripts executable
print_status $BLUE "🔧 Making scripts executable..."
chmod +x scripts/*.sh

# Create a simple test script
print_status $BLUE "🧪 Creating test script..."
cat > scripts/test-system.sh << 'EOF'
#!/bin/bash
set -euo pipefail

echo "🧪 Testing CVLeap Job Submission System..."

# Test Go build
echo "Testing terminald build..."
cd terminald
go build -o terminald-test .
./terminald-test --help 2>/dev/null || echo "terminald built successfully"
rm terminald-test
cd ..

# Test Python environment
echo "Testing Python environment..."
source venv/bin/activate
python -c "
import playwright
import fastapi
import langchain
print('✅ All Python dependencies available')
"

# Test Playwright
echo "Testing Playwright..."
python -c "
from playwright.sync_api import sync_playwright
with sync_playwright() as p:
    browser = p.chromium.launch(headless=True)
    page = browser.new_page()
    page.goto('data:text/html,<h1>Test</h1>')
    assert 'Test' in page.content()
    browser.close()
    print('✅ Playwright working correctly')
"

echo "🎉 All tests passed!"
EOF

chmod +x scripts/test-system.sh

# Run tests
print_status $BLUE "🧪 Running system tests..."
if ./scripts/test-system.sh; then
    print_status $GREEN "✅ All tests passed!"
else
    print_status $RED "❌ Some tests failed"
fi

# Create development documentation
print_status $BLUE "📚 Creating development documentation..."
cat > DEV_SETUP.md << 'EOF'
# CVLeap Job Submission - Development Setup

## Quick Start

1. **Setup Development Environment**
   ```bash
   ./scripts/setup-dev.sh
   ```

2. **Start All Services**
   ```bash
   ./scripts/start-all.sh
   ```

3. **Open Browser**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8080

## Individual Services

### Backend Only
```bash
./scripts/start-backend.sh
```

### Frontend Only
```bash
./scripts/start-frontend.sh
```

## Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Required for full functionality
OPENAI_API_KEY=your_openai_key
BRIGHTDATA_USERNAME=your_brightdata_username
BRIGHTDATA_PASSWORD=your_brightdata_password
```

## Development Workflow

1. **Make Changes** to code
2. **Test Changes** with `./scripts/test-system.sh`
3. **Restart Services** if needed
4. **Check Logs** in `logs/` directory

## Troubleshooting

- **Port Conflicts**: Change ports in `.env` file
- **Permission Issues**: Run `chmod +x scripts/*.sh`
- **Python Issues**: Recreate venv with `rm -rf venv && python3 -m venv venv`
- **Go Issues**: Run `go mod tidy` in `terminald/` directory

## Project Structure

```
autonomous-job-submission/
├── scripts/           # Development scripts
├── terminald/         # Go WebSocket daemon
├── agent-core/        # Python LangChain agent
├── adapter-sdk/       # Portal adapter framework
├── sidecar/          # FastAPI endpoints
├── frontend/         # React frontend
├── api-proxy/        # Next.js API proxy
├── logs/             # Application logs
└── .env              # Environment configuration
```
EOF

print_status $GREEN "✅ Development documentation created: DEV_SETUP.md"

# Final summary
print_status $GREEN ""
print_status $GREEN "🎉 Development environment setup complete!"
print_status $GREEN ""

echo "📊 Setup Summary:"
echo "   ✅ Dependencies installed and verified"
echo "   ✅ Python virtual environment created"
echo "   ✅ Playwright browsers installed"
echo "   ✅ Go module initialized and built"
echo "   ✅ Project directories created"
echo "   ✅ Environment configuration created"
echo "   ✅ Scripts made executable"
echo "   ✅ System tests passed"
echo ""

print_status $BLUE "🚀 Next Steps:"
echo "   1. Review and update .env file with your API keys"
echo "   2. Run: ./scripts/start-all.sh"
echo "   3. Open: http://localhost:3000"
echo ""

print_status $BLUE "📚 Documentation:"
echo "   - Development Guide: DEV_SETUP.md"
echo "   - Main README: README.md"
echo "   - Environment Config: .env"
echo ""

print_status $YELLOW "💡 Pro Tips:"
echo "   - Use 'tmux attach-session -t cvleap' for direct terminal access"
echo "   - Check logs in logs/ directory for troubleshooting"
echo "   - Run ./scripts/test-system.sh to verify everything works"
echo ""

print_status $GREEN "Happy coding! 🚀"
