#!/bin/bash
set -euo pipefail

# CVLeap Autonomous Job Submission - Backend Startup Script
# Starts all backend services: terminald, FastAPI sidecar, and agent core

echo "🚀 Starting CVLeap Job Submission Backend..."

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
VENV_PATH="$PROJECT_ROOT/venv"
LOG_DIR="$PROJECT_ROOT/logs"

# Environment variables
export PYTHONPATH="$PROJECT_ROOT:$PROJECT_ROOT/agent-core:$PROJECT_ROOT/adapter-sdk:$PROJECT_ROOT/sidecar"
export ENABLE_TERMINAL="${ENABLE_TERMINAL:-1}"
export TERMINAL_PORT="${TERMINAL_PORT:-7000}"
export SIDECAR_PORT="${SIDECAR_PORT:-8080}"
export LOG_LEVEL="${LOG_LEVEL:-INFO}"
export JWT_SECRET="${JWT_SECRET:-cvleap-dev-secret-$(date +%s)}"
export SANDBOX_ID="${SANDBOX_ID:-dev-sandbox-$(date +%s)}"

# Create necessary directories
mkdir -p "$LOG_DIR" /tmp/artifacts /tmp/screenshots

echo "📁 Project root: $PROJECT_ROOT"
echo "🔧 Environment: ENABLE_TERMINAL=$ENABLE_TERMINAL, TERMINAL_PORT=$TERMINAL_PORT, SIDECAR_PORT=$SIDECAR_PORT"

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "❌ Port $port is already in use"
        return 1
    fi
    return 0
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" >/dev/null 2>&1; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - waiting..."
        sleep 2
        ((attempt++))
    done
    
    echo "❌ $service_name failed to start within timeout"
    return 1
}

# Function to cleanup on exit
cleanup() {
    echo "🧹 Cleaning up background processes..."
    
    # Kill background processes
    if [[ -n "${TERMINALD_PID:-}" ]]; then
        kill -TERM "$TERMINALD_PID" 2>/dev/null || true
    fi
    
    if [[ -n "${SIDECAR_PID:-}" ]]; then
        kill -TERM "$SIDECAR_PID" 2>/dev/null || true
    fi
    
    if [[ -n "${AGENT_PID:-}" ]]; then
        kill -TERM "$AGENT_PID" 2>/dev/null || true
    fi
    
    # Kill tmux session
    tmux kill-session -t cvleap 2>/dev/null || true
    
    echo "✅ Cleanup completed"
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT EXIT

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check if Go is installed (for terminald)
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.21+ to build terminald"
    exit 1
fi

# Check if Python virtual environment exists
if [[ ! -d "$VENV_PATH" ]]; then
    echo "📦 Creating Python virtual environment..."
    python3 -m venv "$VENV_PATH"
fi

# Activate virtual environment
source "$VENV_PATH/bin/activate"

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip install -q --upgrade pip
pip install -q -r "$PROJECT_ROOT/agent-core/requirements.txt"

# Install Playwright browsers
echo "🎭 Installing Playwright browsers..."
playwright install chromium

# Check ports availability
echo "🔍 Checking port availability..."
if ! check_port $TERMINAL_PORT; then
    echo "💡 You can change the terminal port with: export TERMINAL_PORT=<port>"
    exit 1
fi

if ! check_port $SIDECAR_PORT; then
    echo "💡 You can change the sidecar port with: export SIDECAR_PORT=<port>"
    exit 1
fi

# Build terminald
echo "🔨 Building terminald..."
cd "$PROJECT_ROOT/terminald"
if [[ ! -f "go.mod" ]]; then
    go mod init terminald
    go get github.com/creack/pty@v1.1.21
    go get github.com/golang-jwt/jwt/v5@v5.2.0
    go get github.com/gorilla/websocket@v1.5.1
fi
go build -o terminald .
cd "$PROJECT_ROOT"

# Initialize tmux session
echo "🖥️  Initializing tmux session..."
tmux kill-session -t cvleap 2>/dev/null || true
tmux new-session -d -s cvleap -x 120 -y 30
tmux send-keys -t cvleap "echo 'CVLeap Job Submission Sandbox Ready'" Enter
tmux send-keys -t cvleap "echo 'Sandbox ID: $SANDBOX_ID'" Enter
tmux send-keys -t cvleap "echo 'Use Ctrl+B then D to detach from this session'" Enter

# Start terminald (if terminal enabled)
if [[ "$ENABLE_TERMINAL" == "1" ]]; then
    echo "🖥️  Starting terminald on port $TERMINAL_PORT..."
    cd "$PROJECT_ROOT/terminald"
    ./terminald > "$LOG_DIR/terminald.log" 2>&1 &
    TERMINALD_PID=$!
    cd "$PROJECT_ROOT"
    
    # Wait for terminald to start
    sleep 3
    
    if kill -0 "$TERMINALD_PID" 2>/dev/null; then
        echo "✅ terminald started successfully (PID: $TERMINALD_PID)"
    else
        echo "❌ terminald failed to start"
        cat "$LOG_DIR/terminald.log"
        exit 1
    fi
else
    echo "ℹ️  Terminal disabled, skipping terminald"
fi

# Start FastAPI sidecar
echo "🔧 Starting FastAPI sidecar on port $SIDECAR_PORT..."
cd "$PROJECT_ROOT/sidecar"
python -m uvicorn server:app \
    --host 0.0.0.0 \
    --port "$SIDECAR_PORT" \
    --log-level "${LOG_LEVEL,,}" \
    --access-log \
    --reload > "$LOG_DIR/sidecar.log" 2>&1 &
SIDECAR_PID=$!
cd "$PROJECT_ROOT"

# Wait for sidecar to be ready
if wait_for_service "http://localhost:$SIDECAR_PORT/ready" "FastAPI sidecar"; then
    echo "✅ FastAPI sidecar started successfully (PID: $SIDECAR_PID)"
else
    echo "❌ FastAPI sidecar failed to start"
    cat "$LOG_DIR/sidecar.log"
    exit 1
fi

# Start job submission agent
echo "🤖 Starting job submission agent..."
cd "$PROJECT_ROOT/agent-core"
python -c "
import asyncio
import logging
from planner import JobSubmissionPlanner
from executor import JobSubmissionExecutor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def main():
    logger.info('Job submission agent started')
    logger.info('Waiting for job submission requests...')
    
    # Keep the agent running
    while True:
        await asyncio.sleep(10)
        logger.debug('Agent heartbeat')

if __name__ == '__main__':
    asyncio.run(main())
" > "$LOG_DIR/agent.log" 2>&1 &
AGENT_PID=$!
cd "$PROJECT_ROOT"

echo "✅ Job submission agent started (PID: $AGENT_PID)"

# Display status
echo ""
echo "🎉 Backend services started successfully!"
echo ""
echo "📊 Service Status:"
echo "  🖥️  tmux session: cvleap (tmux attach-session -t cvleap)"
if [[ "$ENABLE_TERMINAL" == "1" ]]; then
    echo "  🔌 terminald: http://localhost:$TERMINAL_PORT (WebSocket)"
fi
echo "  🔧 FastAPI sidecar: http://localhost:$SIDECAR_PORT"
echo "  🤖 Job agent: Running (PID: $AGENT_PID)"
echo ""
echo "📁 Logs:"
if [[ "$ENABLE_TERMINAL" == "1" ]]; then
    echo "  terminald: $LOG_DIR/terminald.log"
fi
echo "  sidecar: $LOG_DIR/sidecar.log"
echo "  agent: $LOG_DIR/agent.log"
echo ""
echo "🔗 API Endpoints:"
echo "  Health: curl http://localhost:$SIDECAR_PORT/ready"
echo "  Status: curl http://localhost:$SIDECAR_PORT/rpc/status"
echo "  Metrics: curl http://localhost:$SIDECAR_PORT/rpc/metrics"
echo ""
echo "🛑 To stop all services: Ctrl+C or kill this script"
echo ""

# Test the services
echo "🧪 Running quick health checks..."

# Test sidecar health
if curl -f -s "http://localhost:$SIDECAR_PORT/ready" | jq -r '.status' | grep -q "healthy"; then
    echo "✅ Sidecar health check passed"
else
    echo "⚠️  Sidecar health check failed"
fi

# Test terminald (if enabled)
if [[ "$ENABLE_TERMINAL" == "1" ]]; then
    if curl -f -s "http://localhost:$TERMINAL_PORT/health" >/dev/null 2>&1; then
        echo "✅ terminald health check passed"
    else
        echo "⚠️  terminald health check failed"
    fi
fi

echo ""
echo "🚀 Backend is ready! You can now start the frontend."
echo "💡 Run './scripts/start-frontend.sh' in another terminal"
echo ""

# Keep the script running and monitor processes
echo "📊 Monitoring services... (Ctrl+C to stop)"
while true; do
    # Check if processes are still running
    if [[ -n "${SIDECAR_PID:-}" ]] && ! kill -0 "$SIDECAR_PID" 2>/dev/null; then
        echo "❌ FastAPI sidecar died unexpectedly"
        exit 1
    fi
    
    if [[ "$ENABLE_TERMINAL" == "1" ]] && [[ -n "${TERMINALD_PID:-}" ]] && ! kill -0 "$TERMINALD_PID" 2>/dev/null; then
        echo "❌ terminald died unexpectedly"
        exit 1
    fi
    
    if [[ -n "${AGENT_PID:-}" ]] && ! kill -0 "$AGENT_PID" 2>/dev/null; then
        echo "⚠️  Job agent died, restarting..."
        # Restart agent
        cd "$PROJECT_ROOT/agent-core"
        python -c "
import asyncio
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
async def main():
    logger.info('Job submission agent restarted')
    while True:
        await asyncio.sleep(10)
        logger.debug('Agent heartbeat')
if __name__ == '__main__':
    asyncio.run(main())
" > "$LOG_DIR/agent.log" 2>&1 &
        AGENT_PID=$!
        cd "$PROJECT_ROOT"
        echo "✅ Job agent restarted (PID: $AGENT_PID)"
    fi
    
    sleep 10
done
