#!/bin/bash
set -euo pipefail

# CVLeap Autonomous Job Submission - Complete System Startup
# Starts both backend and frontend services in the correct order

echo "🚀 Starting CVLeap Autonomous Job Submission System..."

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to cleanup on exit
cleanup() {
    print_status $YELLOW "🧹 Shutting down all services..."
    
    # Kill background processes
    if [[ -n "${BACKEND_PID:-}" ]]; then
        kill -TERM "$BACKEND_PID" 2>/dev/null || true
        wait "$BACKEND_PID" 2>/dev/null || true
    fi
    
    if [[ -n "${FRONTEND_PID:-}" ]]; then
        kill -TERM "$FRONTEND_PID" 2>/dev/null || true
        wait "$FRONTEND_PID" 2>/dev/null || true
    fi
    
    print_status $GREEN "✅ All services stopped"
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT EXIT

# Check prerequisites
print_status $BLUE "🔍 Checking system prerequisites..."

# Check if required tools are installed
missing_tools=()

if ! command -v node &> /dev/null; then
    missing_tools+=("Node.js 18+")
fi

if ! command -v npm &> /dev/null; then
    missing_tools+=("npm")
fi

if ! command -v python3 &> /dev/null; then
    missing_tools+=("Python 3.12+")
fi

if ! command -v go &> /dev/null; then
    missing_tools+=("Go 1.21+")
fi

if ! command -v curl &> /dev/null; then
    missing_tools+=("curl")
fi

if [[ ${#missing_tools[@]} -gt 0 ]]; then
    print_status $RED "❌ Missing required tools:"
    for tool in "${missing_tools[@]}"; do
        echo "   - $tool"
    done
    echo ""
    print_status $YELLOW "Please install the missing tools and try again."
    exit 1
fi

print_status $GREEN "✅ All prerequisites found"

# Display system information
print_status $BLUE "📊 System Information:"
echo "   Node.js: $(node --version)"
echo "   npm: $(npm --version)"
echo "   Python: $(python3 --version)"
echo "   Go: $(go version | cut -d' ' -f3)"
echo "   OS: $(uname -s) $(uname -r)"
echo ""

# Set environment variables
export ENABLE_TERMINAL=1
export TERMINAL_PORT=7000
export SIDECAR_PORT=8080
export FRONTEND_PORT=3000
export API_PROXY_PORT=3001
export LOG_LEVEL=INFO

print_status $BLUE "🔧 Configuration:"
echo "   Terminal Port: $TERMINAL_PORT"
echo "   Sidecar Port: $SIDECAR_PORT"
echo "   Frontend Port: $FRONTEND_PORT"
echo "   API Proxy Port: $API_PROXY_PORT"
echo "   Log Level: $LOG_LEVEL"
echo ""

# Make scripts executable
chmod +x "$SCRIPT_DIR/start-backend.sh"
chmod +x "$SCRIPT_DIR/start-frontend.sh"

# Start backend services
print_status $BLUE "🔧 Starting backend services..."
"$SCRIPT_DIR/start-backend.sh" &
BACKEND_PID=$!

# Wait for backend to be ready
print_status $YELLOW "⏳ Waiting for backend to be ready..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -f -s "http://localhost:$SIDECAR_PORT/ready" >/dev/null 2>&1; then
        print_status $GREEN "✅ Backend is ready!"
        break
    fi
    
    if ! kill -0 "$BACKEND_PID" 2>/dev/null; then
        print_status $RED "❌ Backend process died during startup"
        exit 1
    fi
    
    echo "   Attempt $attempt/$max_attempts - waiting for backend..."
    sleep 3
    ((attempt++))
done

if [ $attempt -gt $max_attempts ]; then
    print_status $RED "❌ Backend failed to start within timeout"
    exit 1
fi

# Start frontend services
print_status $BLUE "🎨 Starting frontend services..."
"$SCRIPT_DIR/start-frontend.sh" &
FRONTEND_PID=$!

# Wait for frontend to be ready
print_status $YELLOW "⏳ Waiting for frontend to be ready..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -f -s "http://localhost:$FRONTEND_PORT" >/dev/null 2>&1; then
        print_status $GREEN "✅ Frontend is ready!"
        break
    fi
    
    if ! kill -0 "$FRONTEND_PID" 2>/dev/null; then
        print_status $RED "❌ Frontend process died during startup"
        exit 1
    fi
    
    echo "   Attempt $attempt/$max_attempts - waiting for frontend..."
    sleep 3
    ((attempt++))
done

if [ $attempt -gt $max_attempts ]; then
    print_status $RED "❌ Frontend failed to start within timeout"
    exit 1
fi

# System is ready
print_status $GREEN ""
print_status $GREEN "🎉 CVLeap Job Submission System is ready!"
print_status $GREEN ""

echo "📊 Service Status:"
echo "   🔧 Backend Services:"
echo "      - terminald: http://localhost:$TERMINAL_PORT (WebSocket)"
echo "      - FastAPI Sidecar: http://localhost:$SIDECAR_PORT"
echo "      - Job Agent: Running"
echo "      - tmux Session: cvleap"
echo ""
echo "   🎨 Frontend Services:"
echo "      - React App: http://localhost:$FRONTEND_PORT"
echo "      - API Proxy: http://localhost:$API_PROXY_PORT"
echo ""

print_status $BLUE "🔗 Quick Links:"
echo "   🌐 Main Application: http://localhost:$FRONTEND_PORT"
echo "   📊 Health Check: http://localhost:$SIDECAR_PORT/ready"
echo "   📈 Metrics: http://localhost:$SIDECAR_PORT/rpc/metrics"
echo "   🖥️  Terminal Session: tmux attach-session -t cvleap"
echo ""

print_status $BLUE "🧪 Quick Tests:"
echo "   # Test backend health"
echo "   curl http://localhost:$SIDECAR_PORT/ready"
echo ""
echo "   # Test frontend"
echo "   curl http://localhost:$FRONTEND_PORT"
echo ""
echo "   # View logs"
echo "   tail -f $PROJECT_ROOT/logs/*.log"
echo ""

print_status $YELLOW "💡 Usage Tips:"
echo "   - Open http://localhost:$FRONTEND_PORT in your browser"
echo "   - Enter a sandbox ID and click 'Show Terminal' to see live output"
echo "   - Use tmux attach-session -t cvleap for direct terminal access"
echo "   - Check logs in $PROJECT_ROOT/logs/ for troubleshooting"
echo ""

print_status $YELLOW "🛑 To stop all services: Press Ctrl+C"
echo ""

# Open browser automatically
if command -v open &> /dev/null; then
    print_status $BLUE "🌐 Opening browser..."
    open "http://localhost:$FRONTEND_PORT"
elif command -v xdg-open &> /dev/null; then
    print_status $BLUE "🌐 Opening browser..."
    xdg-open "http://localhost:$FRONTEND_PORT"
fi

# Monitor services
print_status $BLUE "📊 Monitoring all services... (Ctrl+C to stop)"
echo ""

while true; do
    # Check backend
    if ! kill -0 "$BACKEND_PID" 2>/dev/null; then
        print_status $RED "❌ Backend services died unexpectedly"
        exit 1
    fi
    
    # Check frontend
    if ! kill -0 "$FRONTEND_PID" 2>/dev/null; then
        print_status $RED "❌ Frontend services died unexpectedly"
        exit 1
    fi
    
    # Health check every 30 seconds
    if curl -f -s "http://localhost:$SIDECAR_PORT/ready" >/dev/null 2>&1; then
        echo "$(date '+%H:%M:%S') - ✅ All services healthy"
    else
        print_status $YELLOW "$(date '+%H:%M:%S') - ⚠️  Backend health check failed"
    fi
    
    sleep 30
done
