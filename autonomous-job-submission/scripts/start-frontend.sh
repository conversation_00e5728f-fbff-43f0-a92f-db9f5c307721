#!/bin/bash
set -euo pipefail

# CVLeap Autonomous Job Submission - Frontend Startup Script
# Starts the React frontend with terminal integration

echo "🎨 Starting CVLeap Job Submission Frontend..."

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
API_PROXY_DIR="$PROJECT_ROOT/api-proxy"
LOG_DIR="$PROJECT_ROOT/logs"

# Environment variables
export NODE_ENV="${NODE_ENV:-development}"
export FRONTEND_PORT="${FRONTEND_PORT:-3000}"
export API_PROXY_PORT="${API_PROXY_PORT:-3001}"
export BACKEND_URL="${BACKEND_URL:-http://localhost:8080}"
export NEXTAUTH_SECRET="${NEXTAUTH_SECRET:-cvleap-frontend-secret-$(date +%s)}"
export NEXTAUTH_URL="${NEXTAUTH_URL:-http://localhost:$API_PROXY_PORT}"

# Create necessary directories
mkdir -p "$LOG_DIR" "$FRONTEND_DIR" "$API_PROXY_DIR"

echo "📁 Frontend directory: $FRONTEND_DIR"
echo "🔧 Environment: NODE_ENV=$NODE_ENV, FRONTEND_PORT=$FRONTEND_PORT, API_PROXY_PORT=$API_PROXY_PORT"

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "❌ Port $port is already in use"
        return 1
    fi
    return 0
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" >/dev/null 2>&1; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - waiting..."
        sleep 2
        ((attempt++))
    done
    
    echo "❌ $service_name failed to start within timeout"
    return 1
}

# Function to cleanup on exit
cleanup() {
    echo "🧹 Cleaning up frontend processes..."
    
    # Kill background processes
    if [[ -n "${FRONTEND_PID:-}" ]]; then
        kill -TERM "$FRONTEND_PID" 2>/dev/null || true
    fi
    
    if [[ -n "${API_PROXY_PID:-}" ]]; then
        kill -TERM "$API_PROXY_PID" 2>/dev/null || true
    fi
    
    echo "✅ Frontend cleanup completed"
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT EXIT

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and npm"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm"
    exit 1
fi

echo "✅ Node.js $(node --version) and npm $(npm --version) found"

# Check if backend is running
echo "🔍 Checking backend connectivity..."
if ! curl -f -s "$BACKEND_URL/ready" >/dev/null 2>&1; then
    echo "❌ Backend is not running at $BACKEND_URL"
    echo "💡 Please start the backend first with: ./scripts/start-backend.sh"
    exit 1
fi
echo "✅ Backend is running at $BACKEND_URL"

# Check ports availability
echo "🔍 Checking port availability..."
if ! check_port $FRONTEND_PORT; then
    echo "💡 You can change the frontend port with: export FRONTEND_PORT=<port>"
    exit 1
fi

if ! check_port $API_PROXY_PORT; then
    echo "💡 You can change the API proxy port with: export API_PROXY_PORT=<port>"
    exit 1
fi

# Initialize frontend project
echo "📦 Setting up React frontend..."

if [[ ! -d "$FRONTEND_DIR" ]]; then
    mkdir -p "$FRONTEND_DIR"
fi

cd "$FRONTEND_DIR"

# Create package.json if it doesn't exist
if [[ ! -f "package.json" ]]; then
    echo "📝 Creating React frontend project..."
    
    cat > package.json << 'EOF'
{
  "name": "cvleap-job-submission-frontend",
  "version": "1.0.0",
  "description": "CVLeap Job Submission Frontend with Terminal Integration",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "next-auth": "^4.24.0",
    "xterm": "^5.3.0",
    "xterm-addon-fit": "^0.8.0",
    "xterm-addon-web-links": "^0.9.0",
    "xterm-addon-search": "^0.13.0",
    "@types/node": "^20.0.0",
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "typescript": "^5.2.0",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.0",
    "postcss": "^8.4.0",
    "lucide-react": "^0.294.0",
    "clsx": "^2.0.0",
    "zod": "^3.22.0"
  },
  "devDependencies": {
    "eslint": "^8.0.0",
    "eslint-config-next": "^14.0.0",
    "@tailwindcss/forms": "^0.5.0",
    "@tailwindcss/typography": "^0.5.0"
  }
}
EOF
fi

# Install dependencies
echo "📦 Installing frontend dependencies..."
npm install --silent

# Create basic Next.js configuration
if [[ ! -f "next.config.js" ]]; then
    cat > next.config.js << 'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  experimental: {
    appDir: false
  },
  env: {
    BACKEND_URL: process.env.BACKEND_URL || 'http://localhost:8080',
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL
  }
}

module.exports = nextConfig
EOF
fi

# Create Tailwind configuration
if [[ ! -f "tailwind.config.js" ]]; then
    cat > tailwind.config.js << 'EOF'
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        'cvleap-blue': '#0066cc',
        'cvleap-dark': '#1a1a1a',
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
EOF
fi

# Create PostCSS configuration
if [[ ! -f "postcss.config.js" ]]; then
    cat > postcss.config.js << 'EOF'
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
EOF
fi

# Create TypeScript configuration
if [[ ! -f "tsconfig.json" ]]; then
    cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
EOF
fi

# Create basic pages structure
mkdir -p pages/api pages/sandbox components hooks styles

# Create main page
if [[ ! -f "pages/index.tsx" ]]; then
    cat > pages/index.tsx << 'EOF'
import { useState } from 'react'
import { useSandboxTerminal } from '../hooks/useSandboxTerminal'
import TerminalComponent from '../components/TerminalComponent'

export default function Home() {
  const [sandboxId, setSandboxId] = useState('dev-sandbox-123')
  const [showTerminal, setShowTerminal] = useState(false)

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-4xl font-bold mb-8 text-center text-cvleap-blue">
          CVLeap Job Submission System
        </h1>
        
        <div className="max-w-4xl mx-auto">
          <div className="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 className="text-2xl font-semibold mb-4">Sandbox Control</h2>
            
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">
                Sandbox ID:
              </label>
              <input
                type="text"
                value={sandboxId}
                onChange={(e) => setSandboxId(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                placeholder="Enter sandbox ID"
              />
            </div>
            
            <button
              onClick={() => setShowTerminal(!showTerminal)}
              className="bg-cvleap-blue hover:bg-blue-700 px-4 py-2 rounded-md font-medium transition-colors"
            >
              {showTerminal ? 'Hide Terminal' : 'Show Terminal'}
            </button>
          </div>
          
          {showTerminal && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Live Terminal</h3>
              <TerminalComponent sandboxId={sandboxId} />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
EOF
fi

# Create terminal component
if [[ ! -f "components/TerminalComponent.tsx" ]]; then
    cat > components/TerminalComponent.tsx << 'EOF'
import { useEffect, useRef } from 'react'
import { useSandboxTerminal } from '../hooks/useSandboxTerminal'

interface TerminalComponentProps {
  sandboxId: string
}

export default function TerminalComponent({ sandboxId }: TerminalComponentProps) {
  const {
    containerRef,
    isConnected,
    isConnecting,
    isLoading,
    error,
    connect,
    disconnect,
    isSupported
  } = useSandboxTerminal({
    sandboxId,
    autoConnect: true,
    config: {
      theme: {
        background: '#1a1a1a',
        foreground: '#ffffff',
        cursor: '#00ff00'
      }
    }
  })

  if (!isSupported) {
    return (
      <div className="bg-red-900 border border-red-700 rounded-md p-4">
        <p className="text-red-200">
          Terminal not supported in this browser. Please use a modern browser with WebSocket support.
        </p>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="bg-gray-700 rounded-md p-4 text-center">
        <p>Loading terminal...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-900 border border-red-700 rounded-md p-4">
        <p className="text-red-200 mb-2">Terminal Error:</p>
        <p className="text-red-300 text-sm font-mono">{error}</p>
        <button
          onClick={connect}
          className="mt-2 bg-red-700 hover:bg-red-600 px-3 py-1 rounded text-sm"
        >
          Retry Connection
        </button>
      </div>
    )
  }

  return (
    <div className="border border-gray-600 rounded-md overflow-hidden">
      <div className="bg-gray-700 px-4 py-2 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${
            isConnected ? 'bg-green-500' : isConnecting ? 'bg-yellow-500' : 'bg-red-500'
          }`} />
          <span className="text-sm">
            {isConnected ? 'Connected' : isConnecting ? 'Connecting...' : 'Disconnected'}
          </span>
        </div>
        
        <div className="flex space-x-2">
          {!isConnected && !isConnecting && (
            <button
              onClick={connect}
              className="bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-sm"
            >
              Connect
            </button>
          )}
          {isConnected && (
            <button
              onClick={disconnect}
              className="bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm"
            >
              Disconnect
            </button>
          )}
        </div>
      </div>
      
      <div
        ref={containerRef}
        className="bg-black"
        style={{ height: '400px', width: '100%' }}
      />
      
      <div className="bg-gray-700 px-4 py-2 text-xs text-gray-300">
        <p>Hotkeys: ⌘K (Capture), ⌘C (Interrupt), ⌘D (EOF)</p>
      </div>
    </div>
  )
}
EOF
fi

# Copy the terminal hook from the project
cp "$PROJECT_ROOT/frontend/hooks/useSandboxTerminal.ts" hooks/ 2>/dev/null || {
    echo "⚠️  Terminal hook not found, creating basic version..."
    mkdir -p hooks
    cat > hooks/useSandboxTerminal.ts << 'EOF'
// Basic terminal hook - replace with full implementation
import { useState, useRef } from 'react'

export function useSandboxTerminal(options: any) {
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const connect = async () => {
    setIsConnecting(true)
    setError(null)
    
    try {
      // Simulate connection
      await new Promise(resolve => setTimeout(resolve, 1000))
      setIsConnected(true)
      setIsConnecting(false)
    } catch (err) {
      setError('Connection failed')
      setIsConnecting(false)
    }
  }

  const disconnect = () => {
    setIsConnected(false)
  }

  return {
    containerRef,
    isConnected,
    isConnecting,
    isLoading,
    error,
    connect,
    disconnect,
    isSupported: true,
    sendInput: () => {},
    captureContent: () => '',
    clearTerminal: () => {},
    resize: () => {},
    search: () => {}
  }
}
EOF
}

# Create global styles
if [[ ! -f "styles/globals.css" ]]; then
    cat > styles/globals.css << 'EOF'
@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

/* Terminal styles */
.xterm {
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.2;
}

.xterm-viewport {
  background-color: transparent;
}
EOF
fi

# Initialize API proxy project
echo "📦 Setting up API proxy..."

cd "$API_PROXY_DIR"

# Create package.json for API proxy
if [[ ! -f "package.json" ]]; then
    cat > package.json << 'EOF'
{
  "name": "cvleap-api-proxy",
  "version": "1.0.0",
  "description": "CVLeap API Proxy for WebSocket Authentication",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start"
  },
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "next-auth": "^4.24.0",
    "jsonwebtoken": "^9.0.0",
    "zod": "^3.22.0",
    "@types/jsonwebtoken": "^9.0.0",
    "@types/node": "^20.0.0",
    "typescript": "^5.2.0"
  }
}
EOF
fi

# Install API proxy dependencies
echo "📦 Installing API proxy dependencies..."
npm install --silent

# Copy the API route from the project
mkdir -p pages/api/sandbox
cp "$PROJECT_ROOT/api-proxy/pages/api/sandbox/[id]/watch.ts" pages/api/sandbox/ 2>/dev/null || {
    echo "⚠️  API route not found, creating basic version..."
    mkdir -p pages/api/sandbox/\[id\]
    cat > pages/api/sandbox/\[id\]/watch.ts << 'EOF'
// Basic API route - replace with full implementation
import { NextApiRequest, NextApiResponse } from 'next'

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query
  
  // Mock response for development
  res.status(200).json({
    success: true,
    websocket: {
      url: `ws://localhost:7000/ws`,
      protocol: 'mock-token',
      timeout: 60000
    },
    session: {
      sandbox_id: id,
      user_id: 'dev-user',
      role: 'Admin'
    }
  })
}
EOF
}

cd "$PROJECT_ROOT"

# Start API proxy
echo "🔧 Starting API proxy on port $API_PROXY_PORT..."
cd "$API_PROXY_DIR"
PORT=$API_PROXY_PORT npm run dev > "$LOG_DIR/api-proxy.log" 2>&1 &
API_PROXY_PID=$!
cd "$PROJECT_ROOT"

# Wait for API proxy to be ready
if wait_for_service "http://localhost:$API_PROXY_PORT" "API proxy"; then
    echo "✅ API proxy started successfully (PID: $API_PROXY_PID)"
else
    echo "❌ API proxy failed to start"
    cat "$LOG_DIR/api-proxy.log"
    exit 1
fi

# Start frontend
echo "🎨 Starting React frontend on port $FRONTEND_PORT..."
cd "$FRONTEND_DIR"
PORT=$FRONTEND_PORT npm run dev > "$LOG_DIR/frontend.log" 2>&1 &
FRONTEND_PID=$!
cd "$PROJECT_ROOT"

# Wait for frontend to be ready
if wait_for_service "http://localhost:$FRONTEND_PORT" "React frontend"; then
    echo "✅ React frontend started successfully (PID: $FRONTEND_PID)"
else
    echo "❌ React frontend failed to start"
    cat "$LOG_DIR/frontend.log"
    exit 1
fi

# Display status
echo ""
echo "🎉 Frontend services started successfully!"
echo ""
echo "📊 Service Status:"
echo "  🎨 React Frontend: http://localhost:$FRONTEND_PORT"
echo "  🔧 API Proxy: http://localhost:$API_PROXY_PORT"
echo "  🔗 Backend: $BACKEND_URL"
echo ""
echo "📁 Logs:"
echo "  frontend: $LOG_DIR/frontend.log"
echo "  api-proxy: $LOG_DIR/api-proxy.log"
echo ""
echo "🌐 Open in browser: http://localhost:$FRONTEND_PORT"
echo ""
echo "🛑 To stop all services: Ctrl+C or kill this script"
echo ""

# Open browser automatically (optional)
if command -v open &> /dev/null; then
    echo "🌐 Opening browser..."
    open "http://localhost:$FRONTEND_PORT"
elif command -v xdg-open &> /dev/null; then
    echo "🌐 Opening browser..."
    xdg-open "http://localhost:$FRONTEND_PORT"
fi

# Keep the script running and monitor processes
echo "📊 Monitoring frontend services... (Ctrl+C to stop)"
while true; do
    # Check if processes are still running
    if [[ -n "${FRONTEND_PID:-}" ]] && ! kill -0 "$FRONTEND_PID" 2>/dev/null; then
        echo "❌ React frontend died unexpectedly"
        exit 1
    fi
    
    if [[ -n "${API_PROXY_PID:-}" ]] && ! kill -0 "$API_PROXY_PID" 2>/dev/null; then
        echo "❌ API proxy died unexpectedly"
        exit 1
    fi
    
    sleep 10
done
