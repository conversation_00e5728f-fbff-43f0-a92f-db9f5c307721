{"defaultAction": "SCMP_ACT_ERRNO", "architectures": ["SCMP_ARCH_X86_64", "SCMP_ARCH_X86", "SCMP_ARCH_X32"], "syscalls": [{"names": ["accept", "accept4", "access", "adjtimex", "alarm", "bind", "brk", "capget", "capset", "chdir", "chmod", "chown", "chown32", "clock_getres", "clock_gettime", "clock_nanosleep", "clone", "close", "connect", "copy_file_range", "creat", "dup", "dup2", "dup3", "epoll_create", "epoll_create1", "epoll_ctl", "epoll_pwait", "epoll_wait", "eventfd", "eventfd2", "execve", "exit", "exit_group", "faccessat", "fadvise64", "fadvise64_64", "fallocate", "fanotify_mark", "fchdir", "fchmod", "fchmodat", "fchown", "fchown32", "fchownat", "fcntl", "fcntl64", "fdatasync", "fgetxattr", "fl<PERSON><PERSON><PERSON><PERSON>", "flock", "fork", "fremovexattr", "fsetxattr", "fstat", "fstat64", "fstatat64", "fstatfs", "fstatfs64", "fsync", "ftrun<PERSON>", "ftruncate64", "futex", "getcwd", "getdents", "getdents64", "<PERSON><PERSON><PERSON>", "getegid32", "<PERSON><PERSON><PERSON>", "geteuid32", "getgid", "getgid32", "getgroups", "getgroups32", "getitimer", "getpeername", "getpgid", "getpgrp", "getpid", "<PERSON><PERSON><PERSON>", "getpriority", "getrandom", "get<PERSON><PERSON>d", "getresgid32", "getresuid", "getresuid32", "getrlimit", "get_robust_list", "getrusage", "getsid", "getsockname", "getsockopt", "get_thread_area", "gettid", "gettimeofday", "getuid", "getuid32", "getxa<PERSON><PERSON>", "inotify_add_watch", "inotify_init", "inotify_init1", "inotify_rm_watch", "io_cancel", "ioctl", "io_destroy", "io_getevents", "ioprio_get", "ioprio_set", "io_setup", "io_submit", "ipc", "kill", "lchown", "lchown32", "lgetxattr", "link", "linkat", "listen", "listxattr", "llistxattr", "lremovexattr", "lseek", "lsetxattr", "lstat", "lstat64", "madvise", "memfd_create", "mincore", "mkdir", "mkdirat", "mknod", "mknodat", "mlock", "mlock2", "m<PERSON>all", "mmap", "mmap2", "mprotect", "mq_getsetattr", "mq_notify", "mq_open", "mq_timedreceive", "mq_timedsend", "mq_unlink", "mremap", "msgctl", "msgget", "msgrcv", "msgsnd", "msync", "munlock", "mun<PERSON><PERSON>", "mun<PERSON>p", "nanosleep", "newfstatat", "open", "openat", "pause", "pipe", "pipe2", "poll", "ppoll", "prctl", "pread64", "preadv", "prlimit64", "pselect6", "pwrite64", "pwritev", "read", "readahead", "readlink", "readlinkat", "readv", "recv", "recvfrom", "recvmmsg", "recvmsg", "remap_file_pages", "removexattr", "rename", "renameat", "renameat2", "restart_syscall", "rmdir", "rt_sigaction", "rt_sigpending", "rt_sigprocmask", "rt_sigqueueinfo", "rt_sigreturn", "rt_sigsuspend", "rt_sigtimedwait", "rt_tgsigqueueinfo", "sched_getaffinity", "sched_getattr", "sched_getparam", "sched_get_priority_max", "sched_get_priority_min", "sched_getscheduler", "sched_rr_get_interval", "sched_setaffinity", "sched_setattr", "sched_setparam", "sched_setscheduler", "sched_yield", "seccomp", "select", "semctl", "semget", "semop", "semtimedop", "send", "sendfile", "sendfile64", "sendmmsg", "sendmsg", "sendto", "setfsgid", "setfsgid32", "set<PERSON>uid", "setfsuid32", "<PERSON><PERSON>d", "setgid32", "setgroups", "setgroups32", "setitimer", "setpgid", "setpriority", "set<PERSON>gi<PERSON>", "setregid32", "<PERSON><PERSON><PERSON><PERSON>", "setresgid32", "setresuid", "setresuid32", "set<PERSON><PERSON>", "setreuid32", "setrlimit", "set_robust_list", "setsid", "setsockopt", "set_thread_area", "set_tid_address", "setuid", "setuid32", "<PERSON><PERSON><PERSON><PERSON>", "shmat", "shmctl", "shmdt", "shmget", "shutdown", "sigaltsta<PERSON>", "signalfd", "signalfd4", "sigret<PERSON>", "socket", "socketcall", "socketpair", "splice", "stat", "stat64", "statfs", "statfs64", "statx", "symlink", "symlinkat", "sync", "sync_file_range", "syncfs", "sysinfo", "tee", "tgkill", "time", "timer_create", "timer_delete", "timer_getoverrun", "timer_gettime", "timer_settime", "times", "tkill", "truncate", "truncate64", "ugetrlimit", "umask", "uname", "unlink", "unlinkat", "utime", "utimensat", "utimes", "vfork", "vmsplice", "wait4", "waitid", "wait<PERSON>", "write", "writev"], "action": "SCMP_ACT_ALLOW"}, {"names": ["ptrace"], "action": "SCMP_ACT_ERRNO", "args": [], "comment": "Deny ptrace for security"}, {"names": ["mount", "umount", "umount2"], "action": "SCMP_ACT_ERRNO", "args": [], "comment": "Deny mount operations"}, {"names": ["reboot", "swapon", "swapoff"], "action": "SCMP_ACT_ERRNO", "args": [], "comment": "Deny system control operations"}, {"names": ["kexec_load", "kexec_file_load"], "action": "SCMP_ACT_ERRNO", "args": [], "comment": "Deny kernel loading"}, {"names": ["init_module", "finit_module", "delete_module"], "action": "SCMP_ACT_ERRNO", "args": [], "comment": "Deny kernel module operations"}]}