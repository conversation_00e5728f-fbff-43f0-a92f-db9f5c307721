#include <tunables/global>

# AppArmor profile for CVLeap job submission sandbox
# Restricts application access to necessary resources only

/app/entrypoint.sh flags=(complain) {
  #include <abstractions/base>
  #include <abstractions/bash>
  #include <abstractions/consoles>
  #include <abstractions/nameservice>
  #include <abstractions/python>
  #include <abstractions/ssl_certs>

  # Capabilities
  capability setuid,
  capability setgid,
  capability kill,
  capability net_bind_service,

  # Network access
  network inet stream,
  network inet dgram,
  network inet6 stream,
  network inet6 dgram,
  network unix stream,
  network unix dgram,

  # File system access
  / r,
  /app/** r,
  /app/entrypoint.sh ix,
  /app/agent-core/** r,
  /app/adapter-sdk/** r,
  /app/sidecar/** r,
  /app/manifests/** r,
  /app/security/** r,

  # Executables
  /usr/local/bin/terminald ix,
  /usr/bin/python3.12 ix,
  /usr/bin/tmux ix,
  /usr/bin/curl ix,
  /usr/bin/wget ix,
  /bin/bash ix,
  /bin/sh ix,
  /bin/cat ix,
  /bin/echo ix,
  /bin/grep ix,
  /bin/ps ix,
  /bin/kill ix,
  /usr/bin/pgrep ix,
  /usr/bin/pkill ix,

  # System libraries
  /lib/** mr,
  /lib64/** mr,
  /usr/lib/** mr,
  /usr/lib64/** mr,

  # Python and Playwright
  /opt/venv/** r,
  /home/<USER>/.cache/ms-playwright/** rw,
  /home/<USER>/.local/** rw,
  /usr/share/ca-certificates/** r,

  # Temporary and log directories
  /tmp/** rw,
  /tmp/artifacts/** rw,
  /tmp/screenshots/** rw,
  /var/log/cvleap/** rw,
  /var/tmp/** rw,

  # Process information
  /proc/*/stat r,
  /proc/*/status r,
  /proc/*/cmdline r,
  /proc/*/environ r,
  /proc/*/fd/ r,
  /proc/*/fd/* r,
  /proc/sys/kernel/random/uuid r,
  /proc/meminfo r,
  /proc/cpuinfo r,
  /proc/loadavg r,
  /proc/uptime r,
  /proc/version r,

  # System information
  /sys/class/net/ r,
  /sys/devices/system/cpu/ r,
  /sys/devices/system/cpu/online r,

  # Device access (limited)
  /dev/null rw,
  /dev/zero r,
  /dev/random r,
  /dev/urandom r,
  /dev/tty rw,
  /dev/pts/* rw,

  # DNS resolution
  /etc/hosts r,
  /etc/resolv.conf r,
  /etc/nsswitch.conf r,
  /etc/passwd r,
  /etc/group r,

  # Time zone
  /etc/localtime r,
  /usr/share/zoneinfo/** r,

  # SSL/TLS
  /etc/ssl/certs/** r,
  /usr/share/ca-certificates/** r,

  # Deny dangerous operations
  deny /boot/** rwklx,
  deny /etc/shadow rwklx,
  deny /etc/gshadow rwklx,
  deny /etc/sudoers rwklx,
  deny /etc/sudoers.d/** rwklx,
  deny /root/** rwklx,
  deny /home/<USER>/.ssh/** rwklx,
  deny /var/spool/cron/** rwklx,
  deny /etc/cron.d/** rwklx,
  deny /etc/crontab rwklx,

  # Deny network configuration
  deny /etc/network/** rwklx,
  deny /etc/netplan/** rwklx,
  deny /etc/systemd/network/** rwklx,

  # Deny system configuration
  deny /etc/systemd/** rwklx,
  deny /etc/init.d/** rwklx,
  deny /etc/rc*.d/** rwklx,

  # Deny kernel modules
  deny /lib/modules/** rwklx,
  deny /usr/lib/modules/** rwklx,

  # Deny mount points
  deny /mnt/** rwklx,
  deny /media/** rwklx,

  # Signal handling
  signal (send) set=(term, kill, int, quit, usr1, usr2),
  signal (receive) set=(term, kill, int, quit, usr1, usr2),

  # Child profiles
  /usr/local/bin/terminald {
    #include <abstractions/base>
    #include <abstractions/nameservice>

    capability net_bind_service,

    network inet stream,
    network inet dgram,
    network unix stream,

    /usr/local/bin/terminald mr,
    /lib/** mr,
    /lib64/** mr,
    /usr/lib/** mr,

    /tmp/** rw,
    /var/log/cvleap/** rw,
    /proc/*/stat r,
    /proc/*/status r,

    /dev/null rw,
    /dev/zero r,
    /dev/random r,
    /dev/urandom r,
    /dev/pts/* rw,

    signal (send) set=(term, kill, int),
    signal (receive) set=(term, kill, int),
  }

  /opt/venv/bin/python {
    #include <abstractions/base>
    #include <abstractions/python>
    #include <abstractions/nameservice>
    #include <abstractions/ssl_certs>

    network inet stream,
    network inet dgram,
    network inet6 stream,
    network inet6 dgram,

    /opt/venv/** r,
    /app/** r,
    /tmp/** rw,
    /var/log/cvleap/** rw,
    /home/<USER>/.cache/** rw,
    /home/<USER>/.local/** rw,

    /proc/*/stat r,
    /proc/*/status r,
    /proc/*/cmdline r,
    /proc/meminfo r,
    /proc/cpuinfo r,

    /dev/null rw,
    /dev/zero r,
    /dev/random r,
    /dev/urandom r,

    signal (send) set=(term, kill, int),
    signal (receive) set=(term, kill, int),
  }
}
