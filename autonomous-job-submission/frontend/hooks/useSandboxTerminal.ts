/**
 * React hook for sandbox terminal integration
 * Lazy loads xterm.js, handles WebSocket connection, and provides terminal interface
 */

import { useState, useEffect, useRef, useCallback } from 'react'
import { useSession } from 'next-auth/react'

// Lazy import types (actual imports happen dynamically)
type Terminal = any
type FitAddon = any
type WebLinksAddon = any
type SearchAddon = any

interface TerminalConfig {
  theme?: {
    background?: string
    foreground?: string
    cursor?: string
    selection?: string
  }
  fontSize?: number
  fontFamily?: string
  rows?: number
  cols?: number
  scrollback?: number
}

interface SandboxTerminalState {
  isConnected: boolean
  isConnecting: boolean
  isLoading: boolean
  error: string | null
  terminal: Terminal | null
  websocket: WebSocket | null
  ringBuffer: string
  lastActivity: Date | null
}

interface SandboxTerminalActions {
  connect: () => Promise<void>
  disconnect: () => void
  sendInput: (input: string) => void
  captureContent: () => string
  clearTerminal: () => void
  resize: (cols: number, rows: number) => void
  search: (term: string) => void
}

interface UseSandboxTerminalOptions {
  sandboxId: string
  autoConnect?: boolean
  config?: TerminalConfig
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: string) => void
  onData?: (data: string) => void
}

interface UseSandboxTerminalReturn extends SandboxTerminalState, SandboxTerminalActions {
  containerRef: React.RefObject<HTMLDivElement>
  isSupported: boolean
}

const DEFAULT_CONFIG: TerminalConfig = {
  theme: {
    background: '#1e1e1e',
    foreground: '#d4d4d4',
    cursor: '#ffffff',
    selection: '#264f78'
  },
  fontSize: 14,
  fontFamily: 'Menlo, Monaco, "Courier New", monospace',
  rows: 30,
  cols: 120,
  scrollback: 10000
}

export function useSandboxTerminal(options: UseSandboxTerminalOptions): UseSandboxTerminalReturn {
  const { sandboxId, autoConnect = false, config = {}, onConnect, onDisconnect, onError, onData } = options
  const { data: session } = useSession()
  
  // State
  const [state, setState] = useState<SandboxTerminalState>({
    isConnected: false,
    isConnecting: false,
    isLoading: false,
    error: null,
    terminal: null,
    websocket: null,
    ringBuffer: '',
    lastActivity: null
  })

  // Refs
  const containerRef = useRef<HTMLDivElement>(null)
  const terminalRef = useRef<Terminal | null>(null)
  const websocketRef = useRef<WebSocket | null>(null)
  const addonsRef = useRef<{
    fit?: FitAddon
    webLinks?: WebLinksAddon
    search?: SearchAddon
  }>({})

  // Check if terminal is supported
  const isSupported = typeof window !== 'undefined' && 'WebSocket' in window

  /**
   * Lazy load xterm.js and addons
   */
  const loadTerminalLibraries = useCallback(async () => {
    if (terminalRef.current) return terminalRef.current

    setState(prev => ({ ...prev, isLoading: true }))

    try {
      // Dynamic imports for xterm.js
      const [
        { Terminal },
        { FitAddon },
        { WebLinksAddon },
        { SearchAddon }
      ] = await Promise.all([
        import('xterm'),
        import('xterm-addon-fit'),
        import('xterm-addon-web-links'),
        import('xterm-addon-search')
      ])

      // Create terminal instance
      const terminal = new Terminal({
        ...DEFAULT_CONFIG,
        ...config,
        allowProposedApi: true
      })

      // Create addons
      const fitAddon = new FitAddon()
      const webLinksAddon = new WebLinksAddon()
      const searchAddon = new SearchAddon()

      // Load addons
      terminal.loadAddon(fitAddon)
      terminal.loadAddon(webLinksAddon)
      terminal.loadAddon(searchAddon)

      // Store references
      terminalRef.current = terminal
      addonsRef.current = { fit: fitAddon, webLinks: webLinksAddon, search: searchAddon }

      // Open terminal in container
      if (containerRef.current) {
        terminal.open(containerRef.current)
        fitAddon.fit()
      }

      // Set up event handlers
      terminal.onData((data: string) => {
        if (websocketRef.current?.readyState === WebSocket.OPEN) {
          websocketRef.current.send(data)
        }
      })

      terminal.onResize(({ cols, rows }) => {
        if (websocketRef.current?.readyState === WebSocket.OPEN) {
          // Send resize signal to terminal daemon
          websocketRef.current.send(`\x1b[8;${rows};${cols}t`)
        }
      })

      setState(prev => ({ ...prev, terminal, isLoading: false }))
      return terminal

    } catch (error) {
      const errorMessage = `Failed to load terminal: ${error instanceof Error ? error.message : 'Unknown error'}`
      setState(prev => ({ ...prev, error: errorMessage, isLoading: false }))
      onError?.(errorMessage)
      throw error
    }
  }, [config, onError])

  /**
   * Connect to sandbox terminal WebSocket
   */
  const connect = useCallback(async () => {
    if (!session?.user || state.isConnecting || state.isConnected) {
      return
    }

    setState(prev => ({ ...prev, isConnecting: true, error: null }))

    try {
      // Load terminal libraries if not already loaded
      const terminal = await loadTerminalLibraries()

      // Get WebSocket connection details from API
      const response = await fetch(`/api/sandbox/${sandboxId}/watch`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const { websocket: wsConfig, session: sessionInfo } = await response.json()

      // Create WebSocket connection
      const ws = new WebSocket(wsConfig.url, wsConfig.protocol)
      websocketRef.current = ws

      // WebSocket event handlers
      ws.onopen = () => {
        setState(prev => ({ 
          ...prev, 
          isConnected: true, 
          isConnecting: false, 
          websocket: ws,
          lastActivity: new Date()
        }))
        
        terminal.clear()
        terminal.writeln('\x1b[32m✓ Connected to sandbox terminal\x1b[0m')
        terminal.writeln(`\x1b[36mSandbox: ${sessionInfo.sandbox_id}\x1b[0m`)
        terminal.writeln(`\x1b[36mRole: ${sessionInfo.role}\x1b[0m`)
        terminal.writeln('')
        
        onConnect?.()
      }

      ws.onmessage = (event) => {
        const data = event.data
        terminal.write(data)
        
        setState(prev => ({ 
          ...prev, 
          ringBuffer: prev.ringBuffer + data,
          lastActivity: new Date()
        }))
        
        onData?.(data)
      }

      ws.onclose = (event) => {
        setState(prev => ({ 
          ...prev, 
          isConnected: false, 
          isConnecting: false, 
          websocket: null 
        }))
        
        if (event.code !== 1000) { // Not a normal closure
          const errorMessage = `Connection closed: ${event.reason || 'Unknown reason'} (${event.code})`
          terminal.writeln(`\x1b[31m✗ ${errorMessage}\x1b[0m`)
          setState(prev => ({ ...prev, error: errorMessage }))
          onError?.(errorMessage)
        } else {
          terminal.writeln('\x1b[33m✓ Connection closed\x1b[0m')
        }
        
        onDisconnect?.()
      }

      ws.onerror = (error) => {
        const errorMessage = 'WebSocket connection error'
        setState(prev => ({ 
          ...prev, 
          error: errorMessage, 
          isConnecting: false 
        }))
        terminal.writeln(`\x1b[31m✗ ${errorMessage}\x1b[0m`)
        onError?.(errorMessage)
      }

      // Set connection timeout
      setTimeout(() => {
        if (ws.readyState === WebSocket.CONNECTING) {
          ws.close()
          const errorMessage = 'Connection timeout'
          setState(prev => ({ ...prev, error: errorMessage, isConnecting: false }))
          onError?.(errorMessage)
        }
      }, wsConfig.timeout || 10000)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Connection failed'
      setState(prev => ({ 
        ...prev, 
        error: errorMessage, 
        isConnecting: false 
      }))
      onError?.(errorMessage)
    }
  }, [session, sandboxId, state.isConnecting, state.isConnected, loadTerminalLibraries, onConnect, onDisconnect, onError, onData])

  /**
   * Disconnect from terminal
   */
  const disconnect = useCallback(() => {
    if (websocketRef.current) {
      websocketRef.current.close(1000, 'User disconnected')
      websocketRef.current = null
    }
    
    setState(prev => ({ 
      ...prev, 
      isConnected: false, 
      isConnecting: false, 
      websocket: null 
    }))
  }, [])

  /**
   * Send input to terminal
   */
  const sendInput = useCallback((input: string) => {
    if (websocketRef.current?.readyState === WebSocket.OPEN) {
      websocketRef.current.send(input)
    }
  }, [])

  /**
   * Capture current terminal content
   */
  const captureContent = useCallback((): string => {
    if (terminalRef.current) {
      return terminalRef.current.getSelection() || terminalRef.current.buffer.active.getLine(0)?.translateToString() || ''
    }
    return ''
  }, [])

  /**
   * Clear terminal
   */
  const clearTerminal = useCallback(() => {
    if (terminalRef.current) {
      terminalRef.current.clear()
    }
    setState(prev => ({ ...prev, ringBuffer: '' }))
  }, [])

  /**
   * Resize terminal
   */
  const resize = useCallback((cols: number, rows: number) => {
    if (terminalRef.current && addonsRef.current.fit) {
      terminalRef.current.resize(cols, rows)
      addonsRef.current.fit.fit()
    }
  }, [])

  /**
   * Search in terminal
   */
  const search = useCallback((term: string) => {
    if (addonsRef.current.search) {
      addonsRef.current.search.findNext(term)
    }
  }, [])

  // Auto-connect if enabled
  useEffect(() => {
    if (autoConnect && session?.user && !state.isConnected && !state.isConnecting) {
      connect()
    }
  }, [autoConnect, session, state.isConnected, state.isConnecting, connect])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
      if (terminalRef.current) {
        terminalRef.current.dispose()
        terminalRef.current = null
      }
    }
  }, [disconnect])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!state.isConnected) return

      // Cmd+K or Ctrl+K - Capture content
      if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
        event.preventDefault()
        const content = captureContent()
        navigator.clipboard?.writeText(content)
      }

      // Cmd+C or Ctrl+C - Send interrupt
      if ((event.metaKey || event.ctrlKey) && event.key === 'c' && !terminalRef.current?.hasSelection()) {
        event.preventDefault()
        sendInput('\x03') // Ctrl+C
      }

      // Cmd+D or Ctrl+D - Send EOF
      if ((event.metaKey || event.ctrlKey) && event.key === 'd') {
        event.preventDefault()
        sendInput('\x04') // Ctrl+D
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [state.isConnected, captureContent, sendInput])

  return {
    ...state,
    containerRef,
    isSupported,
    connect,
    disconnect,
    sendInput,
    captureContent,
    clearTerminal,
    resize,
    search
  }
}
