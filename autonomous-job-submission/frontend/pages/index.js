import { useState, useEffect } from 'react'

export default function Home() {
  const [status, setStatus] = useState(null)
  const [metrics, setMetrics] = useState(null)
  const [sandboxId, setSandboxId] = useState('dev-sandbox-123')
  const [loading, setLoading] = useState(false)

  const fetchStatus = async () => {
    try {
      const [statusRes, metricsRes] = await Promise.all([
        fetch('/api/status'),
        fetch('/api/metrics')
      ])
      const statusData = await statusRes.json()
      const metricsData = await metricsRes.json()
      setStatus(statusData)
      setMetrics(metricsData)
    } catch (err) {
      console.error('Failed to fetch data:', err)
    }
  }

  const startJob = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/start', { method: 'POST' })
      const data = await response.json()
      alert(data.message)
      fetchStatus()
    } catch (err) {
      alert('Failed to start job: ' + err.message)
    }
    setLoading(false)
  }

  useEffect(() => {
    fetchStatus()
    const interval = setInterval(fetchStatus, 5000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', backgroundColor: '#1a1a1a', color: 'white', minHeight: '100vh' }}>
      <h1 style={{ color: '#0066cc', textAlign: 'center' }}>🚀 CVLeap Job Submission System</h1>
      
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
        <div style={{ backgroundColor: '#2a2a2a', padding: '20px', borderRadius: '8px' }}>
          <h2>📊 System Status</h2>
          {status ? (
            <div>
              <p>✅ Backend: Connected</p>
              <p>📈 Status: <span style={{ color: '#00ff00' }}>{status.status}</span></p>
              <p>🆔 Sandbox: {status.sandbox_id}</p>
              <p>📝 Current Step: {status.current_step}</p>
              <p>⏰ Last Check: {new Date().toLocaleTimeString()}</p>
            </div>
          ) : (
            <p>⏳ Loading...</p>
          )}
        </div>

        <div style={{ backgroundColor: '#2a2a2a', padding: '20px', borderRadius: '8px' }}>
          <h2>💻 System Metrics</h2>
          {metrics ? (
            <div>
              <p>🧠 CPU: {metrics.system.cpu_percent}%</p>
              <p>💾 Memory: {metrics.system.memory_percent}% ({metrics.system.memory_used_mb}MB / {metrics.system.memory_total_mb}MB)</p>
              <p>💿 Disk: {metrics.system.disk_percent}% ({metrics.system.disk_used_gb}GB / {metrics.system.disk_total_gb}GB)</p>
              <p>⏱️ Uptime: {metrics.uptime}s</p>
            </div>
          ) : (
            <p>⏳ Loading...</p>
          )}
        </div>
      </div>

      <div style={{ backgroundColor: '#2a2a2a', padding: '20px', borderRadius: '8px', marginBottom: '20px' }}>
        <h2>🎯 Job Control</h2>
        <div style={{ marginBottom: '15px' }}>
          <label>Sandbox ID:</label>
          <input 
            type="text" 
            value={sandboxId} 
            onChange={(e) => setSandboxId(e.target.value)}
            style={{ marginLeft: '10px', padding: '8px', backgroundColor: '#3a3a3a', color: 'white', border: '1px solid #555', borderRadius: '4px' }}
          />
        </div>
        <button 
          onClick={startJob}
          disabled={loading}
          style={{ 
            backgroundColor: loading ? '#555' : '#0066cc', 
            color: 'white', 
            border: 'none', 
            padding: '10px 20px', 
            borderRadius: '4px', 
            cursor: loading ? 'not-allowed' : 'pointer',
            fontSize: '16px'
          }}
        >
          {loading ? '⏳ Starting...' : '🚀 Start Job Submission'}
        </button>
      </div>

      <div style={{ backgroundColor: '#2a2a2a', padding: '20px', borderRadius: '8px' }}>
        <h2>🔗 Quick Links</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
          <a href="http://localhost:8080/ready" target="_blank" style={{ color: '#0066cc', textDecoration: 'none', padding: '10px', backgroundColor: '#3a3a3a', borderRadius: '4px', textAlign: 'center' }}>
            🏥 Health Check
          </a>
          <a href="http://localhost:8080/rpc/status" target="_blank" style={{ color: '#0066cc', textDecoration: 'none', padding: '10px', backgroundColor: '#3a3a3a', borderRadius: '4px', textAlign: 'center' }}>
            📊 Status API
          </a>
          <a href="http://localhost:8080/rpc/metrics" target="_blank" style={{ color: '#0066cc', textDecoration: 'none', padding: '10px', backgroundColor: '#3a3a3a', borderRadius: '4px', textAlign: 'center' }}>
            📈 Metrics API
          </a>
          <a href="http://localhost:8080/docs" target="_blank" style={{ color: '#0066cc', textDecoration: 'none', padding: '10px', backgroundColor: '#3a3a3a', borderRadius: '4px', textAlign: 'center' }}>
            📚 API Docs
          </a>
        </div>
      </div>

      <div style={{ marginTop: '20px', fontSize: '14px', color: '#888', backgroundColor: '#2a2a2a', padding: '15px', borderRadius: '8px' }}>
        <h3>💡 Development Tips:</h3>
        <ul>
          <li>Backend API is running at <code>http://localhost:8080</code></li>
          <li>Check logs in the <code>logs/</code> directory for troubleshooting</li>
          <li>Terminal mode is disabled in minimal mode for simplicity</li>
          <li>This is a development environment - not for production use</li>
        </ul>
      </div>
    </div>
  )
}
