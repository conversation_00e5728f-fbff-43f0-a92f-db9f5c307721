"""
FastAPI sidecar server for job submission sandbox
Provides health checks, status reporting, and signal handling for Temporal workflows
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from fastapi import FastAPI, HTTPException, BackgroundTasks, Request
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

from .status import StatusManager
from .signals import SignalHandler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic models
class HealthResponse(BaseModel):
    status: str = "healthy"
    timestamp: datetime
    uptime: float
    sandbox_id: Optional[str] = None
    terminal_enabled: bool = False
    current_job: Optional[str] = None

class StatusResponse(BaseModel):
    sandbox_id: str
    status: str
    progress: float = Field(ge=0, le=100)
    current_step: str
    total_steps: int
    elapsed_time: float
    estimated_remaining: Optional[float] = None
    portal: Optional[str] = None
    job_url: Optional[str] = None
    checkpoints: List[Dict[str, Any]] = []
    artifacts: List[str] = []
    error: Optional[str] = None

class SignalRequest(BaseModel):
    signal_type: str
    payload: Optional[Dict[str, Any]] = None
    sender: str = "temporal"

class SignalResponse(BaseModel):
    success: bool
    message: str
    timestamp: datetime

# Global state
app_state = {
    "start_time": time.time(),
    "sandbox_id": None,
    "current_execution": None,
    "status_manager": None,
    "signal_handler": None
}

# FastAPI app
app = FastAPI(
    title="Job Submission Sidecar",
    description="Sidecar API for autonomous job submission sandboxes",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Initialize the sidecar server"""
    logger.info("Starting job submission sidecar server")
    
    # Initialize components
    app_state["status_manager"] = StatusManager()
    app_state["signal_handler"] = SignalHandler()
    
    # Get sandbox ID from environment
    import os
    app_state["sandbox_id"] = os.getenv("SANDBOX_ID", f"sandbox_{int(time.time())}")
    
    logger.info(f"Sidecar initialized for sandbox: {app_state['sandbox_id']}")

@app.get("/ready", response_model=HealthResponse)
async def health_check():
    """Kubernetes readiness probe endpoint"""
    
    uptime = time.time() - app_state["start_time"]
    terminal_enabled = os.getenv("ENABLE_TERMINAL", "0") == "1"
    
    current_job = None
    if app_state["current_execution"]:
        current_job = app_state["current_execution"].get("job_url")
    
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(),
        uptime=uptime,
        sandbox_id=app_state["sandbox_id"],
        terminal_enabled=terminal_enabled,
        current_job=current_job
    )

@app.get("/rpc/status", response_model=StatusResponse)
async def get_status():
    """Get current job submission status"""
    
    status_manager = app_state["status_manager"]
    if not status_manager:
        raise HTTPException(status_code=503, detail="Status manager not initialized")
    
    status_data = status_manager.get_current_status()
    
    return StatusResponse(
        sandbox_id=app_state["sandbox_id"],
        status=status_data.get("status", "idle"),
        progress=status_data.get("progress", 0.0),
        current_step=status_data.get("current_step", ""),
        total_steps=status_data.get("total_steps", 0),
        elapsed_time=status_data.get("elapsed_time", 0.0),
        estimated_remaining=status_data.get("estimated_remaining"),
        portal=status_data.get("portal"),
        job_url=status_data.get("job_url"),
        checkpoints=status_data.get("checkpoints", []),
        artifacts=status_data.get("artifacts", []),
        error=status_data.get("error")
    )

@app.get("/rpc/status/stream")
async def stream_status():
    """Stream real-time status updates via Server-Sent Events"""
    
    async def generate_status_stream():
        status_manager = app_state["status_manager"]
        
        while True:
            try:
                status_data = status_manager.get_current_status()
                
                # Format as Server-Sent Event
                event_data = {
                    "timestamp": datetime.now().isoformat(),
                    "sandbox_id": app_state["sandbox_id"],
                    **status_data
                }
                
                yield f"data: {json.dumps(event_data)}\n\n"
                
                # Wait before next update
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in status stream: {e}")
                yield f"data: {json.dumps({'error': str(e)})}\n\n"
                break
    
    return StreamingResponse(
        generate_status_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )

@app.post("/rpc/signal", response_model=SignalResponse)
async def handle_signal(signal_request: SignalRequest, background_tasks: BackgroundTasks):
    """Handle signals from Temporal workflow"""
    
    logger.info(f"Received signal: {signal_request.signal_type} from {signal_request.sender}")
    
    signal_handler = app_state["signal_handler"]
    if not signal_handler:
        raise HTTPException(status_code=503, detail="Signal handler not initialized")
    
    try:
        # Process signal in background
        background_tasks.add_task(
            signal_handler.process_signal,
            signal_request.signal_type,
            signal_request.payload or {}
        )
        
        return SignalResponse(
            success=True,
            message=f"Signal {signal_request.signal_type} accepted",
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Failed to process signal: {e}")
        return SignalResponse(
            success=False,
            message=f"Signal processing failed: {str(e)}",
            timestamp=datetime.now()
        )

@app.get("/rpc/checkpoints")
async def get_checkpoints():
    """Get all checkpoints for the current execution"""
    
    status_manager = app_state["status_manager"]
    if not status_manager:
        raise HTTPException(status_code=503, detail="Status manager not initialized")
    
    checkpoints = status_manager.get_checkpoints()
    
    return {
        "sandbox_id": app_state["sandbox_id"],
        "checkpoints": checkpoints,
        "total_count": len(checkpoints)
    }

@app.get("/rpc/artifacts")
async def get_artifacts():
    """Get all artifacts generated during execution"""
    
    artifacts_dir = Path("/tmp/artifacts")
    artifacts = []
    
    if artifacts_dir.exists():
        for artifact_file in artifacts_dir.glob("*"):
            if artifact_file.is_file():
                artifacts.append({
                    "name": artifact_file.name,
                    "path": str(artifact_file),
                    "size": artifact_file.stat().st_size,
                    "created": artifact_file.stat().st_ctime,
                    "type": artifact_file.suffix.lstrip('.')
                })
    
    return {
        "sandbox_id": app_state["sandbox_id"],
        "artifacts": artifacts,
        "total_count": len(artifacts)
    }

@app.post("/rpc/start")
async def start_execution(request: Request):
    """Start job submission execution"""
    
    payload = await request.json()
    
    logger.info(f"Starting execution for job: {payload.get('job_url')}")
    
    # Store execution context
    app_state["current_execution"] = {
        "job_url": payload.get("job_url"),
        "portal": payload.get("portal"),
        "user_profile": payload.get("user_profile"),
        "start_time": time.time(),
        "status": "starting"
    }
    
    # Update status manager
    status_manager = app_state["status_manager"]
    if status_manager:
        status_manager.start_execution(payload)
    
    return {
        "success": True,
        "message": "Execution started",
        "sandbox_id": app_state["sandbox_id"],
        "execution_id": payload.get("execution_id")
    }

@app.post("/rpc/stop")
async def stop_execution():
    """Stop current execution"""
    
    logger.info("Stopping current execution")
    
    # Signal stop to status manager
    status_manager = app_state["status_manager"]
    if status_manager:
        status_manager.stop_execution()
    
    # Clear execution context
    app_state["current_execution"] = None
    
    return {
        "success": True,
        "message": "Execution stopped",
        "sandbox_id": app_state["sandbox_id"]
    }

@app.post("/rpc/pause")
async def pause_execution():
    """Pause current execution"""
    
    logger.info("Pausing current execution")
    
    status_manager = app_state["status_manager"]
    if status_manager:
        status_manager.pause_execution()
    
    return {
        "success": True,
        "message": "Execution paused",
        "sandbox_id": app_state["sandbox_id"]
    }

@app.post("/rpc/resume")
async def resume_execution():
    """Resume paused execution"""
    
    logger.info("Resuming execution")
    
    status_manager = app_state["status_manager"]
    if status_manager:
        status_manager.resume_execution()
    
    return {
        "success": True,
        "message": "Execution resumed",
        "sandbox_id": app_state["sandbox_id"]
    }

@app.get("/rpc/logs")
async def get_logs(lines: int = 100):
    """Get recent log entries"""
    
    log_file = Path("/tmp/job_submission.log")
    logs = []
    
    if log_file.exists():
        try:
            with open(log_file, 'r') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
                for line in recent_lines:
                    logs.append(line.strip())
        except Exception as e:
            logger.error(f"Failed to read logs: {e}")
    
    return {
        "sandbox_id": app_state["sandbox_id"],
        "logs": logs,
        "total_lines": len(logs)
    }

@app.get("/rpc/metrics")
async def get_metrics():
    """Get performance metrics"""
    
    import psutil
    
    # System metrics
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    # Application metrics
    uptime = time.time() - app_state["start_time"]
    
    execution_metrics = {}
    if app_state["current_execution"]:
        execution_start = app_state["current_execution"].get("start_time", 0)
        execution_metrics = {
            "execution_duration": time.time() - execution_start,
            "portal": app_state["current_execution"].get("portal"),
            "status": app_state["current_execution"].get("status")
        }
    
    return {
        "sandbox_id": app_state["sandbox_id"],
        "timestamp": datetime.now().isoformat(),
        "uptime": uptime,
        "system": {
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_used_mb": memory.used // 1024 // 1024,
            "memory_total_mb": memory.total // 1024 // 1024,
            "disk_percent": disk.percent,
            "disk_used_gb": disk.used // 1024 // 1024 // 1024,
            "disk_total_gb": disk.total // 1024 // 1024 // 1024
        },
        "execution": execution_metrics
    }

if __name__ == "__main__":
    import os
    
    port = int(os.getenv("SIDECAR_PORT", "8080"))
    host = os.getenv("SIDECAR_HOST", "0.0.0.0")
    
    uvicorn.run(
        "server:app",
        host=host,
        port=port,
        log_level="info",
        access_log=True
    )
