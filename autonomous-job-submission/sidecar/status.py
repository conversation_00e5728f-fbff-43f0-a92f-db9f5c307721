"""
Status management for job submission execution
Tracks progress, checkpoints, and execution state
"""

import time
import threading
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class Checkpoint:
    name: str
    description: str
    timestamp: float
    url: Optional[str] = None
    screenshot_path: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ExecutionState:
    status: str = "idle"  # idle, starting, running, paused, completed, failed
    progress: float = 0.0
    current_step: str = ""
    total_steps: int = 0
    start_time: float = 0.0
    portal: Optional[str] = None
    job_url: Optional[str] = None
    error: Optional[str] = None
    
    # Collections
    checkpoints: List[Checkpoint] = field(default_factory=list)
    artifacts: List[str] = field(default_factory=list)
    
    # Timing
    step_start_time: float = 0.0
    estimated_remaining: Optional[float] = None

class StatusManager:
    """Manages execution status and progress tracking"""
    
    def __init__(self):
        self.state = ExecutionState()
        self.lock = threading.RLock()
        self.progress_callbacks = []
        self.checkpoint_callbacks = []
        
        # Performance tracking
        self.step_durations = []
        self.avg_step_duration = 30.0  # Default estimate
        
    def start_execution(self, execution_data: Dict[str, Any]):
        """Start a new execution"""
        with self.lock:
            self.state = ExecutionState(
                status="starting",
                start_time=time.time(),
                portal=execution_data.get("portal"),
                job_url=execution_data.get("job_url"),
                total_steps=execution_data.get("total_steps", 10)  # Default estimate
            )
            
            logger.info(f"Started execution tracking for {self.state.portal}")
            self._notify_progress()
    
    def update_progress(self, current_step: int, total_steps: int, step_name: str = ""):
        """Update execution progress"""
        with self.lock:
            if self.state.status in ["idle", "completed", "failed"]:
                return
            
            # Update step timing
            now = time.time()
            if self.state.step_start_time > 0:
                step_duration = now - self.state.step_start_time
                self.step_durations.append(step_duration)
                
                # Update average step duration (rolling average)
                if len(self.step_durations) > 10:
                    self.step_durations = self.step_durations[-10:]
                self.avg_step_duration = sum(self.step_durations) / len(self.step_durations)
            
            # Update state
            self.state.current_step = step_name
            self.state.total_steps = total_steps
            self.state.progress = (current_step / total_steps) * 100 if total_steps > 0 else 0
            self.state.step_start_time = now
            
            # Estimate remaining time
            remaining_steps = total_steps - current_step
            self.state.estimated_remaining = remaining_steps * self.avg_step_duration
            
            # Update status
            if self.state.status == "starting":
                self.state.status = "running"
            
            logger.debug(f"Progress: {self.state.progress:.1f}% - {step_name}")
            self._notify_progress()
    
    def add_checkpoint(self, checkpoint: Checkpoint):
        """Add a checkpoint to the execution"""
        with self.lock:
            self.state.checkpoints.append(checkpoint)
            
            logger.info(f"Checkpoint: {checkpoint.name} - {checkpoint.description}")
            self._notify_checkpoint(checkpoint)
    
    def add_artifact(self, artifact_path: str):
        """Add an artifact to the execution"""
        with self.lock:
            self.state.artifacts.append(artifact_path)
            logger.debug(f"Artifact added: {artifact_path}")
    
    def set_error(self, error_message: str):
        """Set execution error state"""
        with self.lock:
            self.state.status = "failed"
            self.state.error = error_message
            
            logger.error(f"Execution failed: {error_message}")
            self._notify_progress()
    
    def complete_execution(self):
        """Mark execution as completed"""
        with self.lock:
            self.state.status = "completed"
            self.state.progress = 100.0
            
            duration = time.time() - self.state.start_time
            logger.info(f"Execution completed in {duration:.2f} seconds")
            self._notify_progress()
    
    def pause_execution(self):
        """Pause the current execution"""
        with self.lock:
            if self.state.status == "running":
                self.state.status = "paused"
                logger.info("Execution paused")
                self._notify_progress()
    
    def resume_execution(self):
        """Resume paused execution"""
        with self.lock:
            if self.state.status == "paused":
                self.state.status = "running"
                self.state.step_start_time = time.time()  # Reset step timer
                logger.info("Execution resumed")
                self._notify_progress()
    
    def stop_execution(self):
        """Stop the current execution"""
        with self.lock:
            self.state.status = "stopped"
            logger.info("Execution stopped")
            self._notify_progress()
    
    def get_current_status(self) -> Dict[str, Any]:
        """Get current execution status"""
        with self.lock:
            elapsed_time = time.time() - self.state.start_time if self.state.start_time > 0 else 0
            
            return {
                "status": self.state.status,
                "progress": self.state.progress,
                "current_step": self.state.current_step,
                "total_steps": self.state.total_steps,
                "elapsed_time": elapsed_time,
                "estimated_remaining": self.state.estimated_remaining,
                "portal": self.state.portal,
                "job_url": self.state.job_url,
                "checkpoints": [self._checkpoint_to_dict(cp) for cp in self.state.checkpoints],
                "artifacts": self.state.artifacts.copy(),
                "error": self.state.error,
                "start_time": self.state.start_time
            }
    
    def get_checkpoints(self) -> List[Dict[str, Any]]:
        """Get all checkpoints"""
        with self.lock:
            return [self._checkpoint_to_dict(cp) for cp in self.state.checkpoints]
    
    def get_latest_checkpoint(self) -> Optional[Dict[str, Any]]:
        """Get the most recent checkpoint"""
        with self.lock:
            if self.state.checkpoints:
                return self._checkpoint_to_dict(self.state.checkpoints[-1])
            return None
    
    def add_progress_callback(self, callback):
        """Add a progress update callback"""
        self.progress_callbacks.append(callback)
    
    def add_checkpoint_callback(self, callback):
        """Add a checkpoint callback"""
        self.checkpoint_callbacks.append(callback)
    
    def _checkpoint_to_dict(self, checkpoint: Checkpoint) -> Dict[str, Any]:
        """Convert checkpoint to dictionary"""
        return {
            "name": checkpoint.name,
            "description": checkpoint.description,
            "timestamp": checkpoint.timestamp,
            "datetime": datetime.fromtimestamp(checkpoint.timestamp).isoformat(),
            "url": checkpoint.url,
            "screenshot_path": checkpoint.screenshot_path,
            "metadata": checkpoint.metadata
        }
    
    def _notify_progress(self):
        """Notify progress callbacks"""
        for callback in self.progress_callbacks:
            try:
                callback(self.get_current_status())
            except Exception as e:
                logger.error(f"Progress callback error: {e}")
    
    def _notify_checkpoint(self, checkpoint: Checkpoint):
        """Notify checkpoint callbacks"""
        for callback in self.checkpoint_callbacks:
            try:
                callback(self._checkpoint_to_dict(checkpoint))
            except Exception as e:
                logger.error(f"Checkpoint callback error: {e}")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        with self.lock:
            return {
                "avg_step_duration": self.avg_step_duration,
                "total_steps_completed": len(self.step_durations),
                "step_durations": self.step_durations.copy(),
                "checkpoints_count": len(self.state.checkpoints),
                "artifacts_count": len(self.state.artifacts),
                "execution_efficiency": self._calculate_efficiency()
            }
    
    def _calculate_efficiency(self) -> float:
        """Calculate execution efficiency score"""
        if not self.step_durations:
            return 1.0
        
        # Efficiency based on consistency of step durations
        if len(self.step_durations) < 2:
            return 1.0
        
        import statistics
        try:
            mean_duration = statistics.mean(self.step_durations)
            stdev_duration = statistics.stdev(self.step_durations)
            
            # Lower standard deviation relative to mean = higher efficiency
            coefficient_of_variation = stdev_duration / mean_duration if mean_duration > 0 else 1.0
            efficiency = max(0.0, 1.0 - coefficient_of_variation)
            
            return min(1.0, efficiency)
        except:
            return 1.0
    
    def reset(self):
        """Reset the status manager"""
        with self.lock:
            self.state = ExecutionState()
            self.step_durations.clear()
            self.avg_step_duration = 30.0
            logger.info("Status manager reset")
