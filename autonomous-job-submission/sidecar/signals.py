"""
Signal handling for Temporal workflow communication
Handles pause/resume, configuration updates, and manual interventions
"""

import asyncio
import logging
import json
from typing import Dict, Any, Callable, Optional
from datetime import datetime
import threading

logger = logging.getLogger(__name__)

class SignalHandler:
    """Handles signals from Temporal workflows and external systems"""
    
    def __init__(self):
        self.signal_handlers = {}
        self.signal_queue = asyncio.Queue()
        self.is_processing = False
        self.lock = threading.RLock()
        
        # State for manual interventions
        self.needs_attention = False
        self.attention_reason = ""
        self.manual_input_required = False
        self.manual_input_prompt = ""
        self.manual_input_response = None
        
        # Configuration updates
        self.config_updates = {}
        self.selector_updates = {}
        
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """Register default signal handlers"""
        
        self.register_handler("pause", self._handle_pause)
        self.register_handler("resume", self._handle_resume)
        self.register_handler("stop", self._handle_stop)
        self.register_handler("update_config", self._handle_config_update)
        self.register_handler("update_selectors", self._handle_selector_update)
        self.register_handler("manual_intervention", self._handle_manual_intervention)
        self.register_handler("captcha_solved", self._handle_captcha_solved)
        self.register_handler("needs_attention", self._handle_needs_attention)
        self.register_handler("provide_input", self._handle_provide_input)
    
    def register_handler(self, signal_type: str, handler: Callable):
        """Register a signal handler"""
        self.signal_handlers[signal_type] = handler
        logger.debug(f"Registered handler for signal: {signal_type}")
    
    async def process_signal(self, signal_type: str, payload: Dict[str, Any]):
        """Process an incoming signal"""
        logger.info(f"Processing signal: {signal_type}")
        
        try:
            handler = self.signal_handlers.get(signal_type)
            if handler:
                await handler(payload)
                logger.info(f"Signal {signal_type} processed successfully")
            else:
                logger.warning(f"No handler registered for signal: {signal_type}")
                
        except Exception as e:
            logger.error(f"Error processing signal {signal_type}: {e}")
            raise
    
    async def _handle_pause(self, payload: Dict[str, Any]):
        """Handle pause signal"""
        reason = payload.get("reason", "Manual pause requested")
        logger.info(f"Pause signal received: {reason}")
        
        # Set global pause flag that execution engine will check
        with self.lock:
            self.needs_attention = True
            self.attention_reason = f"Paused: {reason}"
        
        # Notify any listeners
        await self._notify_status_change("paused", reason)
    
    async def _handle_resume(self, payload: Dict[str, Any]):
        """Handle resume signal"""
        logger.info("Resume signal received")
        
        with self.lock:
            self.needs_attention = False
            self.attention_reason = ""
            self.manual_input_required = False
        
        await self._notify_status_change("resumed", "Execution resumed")
    
    async def _handle_stop(self, payload: Dict[str, Any]):
        """Handle stop signal"""
        reason = payload.get("reason", "Manual stop requested")
        logger.info(f"Stop signal received: {reason}")
        
        with self.lock:
            self.needs_attention = True
            self.attention_reason = f"Stopped: {reason}"
        
        await self._notify_status_change("stopped", reason)
    
    async def _handle_config_update(self, payload: Dict[str, Any]):
        """Handle configuration update signal"""
        config_updates = payload.get("config", {})
        logger.info(f"Config update received: {list(config_updates.keys())}")
        
        with self.lock:
            self.config_updates.update(config_updates)
        
        # Apply configuration updates
        await self._apply_config_updates(config_updates)
    
    async def _handle_selector_update(self, payload: Dict[str, Any]):
        """Handle selector update signal"""
        selector_updates = payload.get("selectors", {})
        logger.info(f"Selector update received: {list(selector_updates.keys())}")
        
        with self.lock:
            self.selector_updates.update(selector_updates)
        
        # Write updated selectors to manifest file
        await self._update_selector_manifest(selector_updates)
    
    async def _handle_manual_intervention(self, payload: Dict[str, Any]):
        """Handle manual intervention request"""
        intervention_type = payload.get("type", "unknown")
        message = payload.get("message", "Manual intervention required")
        
        logger.info(f"Manual intervention requested: {intervention_type}")
        
        with self.lock:
            self.needs_attention = True
            self.attention_reason = f"Manual intervention: {message}"
            self.manual_input_required = payload.get("requires_input", False)
            self.manual_input_prompt = payload.get("input_prompt", "")
        
        await self._notify_status_change("needs_attention", message)
    
    async def _handle_captcha_solved(self, payload: Dict[str, Any]):
        """Handle CAPTCHA solved notification"""
        solution = payload.get("solution", "")
        logger.info("CAPTCHA solution received")
        
        # Store solution for execution engine to use
        with self.lock:
            self.manual_input_response = solution
            self.manual_input_required = False
        
        await self._notify_status_change("captcha_solved", "CAPTCHA solution provided")
    
    async def _handle_needs_attention(self, payload: Dict[str, Any]):
        """Handle needs attention signal from execution engine"""
        reason = payload.get("reason", "Attention required")
        requires_input = payload.get("requires_input", False)
        input_prompt = payload.get("input_prompt", "")
        
        logger.info(f"Needs attention: {reason}")
        
        with self.lock:
            self.needs_attention = True
            self.attention_reason = reason
            self.manual_input_required = requires_input
            self.manual_input_prompt = input_prompt
        
        # Send signal to Temporal workflow
        await self._send_temporal_signal("NeedsAttention", {
            "reason": reason,
            "requires_input": requires_input,
            "input_prompt": input_prompt,
            "timestamp": datetime.now().isoformat()
        })
    
    async def _handle_provide_input(self, payload: Dict[str, Any]):
        """Handle manual input provision"""
        input_value = payload.get("input", "")
        input_type = payload.get("type", "text")
        
        logger.info(f"Manual input received: {input_type}")
        
        with self.lock:
            self.manual_input_response = input_value
            self.manual_input_required = False
            self.needs_attention = False
            self.attention_reason = ""
        
        await self._notify_status_change("input_provided", "Manual input provided")
    
    async def _apply_config_updates(self, config_updates: Dict[str, Any]):
        """Apply configuration updates to running execution"""
        try:
            # Update timeout values
            if "timeouts" in config_updates:
                timeouts = config_updates["timeouts"]
                logger.info(f"Updating timeouts: {timeouts}")
                # Apply to execution engine
            
            # Update retry settings
            if "retries" in config_updates:
                retries = config_updates["retries"]
                logger.info(f"Updating retry settings: {retries}")
                # Apply to execution engine
            
            # Update proxy settings
            if "proxy" in config_updates:
                proxy_config = config_updates["proxy"]
                logger.info("Updating proxy configuration")
                # Apply to proxy manager
            
        except Exception as e:
            logger.error(f"Failed to apply config updates: {e}")
    
    async def _update_selector_manifest(self, selector_updates: Dict[str, Any]):
        """Update selector manifest file with new selectors"""
        try:
            import yaml
            from pathlib import Path
            
            # Load current manifest
            manifest_path = Path("/tmp/current_manifest.yml")
            if manifest_path.exists():
                with open(manifest_path, 'r') as f:
                    manifest = yaml.safe_load(f)
                
                # Update selectors
                if "selectors" not in manifest:
                    manifest["selectors"] = {}
                
                manifest["selectors"].update(selector_updates)
                
                # Write updated manifest
                with open(manifest_path, 'w') as f:
                    yaml.dump(manifest, f, default_flow_style=False, indent=2)
                
                logger.info(f"Updated manifest with {len(selector_updates)} selector changes")
            
        except Exception as e:
            logger.error(f"Failed to update selector manifest: {e}")
    
    async def _notify_status_change(self, status: str, message: str):
        """Notify status change to interested parties"""
        notification = {
            "status": status,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        
        # Log the status change
        logger.info(f"Status change: {status} - {message}")
        
        # Could send to external monitoring systems here
    
    async def _send_temporal_signal(self, signal_name: str, payload: Dict[str, Any]):
        """Send signal to Temporal workflow"""
        try:
            # This would integrate with Temporal client
            logger.info(f"Sending Temporal signal: {signal_name}")
            
            # For now, just log the signal
            logger.debug(f"Temporal signal payload: {json.dumps(payload, indent=2)}")
            
        except Exception as e:
            logger.error(f"Failed to send Temporal signal: {e}")
    
    def get_attention_status(self) -> Dict[str, Any]:
        """Get current attention status"""
        with self.lock:
            return {
                "needs_attention": self.needs_attention,
                "reason": self.attention_reason,
                "manual_input_required": self.manual_input_required,
                "input_prompt": self.manual_input_prompt,
                "has_response": self.manual_input_response is not None
            }
    
    def get_manual_input_response(self) -> Optional[str]:
        """Get and clear manual input response"""
        with self.lock:
            response = self.manual_input_response
            self.manual_input_response = None
            return response
    
    def clear_attention(self):
        """Clear attention flags"""
        with self.lock:
            self.needs_attention = False
            self.attention_reason = ""
            self.manual_input_required = False
            self.manual_input_prompt = ""
            self.manual_input_response = None
    
    def get_config_updates(self) -> Dict[str, Any]:
        """Get and clear pending configuration updates"""
        with self.lock:
            updates = self.config_updates.copy()
            self.config_updates.clear()
            return updates
    
    def get_selector_updates(self) -> Dict[str, Any]:
        """Get and clear pending selector updates"""
        with self.lock:
            updates = self.selector_updates.copy()
            self.selector_updates.clear()
            return updates
