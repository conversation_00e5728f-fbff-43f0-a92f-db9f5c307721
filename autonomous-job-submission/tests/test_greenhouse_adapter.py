"""
Unit tests for Greenhouse adapter with Playwright trace-viewer assets
Tests the complete job submission workflow with mock data
"""

import pytest
import asyncio
import json
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from playwright.async_api import async_playwright

from agent_core.adapters.greenhouse_adapter import GreenhouseAdapter
from adapter_sdk.base_adapter import UserProfile, SubmissionStatus

# Test data
TEST_JOB_URL = "https://boards.greenhouse.io/example/jobs/123456"
TEST_USER_PROFILE = UserProfile(
    personal_info={
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>",
        "email": "<EMAIL>",
        "phone": "******-0123",
        "address": "123 Main St",
        "city": "San Francisco",
        "state": "CA",
        "zip_code": "94105",
        "country": "United States"
    },
    resume_path="/tmp/test_resume.pdf",
    cover_letter_path="/tmp/test_cover_letter.pdf"
)

@pytest.fixture
async def greenhouse_adapter():
    """Create a Greenhouse adapter instance for testing"""
    config = {
        "email": "<EMAIL>",
        "password": "test_password",
        "captcha": {
            "service": "mock",
            "api_key": "test_key"
        }
    }
    
    adapter = GreenhouseAdapter(config)
    
    # Mock the page and context
    mock_page = AsyncMock()
    mock_context = AsyncMock()
    
    # Set up common page methods
    mock_page.goto = AsyncMock()
    mock_page.click = AsyncMock()
    mock_page.type = AsyncMock()
    mock_page.fill = AsyncMock()
    mock_page.select_option = AsyncMock()
    mock_page.set_input_files = AsyncMock()
    mock_page.wait_for_selector = AsyncMock()
    mock_page.wait_for_load_state = AsyncMock()
    mock_page.query_selector = AsyncMock()
    mock_page.query_selector_all = AsyncMock()
    mock_page.screenshot = AsyncMock()
    mock_page.content = AsyncMock()
    mock_page.title = AsyncMock()
    mock_page.url = TEST_JOB_URL
    
    adapter.set_page(mock_page, mock_context)
    
    return adapter

@pytest.fixture
def trace_context():
    """Create Playwright trace context for test recording"""
    return {
        "trace_dir": Path("/tmp/test_traces"),
        "test_name": "greenhouse_submission"
    }

class TestGreenhouseAdapter:
    """Test suite for Greenhouse adapter"""
    
    @pytest.mark.asyncio
    async def test_navigate_to_job(self, greenhouse_adapter):
        """Test navigation to job posting"""
        
        # Mock successful navigation
        greenhouse_adapter.page.goto.return_value = None
        greenhouse_adapter.page.url = TEST_JOB_URL
        
        await greenhouse_adapter.navigate_to_job(TEST_JOB_URL)
        
        # Verify navigation was called
        greenhouse_adapter.page.goto.assert_called_once_with(
            TEST_JOB_URL, 
            wait_until="networkidle"
        )
    
    @pytest.mark.asyncio
    async def test_navigate_invalid_url(self, greenhouse_adapter):
        """Test navigation with invalid URL"""
        
        invalid_url = "https://example.com/not-greenhouse"
        greenhouse_adapter.page.url = invalid_url
        
        with pytest.raises(Exception, match="Invalid Greenhouse job URL"):
            await greenhouse_adapter.navigate_to_job(invalid_url)
    
    @pytest.mark.asyncio
    async def test_login_not_required(self, greenhouse_adapter):
        """Test when login is not required"""
        
        # Mock no login button found
        greenhouse_adapter.page.query_selector.return_value = None
        
        result = await greenhouse_adapter.login()
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_login_required_success(self, greenhouse_adapter):
        """Test successful login flow"""
        
        # Mock login button found
        mock_login_button = AsyncMock()
        greenhouse_adapter.page.query_selector.side_effect = [
            mock_login_button,  # Login button exists
            None  # Login button gone after login
        ]
        
        result = await greenhouse_adapter.login()
        
        assert result is True
        
        # Verify login steps
        greenhouse_adapter.page.click.assert_called()
        assert greenhouse_adapter.page.type.call_count == 2  # Email and password
    
    @pytest.mark.asyncio
    async def test_login_no_credentials(self, greenhouse_adapter):
        """Test login failure when no credentials provided"""
        
        # Remove credentials from config
        greenhouse_adapter.config.pop('email', None)
        greenhouse_adapter.config.pop('password', None)
        
        # Mock login button found
        mock_login_button = AsyncMock()
        greenhouse_adapter.page.query_selector.return_value = mock_login_button
        
        result = await greenhouse_adapter.login()
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_start_application_success(self, greenhouse_adapter):
        """Test starting application process"""
        
        # Mock apply button found
        greenhouse_adapter.page.wait_for_selector.return_value = True
        greenhouse_adapter.page.query_selector.return_value = AsyncMock()
        
        await greenhouse_adapter.start_application()
        
        # Verify apply button was clicked
        greenhouse_adapter.page.click.assert_called()
    
    @pytest.mark.asyncio
    async def test_start_application_no_button(self, greenhouse_adapter):
        """Test application start when apply button not found"""
        
        # Mock apply button not found
        greenhouse_adapter.page.wait_for_selector.return_value = False
        
        with pytest.raises(Exception, match="Apply button not found"):
            await greenhouse_adapter.start_application()
    
    @pytest.mark.asyncio
    async def test_fill_form_personal_info(self, greenhouse_adapter):
        """Test filling personal information"""
        
        # Mock form fields exist
        greenhouse_adapter.page.query_selector.return_value = AsyncMock()
        
        # Mock form structure analysis
        greenhouse_adapter.total_pages = 1
        greenhouse_adapter.current_page_index = 0
        
        await greenhouse_adapter.fill_form(TEST_USER_PROFILE)
        
        # Verify personal info fields were filled
        assert greenhouse_adapter.page.type.call_count >= 5  # At least 5 personal fields
    
    @pytest.mark.asyncio
    async def test_upload_documents_success(self, greenhouse_adapter):
        """Test document upload"""
        
        # Mock upload fields exist
        greenhouse_adapter.page.query_selector.return_value = AsyncMock()
        
        await greenhouse_adapter.upload_documents(TEST_USER_PROFILE)
        
        # Verify file uploads
        assert greenhouse_adapter.page.set_input_files.call_count >= 1
    
    @pytest.mark.asyncio
    async def test_submit_form_success(self, greenhouse_adapter):
        """Test successful form submission"""
        
        # Mock submit button found and no errors
        greenhouse_adapter.page.wait_for_selector.return_value = True
        greenhouse_adapter.page.query_selector.return_value = None  # No errors
        
        result = await greenhouse_adapter.submit_form()
        
        assert result.status == SubmissionStatus.SUCCESS
        greenhouse_adapter.page.click.assert_called()  # Submit button clicked
    
    @pytest.mark.asyncio
    async def test_submit_form_error(self, greenhouse_adapter):
        """Test form submission with error"""
        
        # Mock submit button found but error present
        greenhouse_adapter.page.wait_for_selector.return_value = True
        
        mock_error_element = AsyncMock()
        mock_error_element.text_content.return_value = "Validation error"
        greenhouse_adapter.page.query_selector.return_value = mock_error_element
        
        result = await greenhouse_adapter.submit_form()
        
        assert result.status == SubmissionStatus.FAILED
        assert "Validation error" in result.message
    
    @pytest.mark.asyncio
    async def test_verify_submission_success(self, greenhouse_adapter):
        """Test successful submission verification"""
        
        # Mock success indicator found
        greenhouse_adapter.page.query_selector.return_value = AsyncMock()
        
        result = await greenhouse_adapter.verify_submission()
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_verify_submission_by_url(self, greenhouse_adapter):
        """Test submission verification by URL"""
        
        # Mock no success indicators but confirmation URL
        greenhouse_adapter.page.query_selector.return_value = None
        greenhouse_adapter.page.url = "https://boards.greenhouse.io/example/thank-you"
        
        result = await greenhouse_adapter.verify_submission()
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_verify_submission_by_content(self, greenhouse_adapter):
        """Test submission verification by page content"""
        
        # Mock no success indicators or URL but success content
        greenhouse_adapter.page.query_selector.return_value = None
        greenhouse_adapter.page.url = TEST_JOB_URL
        greenhouse_adapter.page.content.return_value = "<html><body>Thank you for your application</body></html>"
        
        result = await greenhouse_adapter.verify_submission()
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_complete_submission_workflow(self, greenhouse_adapter, trace_context):
        """Test complete end-to-end submission workflow"""
        
        # Set up mocks for successful workflow
        greenhouse_adapter.page.query_selector.side_effect = [
            None,  # No login required
            AsyncMock(),  # Apply button found
            AsyncMock(),  # Form found
            AsyncMock(),  # Submit button found
            None,  # No errors
            AsyncMock(),  # Success indicator found
        ]
        
        greenhouse_adapter.page.wait_for_selector.return_value = True
        greenhouse_adapter.page.content.return_value = "<html><body>Application form</body></html>"
        
        # Mock form structure
        greenhouse_adapter.total_pages = 1
        
        # Start trace recording
        trace_dir = trace_context["trace_dir"]
        trace_dir.mkdir(exist_ok=True)
        
        with patch('time.time', return_value=1234567890):
            result = await greenhouse_adapter.submit_application(
                TEST_JOB_URL, 
                TEST_USER_PROFILE
            )
        
        # Verify successful completion
        assert result.status == SubmissionStatus.SUCCESS
        assert result.duration > 0
        
        # Verify all major steps were called
        greenhouse_adapter.page.goto.assert_called()
        greenhouse_adapter.page.click.assert_called()
        greenhouse_adapter.page.type.assert_called()
    
    @pytest.mark.asyncio
    async def test_submission_with_captcha(self, greenhouse_adapter):
        """Test submission workflow with CAPTCHA"""
        
        # Mock CAPTCHA present
        mock_captcha = AsyncMock()
        greenhouse_adapter.page.query_selector.side_effect = [
            None,  # No login required
            AsyncMock(),  # Apply button found
            AsyncMock(),  # Form found
            mock_captcha,  # CAPTCHA found
            None,  # CAPTCHA solved (gone)
            AsyncMock(),  # Submit button found
            None,  # No errors
            AsyncMock(),  # Success indicator found
        ]
        
        greenhouse_adapter.page.wait_for_selector.return_value = True
        
        # Mock CAPTCHA handler
        with patch.object(greenhouse_adapter.captcha_handler, 'solve_captcha', return_value=True):
            result = await greenhouse_adapter.submit_application(
                TEST_JOB_URL, 
                TEST_USER_PROFILE
            )
        
        assert result.status == SubmissionStatus.SUCCESS
    
    @pytest.mark.asyncio
    async def test_submission_failure_recovery(self, greenhouse_adapter):
        """Test submission failure and error handling"""
        
        # Mock failure during form filling
        greenhouse_adapter.page.query_selector.side_effect = [
            None,  # No login required
            AsyncMock(),  # Apply button found
            None,  # Form not found - should cause failure
        ]
        
        greenhouse_adapter.page.wait_for_selector.side_effect = [
            True,  # Apply button wait succeeds
            False,  # Form wait fails
        ]
        
        result = await greenhouse_adapter.submit_application(
            TEST_JOB_URL, 
            TEST_USER_PROFILE
        )
        
        assert result.status == SubmissionStatus.FAILED
        assert result.error_details is not None
        assert len(result.screenshots) > 0  # Error screenshot taken

@pytest.mark.integration
class TestGreenhouseIntegration:
    """Integration tests with real Playwright browser"""
    
    @pytest.mark.asyncio
    async def test_real_browser_navigation(self):
        """Test navigation with real browser (requires network)"""
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            
            # Start tracing
            await context.tracing.start(screenshots=True, snapshots=True)
            
            page = await context.new_page()
            
            # Test navigation to a real Greenhouse page
            test_url = "https://boards.greenhouse.io/embed/job_board?for=example"
            
            try:
                await page.goto(test_url, timeout=10000)
                
                # Verify page loaded
                title = await page.title()
                assert "greenhouse" in title.lower() or "jobs" in title.lower()
                
                # Take screenshot
                await page.screenshot(path="/tmp/greenhouse_test.png")
                
            except Exception as e:
                pytest.skip(f"Network test skipped: {e}")
            
            finally:
                # Stop tracing and save
                await context.tracing.stop(path="/tmp/greenhouse_trace.zip")
                await browser.close()

def test_adapter_configuration():
    """Test adapter configuration and manifest loading"""
    
    adapter = GreenhouseAdapter()
    
    # Verify manifest loaded correctly
    assert adapter.manifest.portal_name == "Greenhouse"
    assert adapter.manifest.base_url == "https://boards.greenhouse.io"
    
    # Verify selectors are loaded
    assert adapter.manifest.selectors.apply_button is not None
    assert adapter.manifest.selectors.first_name is not None
    assert adapter.manifest.selectors.email is not None
    
    # Verify strategies
    assert adapter.manifest.strategies.form_strategy == "multi_step"
    assert adapter.manifest.strategies.login_strategy == "form_based"
    
    # Verify features
    assert adapter.manifest.features.supports_resume_upload is True
    assert adapter.manifest.features.supports_cover_letter is True

def test_user_profile_validation():
    """Test user profile data validation"""
    
    # Test valid profile
    profile = TEST_USER_PROFILE
    assert profile.personal_info["first_name"] == "John"
    assert profile.resume_path.endswith(".pdf")
    
    # Test missing required fields
    incomplete_profile = UserProfile(
        personal_info={"first_name": "John"},  # Missing other required fields
        resume_path="/tmp/resume.pdf"
    )
    
    # Adapter should handle missing fields gracefully
    adapter = GreenhouseAdapter()
    assert adapter.manifest is not None

if __name__ == "__main__":
    # Run tests with Playwright trace collection
    pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "--capture=no",
        "-k", "not integration"  # Skip integration tests by default
    ])
