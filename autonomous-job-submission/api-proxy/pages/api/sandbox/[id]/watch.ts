/**
 * Next.js API route for sandbox terminal WebSocket authentication
 * Verifies user auth, generates JWT, and redirects to WebSocket endpoint
 */

import { NextApiRequest, NextApiResponse } from 'next'
import jwt from 'jsonwebtoken'
import { getSession } from 'next-auth/react'
import { z } from 'zod'

// Validation schemas
const WatchRequestSchema = z.object({
  id: z.string().min(1, 'Sandbox ID is required'),
})

const UserRoleSchema = z.enum(['Basic', 'Advanced', 'Admin'])

interface SandboxSession {
  sandboxId: string
  userId: string
  userRole: string
  userIP: string
  podIP: string
  terminalPort: number
  createdAt: Date
  expiresAt: Date
}

interface JWTPayload {
  user_id: string
  sandbox_id: string
  user_ip: string
  role: string
  iat: number
  exp: number
  iss: string
  sub: string
  jti: string
}

// In-memory session store (in production, use Redis)
const activeSessions = new Map<string, SandboxSession>()

// Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'cvleap-terminal-secret'
const JWT_EXPIRY = 60 // 60 seconds
const TERMINAL_PORT = 7000

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Validate request parameters
    const { id: sandboxId } = WatchRequestSchema.parse(req.query)

    // Get user session
    const session = await getSession({ req })
    if (!session?.user) {
      return res.status(401).json({ error: 'Authentication required' })
    }

    const userId = session.user.id || session.user.email
    const userRole = UserRoleSchema.parse(session.user.role || 'Basic')

    // Get user IP address
    const userIP = getUserIP(req)

    // Verify user has permission to access this sandbox
    const hasPermission = await verifyUserPermission(userId, sandboxId, userRole)
    if (!hasPermission) {
      return res.status(403).json({ error: 'Access denied to this sandbox' })
    }

    // Get sandbox pod information
    const sandboxInfo = await getSandboxInfo(sandboxId)
    if (!sandboxInfo) {
      return res.status(404).json({ error: 'Sandbox not found' })
    }

    // Check if terminal is enabled for this sandbox
    if (!sandboxInfo.terminalEnabled) {
      return res.status(400).json({ 
        error: 'Terminal not enabled for this sandbox',
        hint: 'Set ENABLE_TERMINAL=1 to enable terminal access'
      })
    }

    // Generate single-use JWT token
    const tokenPayload: JWTPayload = {
      user_id: userId,
      sandbox_id: sandboxId,
      user_ip: userIP,
      role: userRole,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + JWT_EXPIRY,
      iss: 'cvleap-api-proxy',
      sub: userId,
      jti: `${sandboxId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }

    const token = jwt.sign(tokenPayload, JWT_SECRET, { algorithm: 'HS256' })

    // Store session for tracking
    const sessionData: SandboxSession = {
      sandboxId,
      userId,
      userRole,
      userIP,
      podIP: sandboxInfo.podIP,
      terminalPort: TERMINAL_PORT,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + JWT_EXPIRY * 1000)
    }

    activeSessions.set(token, sessionData)

    // Clean up expired sessions
    cleanupExpiredSessions()

    // Log access attempt
    console.log(`Terminal access granted: user=${userId}, sandbox=${sandboxId}, role=${userRole}, ip=${userIP}`)

    // Construct WebSocket URL
    const wsUrl = `wss://${sandboxInfo.podIP}:${TERMINAL_PORT}/ws`

    // Return WebSocket connection details
    return res.status(200).json({
      success: true,
      websocket: {
        url: wsUrl,
        protocol: token, // JWT token goes in Sec-WebSocket-Protocol header
        timeout: JWT_EXPIRY * 1000,
        reconnect: false // Single-use token
      },
      session: {
        sandbox_id: sandboxId,
        user_id: userId,
        role: userRole,
        expires_at: sessionData.expiresAt.toISOString(),
        terminal_enabled: true
      },
      instructions: {
        connect: 'Use the provided WebSocket URL with the token in Sec-WebSocket-Protocol header',
        hotkeys: {
          'Cmd+K': 'Capture current terminal content',
          'Cmd+C': 'Send interrupt signal',
          'Cmd+D': 'Send EOF signal'
        }
      }
    })

  } catch (error) {
    console.error('Terminal watch API error:', error)

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Invalid request parameters',
        details: error.errors
      })
    }

    return res.status(500).json({
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

/**
 * Extract user IP address from request
 */
function getUserIP(req: NextApiRequest): string {
  const forwarded = req.headers['x-forwarded-for']
  const realIP = req.headers['x-real-ip']
  const remoteAddress = req.socket.remoteAddress

  if (typeof forwarded === 'string') {
    return forwarded.split(',')[0].trim()
  }

  if (typeof realIP === 'string') {
    return realIP
  }

  return remoteAddress || 'unknown'
}

/**
 * Verify user has permission to access the sandbox
 */
async function verifyUserPermission(
  userId: string,
  sandboxId: string,
  userRole: string
): Promise<boolean> {
  try {
    // Admin users can access any sandbox
    if (userRole === 'Admin') {
      return true
    }

    // Check if user owns this sandbox or has been granted access
    const response = await fetch(`${process.env.INTERNAL_API_URL}/api/sandboxes/${sandboxId}/permissions`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${process.env.INTERNAL_API_TOKEN}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      return false
    }

    const permissions = await response.json()
    
    // Check if user is owner or has explicit access
    return permissions.owner_id === userId || 
           permissions.allowed_users?.includes(userId) ||
           permissions.allowed_roles?.includes(userRole)

  } catch (error) {
    console.error('Permission check failed:', error)
    return false
  }
}

/**
 * Get sandbox information from Kubernetes API
 */
async function getSandboxInfo(sandboxId: string): Promise<{
  podIP: string
  terminalEnabled: boolean
  status: string
} | null> {
  try {
    // Query Kubernetes API for sandbox pod
    const response = await fetch(`${process.env.K8S_API_URL}/api/v1/namespaces/cvleap/pods`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${process.env.K8S_API_TOKEN}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`Kubernetes API error: ${response.status}`)
    }

    const data = await response.json()
    
    // Find pod with matching sandbox ID
    const pod = data.items.find((pod: any) => 
      pod.metadata.labels?.['sandbox-id'] === sandboxId
    )

    if (!pod) {
      return null
    }

    // Extract pod information
    const podIP = pod.status.podIP
    const terminalEnabled = pod.spec.containers[0].env?.find(
      (env: any) => env.name === 'ENABLE_TERMINAL'
    )?.value === '1'

    return {
      podIP,
      terminalEnabled,
      status: pod.status.phase
    }

  } catch (error) {
    console.error('Failed to get sandbox info:', error)
    return null
  }
}

/**
 * Clean up expired sessions
 */
function cleanupExpiredSessions(): void {
  const now = new Date()
  
  for (const [token, session] of activeSessions.entries()) {
    if (session.expiresAt < now) {
      activeSessions.delete(token)
    }
  }
}

/**
 * Get active sessions (for monitoring)
 */
export function getActiveSessions(): SandboxSession[] {
  cleanupExpiredSessions()
  return Array.from(activeSessions.values())
}

/**
 * Revoke a specific session
 */
export function revokeSession(token: string): boolean {
  return activeSessions.delete(token)
}
