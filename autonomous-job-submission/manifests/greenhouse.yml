portal:
  name: "Greenhouse"
  base_url: "https://boards.greenhouse.io"
  version: "1.0.0"
  description: "Greenhouse job application portal adapter"

selectors:
  # Authentication (if required)
  login_button: "a[href*='login'], .login-link"
  email_field: "input[name='email'], input[type='email']"
  password_field: "input[name='password'], input[type='password']"
  login_submit: "button[type='submit'], input[type='submit']"
  login_indicators:
    - ".login-form"
    - "[data-testid='login']"
    - "a[href*='login']"

  # Application process
  apply_button: "a[data-mapped='true'], .application-link, a[href*='apply']"
  application_form: ".application-form, form[data-form-id]"
  next_button: "button[data-source='continue'], .continue-button"
  previous_button: "button[data-source='back'], .back-button"
  submit_button: "button[data-source='submit'], input[value*='Submit']"

  # Personal information
  first_name: "input[name*='first_name'], input[id*='first_name']"
  last_name: "input[name*='last_name'], input[id*='last_name']"
  email: "input[name*='email'], input[type='email']"
  phone: "input[name*='phone'], input[type='tel']"
  address: "input[name*='address'], textarea[name*='address']"
  city: "input[name*='city']"
  state: "select[name*='state'], input[name*='state']"
  zip_code: "input[name*='zip'], input[name*='postal']"
  country: "select[name*='country']"

  # Professional information
  current_company: "input[name*='company'], input[name*='employer']"
  current_title: "input[name*='title'], input[name*='position']"
  experience_years: "select[name*='experience'], input[name*='years']"
  salary_expectation: "input[name*='salary'], input[name*='compensation']"
  availability_date: "input[name*='start'], input[type='date']"

  # File uploads
  resume_upload: "input[name*='resume'], input[accept*='pdf']"
  cover_letter_upload: "input[name*='cover'], input[name*='letter']"
  portfolio_upload: "input[name*='portfolio'], input[name*='work']"
  additional_documents: "input[name*='document'], input[name*='attachment']"

  # Dynamic questions
  text_questions:
    - "textarea[name*='question']"
    - "input[type='text'][name*='custom']"
    - ".question-field textarea"
  dropdown_questions:
    - "select[name*='question']"
    - "select[name*='custom']"
    - ".question-field select"
  checkbox_questions:
    - "input[type='checkbox'][name*='question']"
    - "input[type='checkbox'][name*='custom']"
    - ".question-field input[type='checkbox']"
  radio_questions:
    - "input[type='radio'][name*='question']"
    - "input[type='radio'][name*='custom']"
    - ".question-field input[type='radio']"

  # Verification and confirmation
  success_indicators:
    - ".application-submitted"
    - ".thank-you-message"
    - "h1:contains('Thank you')"
    - ".confirmation-page"
  error_indicators:
    - ".error-message"
    - ".alert-danger"
    - ".field-error"
    - "[data-error='true']"
  confirmation_page: ".application-confirmation, .thank-you-page"
  application_id: ".application-id, .confirmation-number"

  # CAPTCHA and security
  captcha:
    - "iframe[src*='captcha']"
    - ".captcha-container"
    - "#captcha"
  recaptcha: "iframe[src*='recaptcha']"
  security_questions:
    - "input[name*='security']"
    - ".security-question"

  # Navigation
  pagination_next: ".pagination .next, button[data-source='next']"
  pagination_previous: ".pagination .previous, button[data-source='previous']"
  breadcrumbs: ".breadcrumb, .progress-indicator"

strategies:
  login_strategy: "form_based"
  form_strategy: "multi_step"
  upload_strategy: "direct"
  captcha_strategy: "2captcha"
  verification_strategy: "element_presence"
  
  # Timing
  page_load_wait: 5000
  element_wait: 10000
  form_submit_wait: 15000
  upload_wait: 30000
  
  # Retries
  max_retries: 3
  retry_delay: 2000
  exponential_backoff: true

features:
  supports_login: true
  supports_guest_application: true
  supports_resume_upload: true
  supports_cover_letter: true
  supports_portfolio: false
  supports_salary_info: true
  supports_availability: true
  supports_custom_questions: true
  supports_bulk_upload: false
  supports_application_tracking: true

custom_config:
  # Greenhouse-specific settings
  api_version: "v1"
  rate_limit: 60  # requests per minute
  session_timeout: 1800  # 30 minutes
  
  # Form handling
  auto_save_interval: 30  # seconds
  validation_delay: 500   # milliseconds
  
  # File upload limits
  max_file_size: 10485760  # 10MB
  allowed_file_types:
    - "pdf"
    - "doc"
    - "docx"
    - "txt"

field_mappings:
  # Map common field names to Greenhouse-specific names
  "phone_number": "phone"
  "mobile_phone": "phone"
  "home_address": "address"
  "current_position": "current_title"
  "years_of_experience": "experience_years"
  "expected_salary": "salary_expectation"
  "start_date": "availability_date"

question_patterns:
  # Patterns for AI-powered question answering
  "why_interested": "I am interested in this position because"
  "why_company": "I want to work for this company because"
  "greatest_strength": "My greatest strength is"
  "career_goals": "My career goals include"
  "availability": "I am available to start"
  "salary_range": "My salary expectation is"
  "relocation": "I am willing to relocate"
  "remote_work": "I am comfortable with remote work"
  "travel": "I am available for travel"
  "overtime": "I am available for overtime work"
  "background_check": "I consent to a background check"
  "drug_test": "I consent to drug testing"
  "references": "I can provide professional references"
