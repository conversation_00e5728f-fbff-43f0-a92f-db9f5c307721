# 🚀 CVLeap Job Submission - Quick Start Guide

Get the autonomous job submission system running in under 5 minutes!

## Prerequisites

- **Node.js 18+** and **npm**
- **Python 3.12+**
- **Go 1.21+**
- **tmux**, **curl**, **jq**

## 🛠️ One-Command Setup

```bash
# Setup everything automatically
./scripts/setup-dev.sh
```

This script will:
- ✅ Install missing dependencies (macOS/Linux)
- ✅ Create Python virtual environment
- ✅ Install all Python packages
- ✅ Install Playwright browsers
- ✅ Build Go terminald daemon
- ✅ Create environment configuration
- ✅ Run system tests

## 🚀 Start the System

```bash
# Start both backend and frontend
./scripts/start-all.sh
```

**Or start services individually:**

```bash
# Backend only (terminald + FastAPI + agent)
./scripts/start-backend.sh

# Frontend only (React + API proxy)
./scripts/start-frontend.sh
```

## 🌐 Access the Application

Once started, open your browser to:

**🎨 Main Application:** http://localhost:3000

**📊 Backend API:** http://localhost:8080

**🖥️ Terminal Access:** `tmux attach-session -t cvleap`

## 🧪 Test the System

```bash
# Run system tests
./scripts/test-system.sh

# Test backend health
curl http://localhost:8080/ready

# Test frontend
curl http://localhost:3000
```

## ⚙️ Configuration

Edit `.env` file for custom settings:

```bash
# Core ports
FRONTEND_PORT=3000
SIDECAR_PORT=8080
TERMINAL_PORT=7000

# Enable/disable terminal
ENABLE_TERMINAL=1

# Add your API keys
OPENAI_API_KEY=your_key_here
BRIGHTDATA_USERNAME=your_username
BRIGHTDATA_PASSWORD=your_password
```

## 🎯 Quick Demo

1. **Open** http://localhost:3000
2. **Enter** a sandbox ID (e.g., `demo-sandbox-123`)
3. **Click** "Show Terminal" to see live terminal
4. **Watch** real-time job submission automation

## 📊 Service Overview

| Service | Port | Purpose |
|---------|------|---------|
| React Frontend | 3000 | Main UI with terminal integration |
| API Proxy | 3001 | WebSocket authentication |
| FastAPI Sidecar | 8080 | Status, metrics, health checks |
| terminald | 7000 | WebSocket terminal daemon |
| tmux Session | - | Live terminal session |

## 🔧 Development Workflow

1. **Make changes** to code
2. **Services auto-reload** (frontend/backend)
3. **Check logs** in `logs/` directory
4. **Test changes** with test scripts

## 📁 Project Structure

```
autonomous-job-submission/
├── scripts/           # 🛠️ Development scripts
│   ├── setup-dev.sh   # One-command setup
│   ├── start-all.sh   # Start everything
│   ├── start-backend.sh
│   └── start-frontend.sh
├── terminald/         # 🔌 Go WebSocket daemon
├── agent-core/        # 🤖 Python LangChain agent
├── sidecar/          # 🔧 FastAPI endpoints
├── frontend/         # 🎨 React terminal UI
├── api-proxy/        # 🔐 Authentication proxy
├── manifests/        # ⚙️ Portal configurations
├── logs/             # 📝 Application logs
└── .env              # 🔧 Environment config
```

## 🛑 Stop Services

Press **Ctrl+C** in the terminal running the services, or:

```bash
# Kill all related processes
pkill -f "cvleap\|terminald\|job-submission"
tmux kill-session -t cvleap
```

## 🐛 Troubleshooting

### Port Already in Use
```bash
# Change ports in .env file
export FRONTEND_PORT=3001
export SIDECAR_PORT=8081
```

### Python Issues
```bash
# Recreate virtual environment
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r agent-core/requirements.txt
```

### Go Build Issues
```bash
cd terminald
go mod tidy
go build -o terminald .
```

### Permission Issues
```bash
chmod +x scripts/*.sh
```

### Missing Dependencies
```bash
# macOS
brew install node python@3.12 go tmux curl jq

# Ubuntu/Debian
sudo apt install nodejs npm python3.12 golang-go tmux curl jq

# CentOS/RHEL
sudo yum install nodejs npm python3.12 golang tmux curl jq
```

## 📚 More Information

- **Full Documentation:** [README.md](README.md)
- **Development Guide:** [DEV_SETUP.md](DEV_SETUP.md)
- **API Documentation:** Check `/rpc/status` endpoint
- **Security Guide:** [security/](security/)

## 🆘 Getting Help

1. **Check logs** in `logs/` directory
2. **Run tests** with `./scripts/test-system.sh`
3. **Verify health** with `curl http://localhost:8080/ready`
4. **Check tmux session** with `tmux attach-session -t cvleap`

## 🎉 Success!

If you see:
- ✅ Frontend at http://localhost:3000
- ✅ Backend health check passes
- ✅ Terminal shows live output
- ✅ No errors in logs

**You're ready to start automating job submissions!** 🚀

---

**Next Steps:**
- Configure job portal adapters in `manifests/`
- Add your API keys to `.env`
- Test with real job postings
- Deploy to production with Kubernetes manifests
