"""
Command execution engine for autonomous job submission
Executes LangChain-generated plans using Playwright automation
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontext
from playwright.async_api import TimeoutError as PlaywrightTimeoutError

from .planner import ExecutionPlan, PlanStep, ActionType

logger = logging.getLogger(__name__)

class ExecutionStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    PAUSED = "paused"
    CANCELLED = "cancelled"

@dataclass
class ExecutionResult:
    status: ExecutionStatus
    step_index: int
    message: str
    screenshot_path: Optional[str] = None
    error: Optional[str] = None
    duration: float = 0.0
    artifacts: Dict[str, Any] = None

@dataclass
class ExecutionContext:
    plan: ExecutionPlan
    page: Page
    browser: Browser
    context: BrowserContext
    user_data: Dict[str, Any]
    progress_callback: Optional[Callable] = None
    checkpoint_callback: Optional[Callable] = None

class JobSubmissionExecutor:
    """Executes job submission plans using Playwright automation"""
    
    def __init__(self, 
                 headless: bool = True,
                 slow_mo: int = 100,
                 timeout: int = 30000,
                 viewport: Dict[str, int] = None):
        self.headless = headless
        self.slow_mo = slow_mo
        self.timeout = timeout
        self.viewport = viewport or {"width": 1920, "height": 1080}
        
        self.current_execution: Optional[ExecutionContext] = None
        self.is_paused = False
        self.should_stop = False
        
        # Action handlers mapping
        self.action_handlers = {
            ActionType.NAVIGATE: self._handle_navigate,
            ActionType.CLICK: self._handle_click,
            ActionType.TYPE: self._handle_type,
            ActionType.WAIT: self._handle_wait,
            ActionType.SCROLL: self._handle_scroll,
            ActionType.UPLOAD: self._handle_upload,
            ActionType.SELECT: self._handle_select,
            ActionType.VERIFY: self._handle_verify,
            ActionType.CAPTCHA: self._handle_captcha,
            ActionType.CHECKPOINT: self._handle_checkpoint,
        }
    
    async def execute_plan(self, 
                          plan: ExecutionPlan,
                          user_data: Dict[str, Any],
                          progress_callback: Optional[Callable] = None,
                          checkpoint_callback: Optional[Callable] = None) -> List[ExecutionResult]:
        """Execute a complete job submission plan"""
        
        logger.info(f"Starting execution of plan for {plan.portal_name}")
        
        results = []
        
        async with async_playwright() as p:
            # Launch browser with stealth configuration
            browser = await p.chromium.launch(
                headless=self.headless,
                slow_mo=self.slow_mo,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            )
            
            # Create context with realistic user agent
            context = await browser.new_context(
                viewport=self.viewport,
                user_agent='Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            # Enable request interception for HAR recording
            await context.route("**/*", self._intercept_request)
            
            page = await context.new_page()
            
            # Set default timeout
            page.set_default_timeout(self.timeout)
            
            # Create execution context
            self.current_execution = ExecutionContext(
                plan=plan,
                page=page,
                browser=browser,
                context=context,
                user_data=user_data,
                progress_callback=progress_callback,
                checkpoint_callback=checkpoint_callback
            )
            
            try:
                # Execute each step in the plan
                for i, step in enumerate(plan.steps):
                    if self.should_stop:
                        break
                    
                    # Handle pause state
                    while self.is_paused and not self.should_stop:
                        await asyncio.sleep(0.1)
                    
                    logger.info(f"[STEP {i+1}/{len(plan.steps)}] {step.description}")
                    print(f"[STEP {i+1}/{len(plan.steps)}] {step.description}")
                    
                    result = await self._execute_step(step, i)
                    results.append(result)
                    
                    # Report progress
                    if progress_callback:
                        progress_callback(i + 1, len(plan.steps), result)
                    
                    # Stop on failure unless it's a non-critical step
                    if result.status == ExecutionStatus.FAILED and step.action != ActionType.VERIFY:
                        logger.error(f"Step {i+1} failed: {result.error}")
                        break
                
                # Take final screenshot
                screenshot_path = f"/tmp/final_screenshot_{int(time.time())}.png"
                await page.screenshot(path=screenshot_path, full_page=True)
                
                logger.info(f"Execution completed with {len(results)} steps")
                
            except Exception as e:
                logger.error(f"Execution failed with exception: {e}")
                results.append(ExecutionResult(
                    status=ExecutionStatus.FAILED,
                    step_index=-1,
                    message="Execution failed with exception",
                    error=str(e)
                ))
            
            finally:
                await browser.close()
                self.current_execution = None
        
        return results
    
    async def _execute_step(self, step: PlanStep, step_index: int) -> ExecutionResult:
        """Execute a single plan step"""
        
        start_time = time.time()
        
        try:
            # Get the appropriate handler for this action type
            handler = self.action_handlers.get(step.action)
            if not handler:
                raise ValueError(f"No handler for action type: {step.action}")
            
            # Execute the step with retry logic
            for attempt in range(step.retry_count):
                try:
                    await handler(step)
                    
                    # Verify step completion if verification is specified
                    if step.verification:
                        await self._verify_step_completion(step)
                    
                    duration = time.time() - start_time
                    
                    return ExecutionResult(
                        status=ExecutionStatus.SUCCESS,
                        step_index=step_index,
                        message=f"Step completed successfully",
                        duration=duration
                    )
                    
                except Exception as e:
                    logger.warning(f"Step attempt {attempt + 1} failed: {e}")
                    if attempt == step.retry_count - 1:
                        raise e
                    await asyncio.sleep(1)  # Brief delay before retry
            
        except Exception as e:
            duration = time.time() - start_time
            
            # Take screenshot on failure
            screenshot_path = f"/tmp/error_screenshot_{step_index}_{int(time.time())}.png"
            try:
                await self.current_execution.page.screenshot(path=screenshot_path)
            except:
                screenshot_path = None
            
            return ExecutionResult(
                status=ExecutionStatus.FAILED,
                step_index=step_index,
                message=f"Step failed after {step.retry_count} attempts",
                error=str(e),
                duration=duration,
                screenshot_path=screenshot_path
            )
    
    async def _handle_navigate(self, step: PlanStep):
        """Handle navigation action"""
        logger.info(f"Navigating to: {step.target}")
        await self.current_execution.page.goto(step.target, wait_until="networkidle")
    
    async def _handle_click(self, step: PlanStep):
        """Handle click action"""
        logger.info(f"Clicking element: {step.target}")
        await self.current_execution.page.click(step.target, timeout=step.timeout * 1000)
    
    async def _handle_type(self, step: PlanStep):
        """Handle typing action"""
        logger.info(f"Typing into element: {step.target}")
        
        # Clear field first
        await self.current_execution.page.fill(step.target, "")
        
        # Type the value
        if step.value:
            await self.current_execution.page.type(step.target, step.value, delay=50)
    
    async def _handle_wait(self, step: PlanStep):
        """Handle wait action"""
        logger.info(f"Waiting for: {step.target}")
        
        if step.target == "network_idle":
            await self.current_execution.page.wait_for_load_state("networkidle")
        elif step.target == "page_load":
            await self.current_execution.page.wait_for_load_state("load")
        elif step.target.isdigit():
            await asyncio.sleep(int(step.target))
        else:
            # Wait for element
            await self.current_execution.page.wait_for_selector(step.target, timeout=step.timeout * 1000)
    
    async def _handle_scroll(self, step: PlanStep):
        """Handle scroll action"""
        logger.info(f"Scrolling: {step.target}")
        
        if step.target == "bottom":
            await self.current_execution.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        elif step.target == "top":
            await self.current_execution.page.evaluate("window.scrollTo(0, 0)")
        else:
            # Scroll to element
            await self.current_execution.page.locator(step.target).scroll_into_view_if_needed()
    
    async def _handle_upload(self, step: PlanStep):
        """Handle file upload action"""
        logger.info(f"Uploading file to: {step.target}")
        
        if step.value and step.value in self.current_execution.user_data:
            file_path = self.current_execution.user_data[step.value]
            await self.current_execution.page.set_input_files(step.target, file_path)
    
    async def _handle_select(self, step: PlanStep):
        """Handle select dropdown action"""
        logger.info(f"Selecting option in: {step.target}")
        
        if step.value:
            await self.current_execution.page.select_option(step.target, step.value)
    
    async def _handle_verify(self, step: PlanStep):
        """Handle verification action"""
        logger.info(f"Verifying: {step.target}")
        
        if step.verification:
            await self._verify_condition(step.verification)
        else:
            # Default verification - check if element exists
            await self.current_execution.page.wait_for_selector(step.target, timeout=step.timeout * 1000)
    
    async def _handle_captcha(self, step: PlanStep):
        """Handle CAPTCHA solving action"""
        logger.info(f"Handling CAPTCHA: {step.target}")
        
        # Check if CAPTCHA is present
        captcha_element = await self.current_execution.page.query_selector(step.target)
        if captcha_element:
            logger.warning("CAPTCHA detected - manual intervention required")
            
            # Pause execution for manual CAPTCHA solving
            self.pause_execution()
            
            # Wait for CAPTCHA to be solved (element to disappear or form to be submittable)
            while await self.current_execution.page.query_selector(step.target):
                await asyncio.sleep(1)
            
            self.resume_execution()
            logger.info("CAPTCHA appears to be solved, continuing execution")
    
    async def _handle_checkpoint(self, step: PlanStep):
        """Handle checkpoint action for progress tracking"""
        logger.info(f"Checkpoint: {step.target}")
        
        if self.current_execution.checkpoint_callback:
            checkpoint_data = {
                "checkpoint": step.target,
                "description": step.description,
                "timestamp": time.time(),
                "url": self.current_execution.page.url,
                "title": await self.current_execution.page.title()
            }
            self.current_execution.checkpoint_callback(checkpoint_data)
    
    async def _verify_step_completion(self, step: PlanStep):
        """Verify that a step completed successfully"""
        if step.verification:
            await self._verify_condition(step.verification)
    
    async def _verify_condition(self, condition: str):
        """Verify a specific condition"""
        if condition.startswith("page_contains("):
            # Extract text from condition
            text = condition.split("'")[1]
            content = await self.current_execution.page.content()
            if text.lower() not in content.lower():
                raise Exception(f"Page does not contain expected text: {text}")
        
        elif condition.startswith("element_exists("):
            # Extract selector from condition
            selector = condition.split("'")[1]
            element = await self.current_execution.page.query_selector(selector)
            if not element:
                raise Exception(f"Element not found: {selector}")
        
        elif condition.startswith("url_contains("):
            # Extract URL fragment from condition
            url_fragment = condition.split("'")[1]
            current_url = self.current_execution.page.url
            if url_fragment not in current_url:
                raise Exception(f"URL does not contain: {url_fragment}")
    
    async def _intercept_request(self, route):
        """Intercept requests for HAR recording and monitoring"""
        # Log request for debugging
        logger.debug(f"Request: {route.request.method} {route.request.url}")
        
        # Continue with the request
        await route.continue_()
    
    def pause_execution(self):
        """Pause the current execution"""
        logger.info("Execution paused")
        self.is_paused = True
    
    def resume_execution(self):
        """Resume the paused execution"""
        logger.info("Execution resumed")
        self.is_paused = False
    
    def stop_execution(self):
        """Stop the current execution"""
        logger.info("Execution stopped")
        self.should_stop = True
    
    def get_execution_status(self) -> Dict[str, Any]:
        """Get current execution status"""
        if not self.current_execution:
            return {"status": "idle"}
        
        return {
            "status": "running" if not self.is_paused else "paused",
            "portal": self.current_execution.plan.portal_name,
            "url": self.current_execution.page.url if self.current_execution.page else None,
            "paused": self.is_paused,
            "should_stop": self.should_stop
        }
