"""
LangChain-based job submission planner for autonomous portal navigation
Generates step-by-step execution plans for job application workflows
"""

import json
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from langchain.llms import OpenAI
from langchain.chat_models import ChatOpenAI
from langchain.prompts import PromptTemplate, ChatPromptTemplate
from langchain.chains import <PERSON><PERSON>hain
from langchain.schema import BaseOutputParser
from langchain.agents import Too<PERSON>, AgentExecutor, create_react_agent
from langchain.memory import ConversationBufferMemory
from langchain.callbacks import StreamingStdOutCallbackHandler

logger = logging.getLogger(__name__)

class ActionType(Enum):
    NAVIGATE = "navigate"
    CLICK = "click"
    TYPE = "type"
    WAIT = "wait"
    SCROLL = "scroll"
    UPLOAD = "upload"
    SELECT = "select"
    VERIFY = "verify"
    CAPTCHA = "captcha"
    CHECKPOINT = "checkpoint"

@dataclass
class PlanStep:
    action: ActionType
    target: str
    value: Optional[str] = None
    timeout: int = 10
    retry_count: int = 3
    description: str = ""
    verification: Optional[str] = None

@dataclass
class ExecutionPlan:
    portal_name: str
    job_url: str
    steps: List[PlanStep]
    estimated_duration: int
    success_criteria: List[str]
    fallback_steps: List[PlanStep]

class PlanOutputParser(BaseOutputParser):
    """Parse LLM output into structured execution plan"""
    
    def parse(self, text: str) -> ExecutionPlan:
        try:
            # Extract JSON from LLM response
            start_idx = text.find('{')
            end_idx = text.rfind('}') + 1
            json_str = text[start_idx:end_idx]
            
            plan_data = json.loads(json_str)
            
            steps = []
            for step_data in plan_data.get('steps', []):
                step = PlanStep(
                    action=ActionType(step_data['action']),
                    target=step_data['target'],
                    value=step_data.get('value'),
                    timeout=step_data.get('timeout', 10),
                    retry_count=step_data.get('retry_count', 3),
                    description=step_data.get('description', ''),
                    verification=step_data.get('verification')
                )
                steps.append(step)
            
            fallback_steps = []
            for step_data in plan_data.get('fallback_steps', []):
                step = PlanStep(
                    action=ActionType(step_data['action']),
                    target=step_data['target'],
                    value=step_data.get('value'),
                    timeout=step_data.get('timeout', 10),
                    retry_count=step_data.get('retry_count', 3),
                    description=step_data.get('description', ''),
                    verification=step_data.get('verification')
                )
                fallback_steps.append(step)
            
            return ExecutionPlan(
                portal_name=plan_data['portal_name'],
                job_url=plan_data['job_url'],
                steps=steps,
                estimated_duration=plan_data.get('estimated_duration', 60),
                success_criteria=plan_data.get('success_criteria', []),
                fallback_steps=fallback_steps
            )
            
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.error(f"Failed to parse plan output: {e}")
            raise ValueError(f"Invalid plan format: {e}")

class JobSubmissionPlanner:
    """LangChain-powered planner for job submission workflows"""
    
    def __init__(self, 
                 openai_api_key: str,
                 model_name: str = "gpt-4",
                 temperature: float = 0.1):
        self.llm = ChatOpenAI(
            openai_api_key=openai_api_key,
            model_name=model_name,
            temperature=temperature,
            streaming=True,
            callbacks=[StreamingStdOutCallbackHandler()]
        )
        
        self.memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        )
        
        self.parser = PlanOutputParser()
        self._setup_prompts()
        self._setup_tools()
        
    def _setup_prompts(self):
        """Setup LangChain prompts for plan generation"""
        
        self.planning_template = """
You are an expert automation engineer specializing in job application portals.
Your task is to create a detailed execution plan for submitting a job application.

Portal Information:
- Portal Name: {portal_name}
- Job URL: {job_url}
- Portal Type: {portal_type}
- Known Selectors: {selectors}

User Profile:
- Resume: {resume_data}
- Cover Letter: {cover_letter}
- Personal Info: {personal_info}

Requirements:
1. Generate a step-by-step plan to navigate and fill the job application
2. Include proper error handling and verification steps
3. Account for common portal patterns (login, form filling, file uploads)
4. Add checkpoints for progress tracking
5. Include fallback strategies for common failures

Output Format (JSON):
{{
    "portal_name": "{portal_name}",
    "job_url": "{job_url}",
    "estimated_duration": 45,
    "steps": [
        {{
            "action": "navigate",
            "target": "{job_url}",
            "description": "Navigate to job posting",
            "timeout": 15,
            "verification": "page_contains('Apply')"
        }},
        {{
            "action": "click",
            "target": "button[data-testid='apply-button']",
            "description": "Click apply button",
            "timeout": 10,
            "verification": "page_contains('Application')"
        }}
    ],
    "success_criteria": [
        "Application submitted successfully",
        "Confirmation page displayed",
        "Application ID received"
    ],
    "fallback_steps": [
        {{
            "action": "captcha",
            "target": "iframe[src*='captcha']",
            "description": "Solve CAPTCHA if present"
        }}
    ]
}}

Generate the execution plan:
"""
        
        self.planning_prompt = ChatPromptTemplate.from_template(self.planning_template)
        
    def _setup_tools(self):
        """Setup LangChain tools for the agent"""
        
        def analyze_portal_structure(url: str) -> str:
            """Analyze portal structure and return common patterns"""
            # This would integrate with Playwright to analyze the page
            return f"Analyzed portal structure for {url}"
        
        def validate_selectors(selectors: str) -> str:
            """Validate CSS/XPath selectors against the portal"""
            return f"Validated selectors: {selectors}"
        
        def estimate_complexity(portal_type: str) -> str:
            """Estimate complexity and duration for portal type"""
            complexity_map = {
                "greenhouse": "Medium - 30-45 seconds",
                "workday": "High - 60-90 seconds", 
                "taleo": "High - 45-75 seconds",
                "lever": "Low - 20-30 seconds",
                "custom": "Variable - 30-120 seconds"
            }
            return complexity_map.get(portal_type.lower(), "Unknown complexity")
        
        self.tools = [
            Tool(
                name="analyze_portal_structure",
                description="Analyze the structure of a job portal to identify common elements",
                func=analyze_portal_structure
            ),
            Tool(
                name="validate_selectors", 
                description="Validate CSS/XPath selectors against a portal",
                func=validate_selectors
            ),
            Tool(
                name="estimate_complexity",
                description="Estimate the complexity and duration for a portal type",
                func=estimate_complexity
            )
        ]
        
    def create_execution_plan(self,
                            portal_name: str,
                            job_url: str,
                            portal_type: str,
                            selectors: Dict[str, str],
                            resume_data: Dict[str, Any],
                            cover_letter: str,
                            personal_info: Dict[str, str]) -> ExecutionPlan:
        """Create a detailed execution plan for job submission"""
        
        logger.info(f"Creating execution plan for {portal_name} - {job_url}")
        
        # Format the prompt with provided data
        formatted_prompt = self.planning_prompt.format_messages(
            portal_name=portal_name,
            job_url=job_url,
            portal_type=portal_type,
            selectors=json.dumps(selectors, indent=2),
            resume_data=json.dumps(resume_data, indent=2),
            cover_letter=cover_letter[:500] + "..." if len(cover_letter) > 500 else cover_letter,
            personal_info=json.dumps(personal_info, indent=2)
        )
        
        # Generate plan using LLM
        try:
            response = self.llm(formatted_prompt)
            plan = self.parser.parse(response.content)
            
            # Enhance plan with additional steps
            plan = self._enhance_plan(plan, portal_type, selectors)
            
            logger.info(f"Generated plan with {len(plan.steps)} steps, "
                       f"estimated duration: {plan.estimated_duration}s")
            
            return plan
            
        except Exception as e:
            logger.error(f"Failed to create execution plan: {e}")
            # Return a basic fallback plan
            return self._create_fallback_plan(portal_name, job_url)
    
    def _enhance_plan(self, plan: ExecutionPlan, portal_type: str, selectors: Dict[str, str]) -> ExecutionPlan:
        """Enhance the generated plan with portal-specific optimizations"""
        
        enhanced_steps = []
        
        # Add initial checkpoint
        enhanced_steps.append(PlanStep(
            action=ActionType.CHECKPOINT,
            target="start",
            description="Starting job application process",
            verification="page_loaded"
        ))
        
        # Process each step and add enhancements
        for step in plan.steps:
            enhanced_steps.append(step)
            
            # Add verification steps after critical actions
            if step.action in [ActionType.CLICK, ActionType.TYPE, ActionType.UPLOAD]:
                enhanced_steps.append(PlanStep(
                    action=ActionType.WAIT,
                    target="network_idle",
                    timeout=5,
                    description=f"Wait for page to stabilize after {step.action.value}"
                ))
        
        # Add final verification
        enhanced_steps.append(PlanStep(
            action=ActionType.VERIFY,
            target="application_submitted",
            description="Verify application was submitted successfully",
            verification="page_contains('submitted') or page_contains('thank you')"
        ))
        
        # Add final checkpoint
        enhanced_steps.append(PlanStep(
            action=ActionType.CHECKPOINT,
            target="complete",
            description="Job application completed",
            verification="application_confirmed"
        ))
        
        plan.steps = enhanced_steps
        return plan
    
    def _create_fallback_plan(self, portal_name: str, job_url: str) -> ExecutionPlan:
        """Create a basic fallback plan when LLM generation fails"""
        
        fallback_steps = [
            PlanStep(
                action=ActionType.NAVIGATE,
                target=job_url,
                description="Navigate to job posting",
                timeout=15
            ),
            PlanStep(
                action=ActionType.WAIT,
                target="page_load",
                timeout=10,
                description="Wait for page to load"
            ),
            PlanStep(
                action=ActionType.CLICK,
                target="[data-testid*='apply'], .apply-button, #apply-button",
                description="Click apply button",
                timeout=10
            ),
            PlanStep(
                action=ActionType.CHECKPOINT,
                target="application_started",
                description="Application process started"
            )
        ]
        
        return ExecutionPlan(
            portal_name=portal_name,
            job_url=job_url,
            steps=fallback_steps,
            estimated_duration=60,
            success_criteria=["Application form opened"],
            fallback_steps=[]
        )
    
    def adapt_plan_for_context(self, plan: ExecutionPlan, context: Dict[str, Any]) -> ExecutionPlan:
        """Adapt execution plan based on runtime context and feedback"""
        
        logger.info("Adapting plan based on runtime context")
        
        # Analyze context for plan modifications
        if context.get("captcha_detected"):
            # Insert CAPTCHA solving step
            captcha_step = PlanStep(
                action=ActionType.CAPTCHA,
                target="iframe[src*='captcha'], .captcha-container",
                description="Solve CAPTCHA challenge",
                timeout=60
            )
            plan.steps.insert(-1, captcha_step)
        
        if context.get("login_required"):
            # Insert login steps at the beginning
            login_steps = [
                PlanStep(
                    action=ActionType.CLICK,
                    target="[data-testid*='login'], .login-button, #login",
                    description="Click login button"
                ),
                PlanStep(
                    action=ActionType.TYPE,
                    target="input[type='email'], input[name*='email']",
                    value=context.get("email", ""),
                    description="Enter email address"
                ),
                PlanStep(
                    action=ActionType.TYPE,
                    target="input[type='password'], input[name*='password']",
                    value=context.get("password", ""),
                    description="Enter password"
                ),
                PlanStep(
                    action=ActionType.CLICK,
                    target="button[type='submit'], .submit-button",
                    description="Submit login form"
                )
            ]
            plan.steps = login_steps + plan.steps
        
        return plan
    
    def get_plan_summary(self, plan: ExecutionPlan) -> str:
        """Generate a human-readable summary of the execution plan"""
        
        summary = f"""
Execution Plan for {plan.portal_name}
=====================================
Job URL: {plan.job_url}
Estimated Duration: {plan.estimated_duration} seconds
Total Steps: {len(plan.steps)}

Steps:
"""
        
        for i, step in enumerate(plan.steps, 1):
            summary += f"{i:2d}. {step.action.value.upper()}: {step.description}\n"
            if step.target:
                summary += f"    Target: {step.target}\n"
            if step.value:
                summary += f"    Value: {step.value[:50]}...\n"
        
        summary += f"\nSuccess Criteria:\n"
        for criterion in plan.success_criteria:
            summary += f"  - {criterion}\n"
        
        return summary
