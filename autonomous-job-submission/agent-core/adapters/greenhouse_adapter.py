"""
Greenhouse job portal adapter
Implements automated job application submission for Greenhouse-powered career sites
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path

from ..adapter_sdk.base_adapter import BaseAdapter, SubmissionResult, SubmissionStatus, UserProfile

logger = logging.getLogger(__name__)

class GreenhouseAdapter(BaseAdapter):
    """Greenhouse portal automation adapter"""
    
    def __init__(self, config: Dict[str, Any] = None):
        manifest_path = Path(__file__).parent.parent.parent / "manifests" / "greenhouse.yml"
        super().__init__(str(manifest_path), config)
        
        self.current_page_index = 0
        self.total_pages = 1
        self.form_data = {}
    
    async def navigate_to_job(self, job_url: str):
        """Navigate to the Greenhouse job posting"""
        logger.info(f"Navigating to Greenhouse job: {job_url}")
        
        await self.page.goto(job_url, wait_until="networkidle")
        await self._wait_for_stable_page()
        
        # Check if job posting is valid
        if "greenhouse.io" not in self.page.url:
            raise Exception("Invalid Greenhouse job URL")
        
        # Take screenshot for verification
        await self._take_screenshot("job_page")
    
    async def login(self) -> bool:
        """Handle Greenhouse login if required"""
        logger.info("Checking if login is required")
        
        # Check for login indicators
        login_button = await self.page.query_selector(self._get_selector('login_button'))
        if not login_button:
            logger.info("No login required, proceeding as guest")
            return True
        
        # Get credentials from config
        email = self._get_config('email')
        password = self._get_config('password')
        
        if not email or not password:
            logger.warning("Login required but no credentials provided")
            return False
        
        try:
            # Click login button
            await self._click_element(self._get_selector('login_button'))
            
            # Fill login form
            await self._type_text(self._get_selector('email_field'), email)
            await self._type_text(self._get_selector('password_field'), password)
            
            # Submit login form
            await self._click_element(self._get_selector('login_submit'))
            await self._wait_for_navigation()
            
            # Verify login success
            if await self.page.query_selector(self._get_selector('login_button')):
                raise Exception("Login failed - still seeing login button")
            
            logger.info("Login successful")
            return True
            
        except Exception as e:
            logger.error(f"Login failed: {e}")
            return False
    
    async def start_application(self):
        """Start the Greenhouse application process"""
        logger.info("Starting Greenhouse application process")
        
        # Find and click the apply button
        apply_button = self._get_selector('apply_button')
        
        # Wait for apply button to be available
        if not await self._wait_for_element(apply_button):
            raise Exception("Apply button not found")
        
        # Scroll to apply button and click
        await self._scroll_to_element(apply_button)
        await self._click_element(apply_button)
        
        # Wait for application form to load
        await self._wait_for_stable_page()
        
        # Check if we're on the application form
        form_selector = self._get_selector('application_form')
        if not await self._wait_for_element(form_selector, timeout=15000):
            raise Exception("Application form did not load")
        
        # Determine if this is a multi-step form
        await self._analyze_form_structure()
        
        logger.info(f"Application form loaded - {self.total_pages} page(s) detected")
    
    async def fill_form(self, user_profile: UserProfile):
        """Fill out the Greenhouse application form"""
        logger.info("Filling Greenhouse application form")
        
        # Fill form page by page
        for page_index in range(self.total_pages):
            self.current_page_index = page_index
            
            await self._checkpoint(f"form_page_{page_index + 1}", 
                                 f"Filling form page {page_index + 1} of {self.total_pages}")
            
            # Fill current page
            await self._fill_current_page(user_profile)
            
            # Move to next page if not the last page
            if page_index < self.total_pages - 1:
                await self._go_to_next_page()
        
        logger.info("Form filling completed")
    
    async def upload_documents(self, user_profile: UserProfile):
        """Upload documents to Greenhouse"""
        logger.info("Uploading documents")
        
        # Upload resume
        if user_profile.resume_path:
            resume_selector = self._get_selector('resume_upload')
            if await self.page.query_selector(resume_selector):
                await self._upload_file(resume_selector, user_profile.resume_path)
                logger.info("Resume uploaded successfully")
        
        # Upload cover letter if supported and provided
        if user_profile.cover_letter_path and self.manifest.features.supports_cover_letter:
            cover_letter_selector = self._get_selector('cover_letter_upload')
            if await self.page.query_selector(cover_letter_selector):
                await self._upload_file(cover_letter_selector, user_profile.cover_letter_path)
                logger.info("Cover letter uploaded successfully")
        
        # Upload additional documents
        if user_profile.additional_documents:
            for doc_type, doc_path in user_profile.additional_documents.items():
                additional_selector = self._get_selector('additional_documents')
                if await self.page.query_selector(additional_selector):
                    await self._upload_file(additional_selector, doc_path)
                    logger.info(f"Additional document uploaded: {doc_type}")
        
        # Wait for uploads to process
        await asyncio.sleep(2)
    
    async def review_application(self):
        """Review the application before submission"""
        logger.info("Reviewing application")
        
        # Look for review page indicators
        review_indicators = [
            ".review-page",
            ".application-review",
            "h2:contains('Review')",
            "h3:contains('Review')"
        ]
        
        for indicator in review_indicators:
            if await self.page.query_selector(indicator):
                logger.info("Review page detected")
                await self._take_screenshot("review_page")
                break
        
        # Check for any validation errors
        error_selector = self._get_selector('error_indicators')[0] if self._get_selector('error_indicators') else None
        if error_selector and await self.page.query_selector(error_selector):
            error_text = await self.page.text_content(error_selector)
            raise Exception(f"Validation error found: {error_text}")
        
        # Handle CAPTCHA if present
        if not await self._handle_captcha():
            raise Exception("CAPTCHA solving failed")
    
    async def submit_form(self) -> SubmissionResult:
        """Submit the Greenhouse application form"""
        logger.info("Submitting Greenhouse application")
        
        # Find submit button
        submit_button = self._get_selector('submit_button')
        if not await self._wait_for_element(submit_button):
            raise Exception("Submit button not found")
        
        # Take screenshot before submission
        await self._take_screenshot("before_submit")
        
        # Click submit button
        await self._click_element(submit_button)
        
        # Wait for submission to process
        await self._wait_for_stable_page(timeout=30000)
        
        # Check for immediate errors
        error_indicators = self._get_selector('error_indicators', [])
        for error_selector in error_indicators:
            if await self.page.query_selector(error_selector):
                error_text = await self.page.text_content(error_selector)
                return SubmissionResult(
                    status=SubmissionStatus.FAILED,
                    message=f"Submission failed: {error_text}",
                    error_details=error_text
                )
        
        return SubmissionResult(
            status=SubmissionStatus.SUCCESS,
            message="Application submitted successfully"
        )
    
    async def verify_submission(self) -> bool:
        """Verify that the Greenhouse application was submitted successfully"""
        logger.info("Verifying Greenhouse submission")
        
        # Check for success indicators
        success_indicators = self._get_selector('success_indicators', [])
        for indicator in success_indicators:
            if await self.page.query_selector(indicator):
                logger.info(f"Success indicator found: {indicator}")
                
                # Try to extract application ID
                app_id_selector = self._get_selector('application_id')
                if app_id_selector:
                    app_id_element = await self.page.query_selector(app_id_selector)
                    if app_id_element:
                        app_id = await app_id_element.text_content()
                        logger.info(f"Application ID: {app_id}")
                
                # Take final screenshot
                await self._take_screenshot("submission_success")
                return True
        
        # Check URL for confirmation page
        current_url = self.page.url
        if any(keyword in current_url.lower() for keyword in ['thank', 'confirm', 'success', 'submitted']):
            logger.info("Confirmation page detected via URL")
            await self._take_screenshot("submission_success")
            return True
        
        # Check page content for success messages
        page_content = await self.page.content()
        success_keywords = ['thank you', 'submitted', 'received', 'confirmation']
        if any(keyword in page_content.lower() for keyword in success_keywords):
            logger.info("Success message detected in page content")
            await self._take_screenshot("submission_success")
            return True
        
        logger.warning("Could not verify submission success")
        await self._take_screenshot("verification_failed")
        return False
    
    # Helper methods
    
    async def _analyze_form_structure(self):
        """Analyze the form structure to determine number of pages"""
        # Look for pagination indicators
        pagination_indicators = [
            ".pagination",
            ".step-indicator",
            ".progress-bar",
            "[data-step]"
        ]
        
        for indicator in pagination_indicators:
            elements = await self.page.query_selector_all(indicator)
            if elements:
                # Try to determine total pages from pagination
                try:
                    # Look for step indicators
                    steps = await self.page.query_selector_all(".step, [data-step]")
                    if steps:
                        self.total_pages = len(steps)
                        return
                except:
                    pass
        
        # Default to single page if no pagination detected
        self.total_pages = 1
    
    async def _fill_current_page(self, user_profile: UserProfile):
        """Fill the current page of the form"""
        # Personal information
        await self._fill_personal_info(user_profile)
        
        # Professional information
        await self._fill_professional_info(user_profile)
        
        # Custom questions
        await self._fill_custom_questions(user_profile)
    
    async def _fill_personal_info(self, user_profile: UserProfile):
        """Fill personal information fields"""
        personal_fields = {
            'first_name': user_profile.personal_info.get('first_name'),
            'last_name': user_profile.personal_info.get('last_name'),
            'email': user_profile.personal_info.get('email'),
            'phone': user_profile.personal_info.get('phone'),
            'address': user_profile.personal_info.get('address'),
            'city': user_profile.personal_info.get('city'),
            'state': user_profile.personal_info.get('state'),
            'zip_code': user_profile.personal_info.get('zip_code'),
            'country': user_profile.personal_info.get('country')
        }
        
        for field_name, value in personal_fields.items():
            if value:
                selector = self._get_selector(field_name)
                if selector and await self.page.query_selector(selector):
                    await self._type_text(selector, value)
    
    async def _fill_professional_info(self, user_profile: UserProfile):
        """Fill professional information fields"""
        professional_fields = {
            'current_company': user_profile.personal_info.get('current_company'),
            'current_title': user_profile.personal_info.get('current_title'),
            'experience_years': user_profile.personal_info.get('experience_years'),
            'salary_expectation': user_profile.personal_info.get('salary_expectation'),
            'availability_date': user_profile.personal_info.get('availability_date')
        }
        
        for field_name, value in professional_fields.items():
            if value:
                selector = self._get_selector(field_name)
                if selector and await self.page.query_selector(selector):
                    # Handle different field types
                    element = await self.page.query_selector(selector)
                    tag_name = await element.evaluate("el => el.tagName.toLowerCase()")
                    
                    if tag_name == 'select':
                        await self._select_option(selector, value)
                    else:
                        await self._type_text(selector, value)
    
    async def _fill_custom_questions(self, user_profile: UserProfile):
        """Fill custom questions using AI patterns"""
        # This would integrate with the question patterns from the manifest
        # and use AI to generate appropriate responses
        pass
    
    async def _go_to_next_page(self):
        """Navigate to the next page of the form"""
        next_button = self._get_selector('next_button')
        if await self.page.query_selector(next_button):
            await self._click_element(next_button)
            await self._wait_for_stable_page()
        else:
            raise Exception("Next button not found")
