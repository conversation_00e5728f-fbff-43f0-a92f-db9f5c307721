#!/bin/bash
set -euo pipefail

# CVLeap Job Submission Sandbox Entrypoint
# Bootstraps <PERSON><PERSON>, terminald, and agent <PERSON><PERSON> with security hardening

# Configuration
ENABLE_TERMINAL="${ENABLE_TERMINAL:-0}"
TERMINAL_PORT="${TERMINAL_PORT:-7000}"
SIDECAR_PORT="${SIDECAR_PORT:-8080}"
LOG_LEVEL="${LOG_LEVEL:-INFO}"
SANDBOX_ID="${SANDBOX_ID:-sandbox_$(date +%s)}"

# Logging setup
LOG_FILE="/var/log/cvleap/sandbox.log"
exec > >(tee -a "$LOG_FILE")
exec 2>&1

echo "[$(date -Iseconds)] Starting CVLeap Job Submission Sandbox"
echo "[$(date -Iseconds)] Sandbox ID: $SANDBOX_ID"
echo "[$(date -Iseconds)] Terminal enabled: $ENABLE_TERMINAL"

# Function to handle shutdown
cleanup() {
    echo "[$(date -Iseconds)] Received shutdown signal, cleaning up..."
    
    # Stop background processes
    if [[ -n "${TERMINALD_PID:-}" ]]; then
        kill -TERM "$TERMINALD_PID" 2>/dev/null || true
    fi
    
    if [[ -n "${SIDECAR_PID:-}" ]]; then
        kill -TERM "$SIDECAR_PID" 2>/dev/null || true
    fi
    
    if [[ -n "${AGENT_PID:-}" ]]; then
        kill -TERM "$AGENT_PID" 2>/dev/null || true
    fi
    
    # Kill tmux session
    tmux kill-session -t cvleap 2>/dev/null || true
    
    echo "[$(date -Iseconds)] Cleanup completed"
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Validate environment
validate_environment() {
    echo "[$(date -Iseconds)] Validating environment..."
    
    # Check required directories
    for dir in /tmp/artifacts /tmp/screenshots /var/log/cvleap; do
        if [[ ! -d "$dir" ]]; then
            echo "[$(date -Iseconds)] ERROR: Required directory missing: $dir"
            exit 1
        fi
    done
    
    # Check Playwright installation
    if ! playwright --version >/dev/null 2>&1; then
        echo "[$(date -Iseconds)] ERROR: Playwright not installed or not accessible"
        exit 1
    fi
    
    # Check Python dependencies
    if ! python -c "import fastapi, playwright, langchain" 2>/dev/null; then
        echo "[$(date -Iseconds)] ERROR: Required Python dependencies missing"
        exit 1
    fi
    
    echo "[$(date -Iseconds)] Environment validation passed"
}

# Apply security hardening
apply_security() {
    echo "[$(date -Iseconds)] Applying security hardening..."
    
    # Set resource limits
    ulimit -n 1024        # File descriptors
    ulimit -u 128         # Processes
    ulimit -m 2097152     # Memory (2GB)
    ulimit -t 300         # CPU time (5 minutes)
    
    # Set umask for secure file creation
    umask 0077
    
    echo "[$(date -Iseconds)] Security hardening applied"
}

# Initialize tmux session
init_tmux() {
    echo "[$(date -Iseconds)] Initializing tmux session..."
    
    # Kill any existing session
    tmux kill-session -t cvleap 2>/dev/null || true
    
    # Create new session
    tmux new-session -d -s cvleap -x 120 -y 30
    
    # Configure tmux
    tmux set-option -t cvleap -g history-limit 10000
    tmux set-option -t cvleap -g mouse on
    tmux set-option -t cvleap -g status-bg blue
    tmux set-option -t cvleap -g status-fg white
    tmux set-option -t cvleap -g status-left "[CVLeap] "
    tmux set-option -t cvleap -g status-right "[${SANDBOX_ID}] %H:%M:%S"
    
    # Send initial message
    tmux send-keys -t cvleap "echo '[$(date -Iseconds)] CVLeap Job Submission Sandbox Ready'" Enter
    tmux send-keys -t cvleap "echo 'Sandbox ID: ${SANDBOX_ID}'" Enter
    tmux send-keys -t cvleap "echo 'Terminal enabled: ${ENABLE_TERMINAL}'" Enter
    tmux send-keys -t cvleap "echo ''" Enter
    
    echo "[$(date -Iseconds)] tmux session initialized"
}

# Start terminald daemon
start_terminald() {
    if [[ "$ENABLE_TERMINAL" == "1" ]]; then
        echo "[$(date -Iseconds)] Starting terminald on port $TERMINAL_PORT..."
        
        export JWT_SECRET="${JWT_SECRET:-cvleap-terminal-secret-$(date +%s)}"
        export RING_BUFFER_SIZE="${RING_BUFFER_SIZE:-10485760}"
        
        terminald &
        TERMINALD_PID=$!
        
        # Wait for terminald to start
        sleep 2
        
        if kill -0 "$TERMINALD_PID" 2>/dev/null; then
            echo "[$(date -Iseconds)] terminald started successfully (PID: $TERMINALD_PID)"
        else
            echo "[$(date -Iseconds)] ERROR: terminald failed to start"
            exit 1
        fi
    else
        echo "[$(date -Iseconds)] Terminal disabled, skipping terminald"
    fi
}

# Start FastAPI sidecar
start_sidecar() {
    echo "[$(date -Iseconds)] Starting FastAPI sidecar on port $SIDECAR_PORT..."
    
    cd /app/sidecar
    python -m uvicorn server:app \
        --host 0.0.0.0 \
        --port "$SIDECAR_PORT" \
        --log-level "${LOG_LEVEL,,}" \
        --access-log \
        --no-server-header &
    
    SIDECAR_PID=$!
    
    # Wait for sidecar to start
    sleep 3
    
    # Health check
    if curl -f "http://localhost:$SIDECAR_PORT/ready" >/dev/null 2>&1; then
        echo "[$(date -Iseconds)] FastAPI sidecar started successfully (PID: $SIDECAR_PID)"
    else
        echo "[$(date -Iseconds)] ERROR: FastAPI sidecar failed to start"
        exit 1
    fi
}

# Start job submission agent
start_agent() {
    echo "[$(date -Iseconds)] Starting job submission agent..."
    
    cd /app/agent-core
    python -m agent_main &
    AGENT_PID=$!
    
    echo "[$(date -Iseconds)] Job submission agent started (PID: $AGENT_PID)"
}

# Monitor processes
monitor_processes() {
    echo "[$(date -Iseconds)] Starting process monitoring..."
    
    while true; do
        # Check sidecar
        if [[ -n "${SIDECAR_PID:-}" ]] && ! kill -0 "$SIDECAR_PID" 2>/dev/null; then
            echo "[$(date -Iseconds)] ERROR: FastAPI sidecar died"
            exit 1
        fi
        
        # Check terminald (if enabled)
        if [[ "$ENABLE_TERMINAL" == "1" ]] && [[ -n "${TERMINALD_PID:-}" ]] && ! kill -0 "$TERMINALD_PID" 2>/dev/null; then
            echo "[$(date -Iseconds)] ERROR: terminald died"
            exit 1
        fi
        
        # Check agent
        if [[ -n "${AGENT_PID:-}" ]] && ! kill -0 "$AGENT_PID" 2>/dev/null; then
            echo "[$(date -Iseconds)] WARNING: Job submission agent died, restarting..."
            start_agent
        fi
        
        # Check tmux session
        if ! tmux has-session -t cvleap 2>/dev/null; then
            echo "[$(date -Iseconds)] WARNING: tmux session died, recreating..."
            init_tmux
        fi
        
        sleep 10
    done
}

# Print system information
print_system_info() {
    echo "[$(date -Iseconds)] System Information:"
    echo "  OS: $(uname -a)"
    echo "  Python: $(python --version)"
    echo "  Playwright: $(playwright --version)"
    echo "  Memory: $(free -h | grep Mem | awk '{print $2}')"
    echo "  CPU: $(nproc) cores"
    echo "  Disk: $(df -h / | tail -1 | awk '{print $4}') available"
    echo ""
}

# Main execution
main() {
    case "${1:-run}" in
        "run")
            print_system_info
            validate_environment
            apply_security
            init_tmux
            start_terminald
            start_sidecar
            start_agent
            
            echo "[$(date -Iseconds)] All services started successfully"
            echo "[$(date -Iseconds)] Sandbox ready for job submissions"
            
            # Send ready signal to tmux
            tmux send-keys -t cvleap "echo '[$(date -Iseconds)] Sandbox ready for job submissions'" Enter
            
            monitor_processes
            ;;
            
        "test")
            echo "[$(date -Iseconds)] Running sandbox tests..."
            validate_environment
            
            # Test Playwright
            echo "[$(date -Iseconds)] Testing Playwright..."
            python -c "
from playwright.sync_api import sync_playwright
with sync_playwright() as p:
    browser = p.chromium.launch(headless=True)
    page = browser.new_page()
    page.goto('https://example.com')
    print(f'Page title: {page.title()}')
    browser.close()
print('Playwright test passed')
"
            
            echo "[$(date -Iseconds)] All tests passed"
            ;;
            
        "shell")
            echo "[$(date -Iseconds)] Starting interactive shell..."
            exec /bin/bash
            ;;
            
        "tmux")
            echo "[$(date -Iseconds)] Attaching to tmux session..."
            init_tmux
            exec tmux attach-session -t cvleap
            ;;
            
        *)
            echo "Usage: $0 {run|test|shell|tmux}"
            echo "  run   - Start the job submission sandbox (default)"
            echo "  test  - Run system tests"
            echo "  shell - Start interactive shell"
            echo "  tmux  - Attach to tmux session"
            exit 1
            ;;
    esac
}

# Execute main function
main "$@"
