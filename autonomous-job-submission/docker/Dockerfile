# Multi-stage Dockerfile for autonomous job submission sandbox
# Optimized for security, performance, and minimal attack surface

# Stage 1: Build terminald (Go daemon)
FROM golang:1.21-alpine AS terminald-builder

WORKDIR /build
COPY terminald/go.mod terminald/go.sum ./
RUN go mod download

COPY terminald/ ./
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o terminald .

# Stage 2: Build Python dependencies
FROM python:3.12-slim AS python-builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install Python dependencies
COPY agent-core/requirements.txt /tmp/
RUN pip install --no-cache-dir -r /tmp/requirements.txt

# Stage 3: Main runtime image
FROM mcr.microsoft.com/playwright/python:v1.45.0-jammy

# Create non-root user
RUN groupadd -r cvleap && useradd -r -g cvleap -u 1001 cvleap

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Core utilities
    tmux \
    curl \
    wget \
    unzip \
    jq \
    # Security tools
    apparmor \
    apparmor-utils \
    # Monitoring
    htop \
    psmisc \
    # Cleanup
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install Python virtual environment
COPY --from=python-builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install terminald
COPY --from=terminald-builder /build/terminald /usr/local/bin/terminald
RUN chmod +x /usr/local/bin/terminald

# Create application directories
RUN mkdir -p /app /tmp/artifacts /tmp/screenshots /var/log/cvleap \
    && chown -R cvleap:cvleap /app /tmp/artifacts /tmp/screenshots /var/log/cvleap

# Copy application code
COPY agent-core/ /app/agent-core/
COPY adapter-sdk/ /app/adapter-sdk/
COPY sidecar/ /app/sidecar/
COPY manifests/ /app/manifests/
COPY security/ /app/security/

# Copy entrypoint and configuration
COPY docker/entrypoint.sh /app/entrypoint.sh
COPY docker/security.conf /app/security.conf
COPY docker/tmux.conf /home/<USER>/.tmux.conf

# Set permissions
RUN chmod +x /app/entrypoint.sh \
    && chown -R cvleap:cvleap /app /home/<USER>

# Install Playwright browsers (as cvleap user)
USER cvleap
RUN playwright install chromium
USER root

# Security hardening
RUN echo 'cvleap ALL=(ALL) NOPASSWD: /usr/bin/tmux' >> /etc/sudoers.d/cvleap \
    && chmod 440 /etc/sudoers.d/cvleap

# Configure AppArmor profile
COPY security/apparmor/cvleap-sandbox /etc/apparmor.d/
RUN apparmor_parser -r -W /etc/apparmor.d/cvleap-sandbox

# Configure seccomp profile
COPY security/seccomp/job-submission.json /app/seccomp.json

# Set up read-only filesystem preparation
RUN mkdir -p /tmp-rw /var-rw /run-rw \
    && chown cvleap:cvleap /tmp-rw /var-rw /run-rw

# Environment variables
ENV PYTHONPATH="/app:/app/agent-core:/app/adapter-sdk:/app/sidecar"
ENV PLAYWRIGHT_BROWSERS_PATH="/home/<USER>/.cache/ms-playwright"
ENV ENABLE_TERMINAL="0"
ENV TERMINAL_PORT="7000"
ENV SIDECAR_PORT="8080"
ENV LOG_LEVEL="INFO"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/ready || exit 1

# Expose ports
EXPOSE 7000 8080

# Switch to non-root user
USER cvleap
WORKDIR /app

# Set entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["run"]
