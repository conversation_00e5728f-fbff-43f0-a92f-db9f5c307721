package main

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// JWTClaims represents the claims in our JWT token
type JWTClaims struct {
	UserID    string `json:"user_id"`
	SandboxID string `json:"sandbox_id"`
	UserIP    string `json:"user_ip"`
	Role      string `json:"role"`
	jwt.RegisteredClaims
}

// JWTAuth handles JWT token validation and generation
type J<PERSON><PERSON>uth struct {
	secret []byte
}

// NewJWTAuth creates a new JWT authentication handler
func NewJWTAuth(secret string) *JWTAuth {
	return &JWTAuth{
		secret: []byte(secret),
	}
}

// ValidateToken validates a JWT token and returns the claims
func (ja *JWTAuth) ValidateToken(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate the signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return ja.secret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		// Check if token is expired
		if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
			return nil, fmt.Errorf("token is expired")
		}
		
		// Check if token is not yet valid
		if claims.NotBefore != nil && claims.NotBefore.Time.After(time.Now()) {
			return nil, fmt.Errorf("token is not yet valid")
		}
		
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token claims")
}

// GenerateToken generates a new JWT token with the given claims
func (ja *JWTAuth) GenerateToken(userID, sandboxID, userIP, role string, duration time.Duration) (string, error) {
	now := time.Now()
	claims := &JWTClaims{
		UserID:    userID,
		SandboxID: sandboxID,
		UserIP:    userIP,
		Role:      role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(duration)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "cvleap-terminald",
			Subject:   userID,
			ID:        fmt.Sprintf("%s-%d", sandboxID, now.Unix()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(ja.secret)
}

// Enhanced JWT validation for terminald
func (td *TerminalDaemon) validateJWT(tokenString string) bool {
	if tokenString == "" {
		return false
	}

	auth := NewJWTAuth(td.config.JWTSecret)
	claims, err := auth.ValidateToken(tokenString)
	if err != nil {
		log.Printf("JWT validation failed: %v", err)
		return false
	}

	// Additional validation checks
	if claims.SandboxID == "" {
		log.Printf("JWT missing sandbox_id claim")
		return false
	}

	// Check if this is a single-use token (should be consumed after first use)
	// In production, you'd track used tokens in Redis or similar
	
	log.Printf("JWT validated for user %s, sandbox %s, role %s", 
		claims.UserID, claims.SandboxID, claims.Role)
	
	return true
}

// IsAdvancedUser checks if the user has advanced privileges
func (td *TerminalDaemon) isAdvancedUser(tokenString string) bool {
	auth := NewJWTAuth(td.config.JWTSecret)
	claims, err := auth.ValidateToken(tokenString)
	if err != nil {
		return false
	}
	
	return claims.Role == "Advanced" || claims.Role == "Admin"
}

// Enhanced input filtering based on user role
func (td *TerminalDaemon) isAllowedInput(input []byte, userRole string) bool {
	// Advanced users get full access
	if userRole == "Advanced" || userRole == "Admin" {
		return true
	}
	
	// Basic users get filtered input
	for _, b := range input {
		if !((b >= 'a' && b <= 'z') || (b >= 'A' && b <= 'Z') || 
			 (b >= '0' && b <= '9') || b == ' ' || b == '\n' || 
			 b == '\r' || b == '\t' || b == '-' || b == '_' || 
			 b == '.' || b == '/' || b == '=' || b == '"' || b == '\'') {
			return false
		}
	}
	
	// Block potentially dangerous commands for basic users
	inputStr := string(input)
	dangerousCommands := []string{
		"rm ", "sudo ", "su ", "chmod ", "chown ", "kill ", "killall ",
		"pkill ", "systemctl ", "service ", "mount ", "umount ", "dd ",
		"fdisk ", "mkfs ", "fsck ", "iptables ", "ufw ", "firewall-cmd ",
	}
	
	for _, cmd := range dangerousCommands {
		if len(inputStr) >= len(cmd) && inputStr[:len(cmd)] == cmd {
			return false
		}
	}
	
	return true
}
