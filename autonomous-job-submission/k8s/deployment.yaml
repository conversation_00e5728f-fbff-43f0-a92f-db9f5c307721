apiVersion: apps/v1
kind: Deployment
metadata:
  name: job-submission-sandbox
  namespace: cvleap
  labels:
    app: job-submission
    component: sandbox
spec:
  replicas: 3
  selector:
    matchLabels:
      app: job-submission
      component: sandbox
  template:
    metadata:
      labels:
        app: job-submission
        component: sandbox
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: job-submission-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
        seccompProfile:
          type: Localhost
          localhostProfile: job-submission.json
      containers:
      - name: sandbox
        image: cvleap/job-submission:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 7000
          name: terminal
          protocol: TCP
        - containerPort: 8080
          name: sidecar
          protocol: TCP
        env:
        - name: ENABLE_TERMINAL
          value: "1"
        - name: TERMINAL_PORT
          value: "7000"
        - name: SIDECAR_PORT
          value: "8080"
        - name: SANDBOX_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: job-submission-secrets
              key: jwt-secret
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: job-submission-secrets
              key: openai-api-key
        - name: BRIGHTDATA_USERNAME
          valueFrom:
            secretKeyRef:
              name: job-submission-secrets
              key: brightdata-username
        - name: BRIGHTDATA_PASSWORD
          valueFrom:
            secretKeyRef:
              name: job-submission-secrets
              key: brightdata-password
        - name: LOG_LEVEL
          value: "INFO"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
            add:
            - NET_BIND_SERVICE
        volumeMounts:
        - name: tmp-volume
          mountPath: /tmp
        - name: var-volume
          mountPath: /var/tmp
        - name: artifacts-volume
          mountPath: /tmp/artifacts
        - name: screenshots-volume
          mountPath: /tmp/screenshots
        - name: logs-volume
          mountPath: /var/log/cvleap
        - name: seccomp-profile
          mountPath: /app/seccomp.json
          subPath: job-submission.json
          readOnly: true
        livenessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 12
      volumes:
      - name: tmp-volume
        emptyDir:
          sizeLimit: 1Gi
      - name: var-volume
        emptyDir:
          sizeLimit: 512Mi
      - name: artifacts-volume
        emptyDir:
          sizeLimit: 2Gi
      - name: screenshots-volume
        emptyDir:
          sizeLimit: 1Gi
      - name: logs-volume
        emptyDir:
          sizeLimit: 512Mi
      - name: seccomp-profile
        configMap:
          name: seccomp-profiles
      nodeSelector:
        node-type: compute
      tolerations:
      - key: "job-submission"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - job-submission
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: job-submission-service
  namespace: cvleap
  labels:
    app: job-submission
spec:
  selector:
    app: job-submission
    component: sandbox
  ports:
  - name: terminal
    port: 7000
    targetPort: 7000
    protocol: TCP
  - name: sidecar
    port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: job-submission-sa
  namespace: cvleap
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: cvleap
  name: job-submission-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: job-submission-rolebinding
  namespace: cvleap
subjects:
- kind: ServiceAccount
  name: job-submission-sa
  namespace: cvleap
roleRef:
  kind: Role
  name: job-submission-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: seccomp-profiles
  namespace: cvleap
data:
  job-submission.json: |
    {
      "defaultAction": "SCMP_ACT_ERRNO",
      "architectures": ["SCMP_ARCH_X86_64"],
      "syscalls": [
        {
          "names": [
            "accept", "bind", "brk", "clone", "close", "connect", "dup", "dup2",
            "execve", "exit", "exit_group", "fcntl", "fork", "futex", "getcwd",
            "getdents64", "getpid", "getppid", "getuid", "listen", "mmap", "open",
            "openat", "read", "recv", "recvfrom", "send", "sendto", "socket",
            "stat", "write", "writev"
          ],
          "action": "SCMP_ACT_ALLOW"
        }
      ]
    }
