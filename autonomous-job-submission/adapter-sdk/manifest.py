"""
Portal manifest system for configuration-driven automation
Defines selectors, strategies, and portal-specific settings
"""

import yaml
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class PortalSelectors:
    """CSS/XPath selectors for portal elements"""
    
    # Authentication
    login_button: Optional[str] = None
    email_field: Optional[str] = None
    password_field: Optional[str] = None
    login_submit: Optional[str] = None
    login_indicators: List[str] = field(default_factory=list)
    
    # Application process
    apply_button: Optional[str] = None
    application_form: Optional[str] = None
    next_button: Optional[str] = None
    previous_button: Optional[str] = None
    submit_button: Optional[str] = None
    
    # Form fields
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    country: Optional[str] = None
    
    # Professional info
    current_company: Optional[str] = None
    current_title: Optional[str] = None
    experience_years: Optional[str] = None
    salary_expectation: Optional[str] = None
    availability_date: Optional[str] = None
    
    # File uploads
    resume_upload: Optional[str] = None
    cover_letter_upload: Optional[str] = None
    portfolio_upload: Optional[str] = None
    additional_documents: Optional[str] = None
    
    # Questions and surveys
    text_questions: List[str] = field(default_factory=list)
    dropdown_questions: List[str] = field(default_factory=list)
    checkbox_questions: List[str] = field(default_factory=list)
    radio_questions: List[str] = field(default_factory=list)
    
    # Verification and confirmation
    success_indicators: List[str] = field(default_factory=list)
    error_indicators: List[str] = field(default_factory=list)
    confirmation_page: Optional[str] = None
    application_id: Optional[str] = None
    
    # CAPTCHA and security
    captcha: List[str] = field(default_factory=list)
    recaptcha: Optional[str] = None
    security_questions: List[str] = field(default_factory=list)
    
    # Navigation
    pagination_next: Optional[str] = None
    pagination_previous: Optional[str] = None
    breadcrumbs: Optional[str] = None

@dataclass
class PortalStrategies:
    """Automation strategies for different portal behaviors"""
    
    login_strategy: str = "form_based"  # form_based, oauth, sso, none
    form_strategy: str = "single_page"  # single_page, multi_step, wizard
    upload_strategy: str = "direct"     # direct, drag_drop, modal
    captcha_strategy: str = "2captcha"  # 2captcha, manual, anticaptcha
    verification_strategy: str = "url_change"  # url_change, element_presence, text_content
    
    # Timing strategies
    page_load_wait: int = 5000
    element_wait: int = 10000
    form_submit_wait: int = 15000
    upload_wait: int = 30000
    
    # Retry strategies
    max_retries: int = 3
    retry_delay: int = 2000
    exponential_backoff: bool = True

@dataclass
class PortalFeatures:
    """Supported features for this portal"""
    
    supports_login: bool = True
    supports_guest_application: bool = False
    supports_resume_upload: bool = True
    supports_cover_letter: bool = True
    supports_portfolio: bool = False
    supports_salary_info: bool = True
    supports_availability: bool = True
    supports_custom_questions: bool = True
    supports_bulk_upload: bool = False
    supports_application_tracking: bool = True

@dataclass
class PortalManifest:
    """Complete portal configuration manifest"""
    
    portal_name: str
    base_url: str
    version: str
    description: str
    
    selectors: PortalSelectors
    strategies: PortalStrategies
    features: PortalFeatures
    
    # Custom configuration
    custom_config: Dict[str, Any] = field(default_factory=dict)
    
    # Field mappings for dynamic forms
    field_mappings: Dict[str, str] = field(default_factory=dict)
    
    # Question patterns for AI-powered form filling
    question_patterns: Dict[str, str] = field(default_factory=dict)
    
    @classmethod
    def load(cls, manifest_path: str) -> 'PortalManifest':
        """Load portal manifest from YAML file"""
        
        try:
            with open(manifest_path, 'r') as f:
                data = yaml.safe_load(f)
            
            # Parse selectors
            selectors_data = data.get('selectors', {})
            selectors = PortalSelectors(**selectors_data)
            
            # Parse strategies
            strategies_data = data.get('strategies', {})
            strategies = PortalStrategies(**strategies_data)
            
            # Parse features
            features_data = data.get('features', {})
            features = PortalFeatures(**features_data)
            
            # Create manifest
            manifest = cls(
                portal_name=data['portal']['name'],
                base_url=data['portal']['base_url'],
                version=data['portal'].get('version', '1.0.0'),
                description=data['portal'].get('description', ''),
                selectors=selectors,
                strategies=strategies,
                features=features,
                custom_config=data.get('custom_config', {}),
                field_mappings=data.get('field_mappings', {}),
                question_patterns=data.get('question_patterns', {})
            )
            
            logger.info(f"Loaded manifest for {manifest.portal_name} v{manifest.version}")
            return manifest
            
        except Exception as e:
            logger.error(f"Failed to load manifest from {manifest_path}: {e}")
            raise
    
    def save(self, manifest_path: str):
        """Save portal manifest to YAML file"""
        
        data = {
            'portal': {
                'name': self.portal_name,
                'base_url': self.base_url,
                'version': self.version,
                'description': self.description
            },
            'selectors': self._selectors_to_dict(),
            'strategies': self._strategies_to_dict(),
            'features': self._features_to_dict(),
            'custom_config': self.custom_config,
            'field_mappings': self.field_mappings,
            'question_patterns': self.question_patterns
        }
        
        try:
            with open(manifest_path, 'w') as f:
                yaml.dump(data, f, default_flow_style=False, indent=2)
            
            logger.info(f"Saved manifest to {manifest_path}")
            
        except Exception as e:
            logger.error(f"Failed to save manifest to {manifest_path}: {e}")
            raise
    
    def _selectors_to_dict(self) -> Dict[str, Any]:
        """Convert selectors to dictionary for YAML serialization"""
        return {
            'login_button': self.selectors.login_button,
            'email_field': self.selectors.email_field,
            'password_field': self.selectors.password_field,
            'login_submit': self.selectors.login_submit,
            'login_indicators': self.selectors.login_indicators,
            'apply_button': self.selectors.apply_button,
            'application_form': self.selectors.application_form,
            'next_button': self.selectors.next_button,
            'previous_button': self.selectors.previous_button,
            'submit_button': self.selectors.submit_button,
            'first_name': self.selectors.first_name,
            'last_name': self.selectors.last_name,
            'email': self.selectors.email,
            'phone': self.selectors.phone,
            'address': self.selectors.address,
            'city': self.selectors.city,
            'state': self.selectors.state,
            'zip_code': self.selectors.zip_code,
            'country': self.selectors.country,
            'current_company': self.selectors.current_company,
            'current_title': self.selectors.current_title,
            'experience_years': self.selectors.experience_years,
            'salary_expectation': self.selectors.salary_expectation,
            'availability_date': self.selectors.availability_date,
            'resume_upload': self.selectors.resume_upload,
            'cover_letter_upload': self.selectors.cover_letter_upload,
            'portfolio_upload': self.selectors.portfolio_upload,
            'additional_documents': self.selectors.additional_documents,
            'text_questions': self.selectors.text_questions,
            'dropdown_questions': self.selectors.dropdown_questions,
            'checkbox_questions': self.selectors.checkbox_questions,
            'radio_questions': self.selectors.radio_questions,
            'success_indicators': self.selectors.success_indicators,
            'error_indicators': self.selectors.error_indicators,
            'confirmation_page': self.selectors.confirmation_page,
            'application_id': self.selectors.application_id,
            'captcha': self.selectors.captcha,
            'recaptcha': self.selectors.recaptcha,
            'security_questions': self.selectors.security_questions,
            'pagination_next': self.selectors.pagination_next,
            'pagination_previous': self.selectors.pagination_previous,
            'breadcrumbs': self.selectors.breadcrumbs
        }
    
    def _strategies_to_dict(self) -> Dict[str, Any]:
        """Convert strategies to dictionary for YAML serialization"""
        return {
            'login_strategy': self.strategies.login_strategy,
            'form_strategy': self.strategies.form_strategy,
            'upload_strategy': self.strategies.upload_strategy,
            'captcha_strategy': self.strategies.captcha_strategy,
            'verification_strategy': self.strategies.verification_strategy,
            'page_load_wait': self.strategies.page_load_wait,
            'element_wait': self.strategies.element_wait,
            'form_submit_wait': self.strategies.form_submit_wait,
            'upload_wait': self.strategies.upload_wait,
            'max_retries': self.strategies.max_retries,
            'retry_delay': self.strategies.retry_delay,
            'exponential_backoff': self.strategies.exponential_backoff
        }
    
    def _features_to_dict(self) -> Dict[str, Any]:
        """Convert features to dictionary for YAML serialization"""
        return {
            'supports_login': self.features.supports_login,
            'supports_guest_application': self.features.supports_guest_application,
            'supports_resume_upload': self.features.supports_resume_upload,
            'supports_cover_letter': self.features.supports_cover_letter,
            'supports_portfolio': self.features.supports_portfolio,
            'supports_salary_info': self.features.supports_salary_info,
            'supports_availability': self.features.supports_availability,
            'supports_custom_questions': self.features.supports_custom_questions,
            'supports_bulk_upload': self.features.supports_bulk_upload,
            'supports_application_tracking': self.features.supports_application_tracking
        }
    
    def get_selector(self, key: str, fallback: str = None) -> str:
        """Get a selector by key with optional fallback"""
        return getattr(self.selectors, key, fallback)
    
    def get_strategy(self, key: str, default: Any = None) -> Any:
        """Get a strategy value by key with optional default"""
        return getattr(self.strategies, key, default)
    
    def get_feature(self, key: str, default: bool = False) -> bool:
        """Get a feature flag by key with optional default"""
        return getattr(self.features, key, default)
    
    def validate(self) -> List[str]:
        """Validate the manifest configuration and return any issues"""
        issues = []
        
        # Check required fields
        if not self.portal_name:
            issues.append("Portal name is required")
        
        if not self.base_url:
            issues.append("Base URL is required")
        
        # Check critical selectors
        if self.features.supports_login and not self.selectors.login_button:
            issues.append("Login button selector required when login is supported")
        
        if not self.selectors.apply_button:
            issues.append("Apply button selector is required")
        
        if self.features.supports_resume_upload and not self.selectors.resume_upload:
            issues.append("Resume upload selector required when resume upload is supported")
        
        # Check strategy consistency
        if self.strategies.login_strategy == "form_based" and not self.selectors.email_field:
            issues.append("Email field selector required for form-based login")
        
        return issues
