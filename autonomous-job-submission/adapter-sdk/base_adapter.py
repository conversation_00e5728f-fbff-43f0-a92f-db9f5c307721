"""
Base adapter class for job portal automation
Provides common interface and functionality for all portal adapters
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from playwright.async_api import Page, BrowserContext
from .manifest import PortalManifest
from .captcha import Captcha<PERSON>andler
from .verification import VerificationEngine

logger = logging.getLogger(__name__)

class SubmissionStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    SUCCESS = "success"
    FAILED = "failed"
    REQUIRES_ATTENTION = "requires_attention"
    CAPTCHA_REQUIRED = "captcha_required"

@dataclass
class SubmissionResult:
    status: SubmissionStatus
    message: str
    application_id: Optional[str] = None
    confirmation_url: Optional[str] = None
    error_details: Optional[str] = None
    screenshots: List[str] = None
    duration: float = 0.0
    artifacts: Dict[str, Any] = None

@dataclass
class UserProfile:
    personal_info: Dict[str, str]
    resume_path: str
    cover_letter_path: Optional[str] = None
    portfolio_url: Optional[str] = None
    linkedin_url: Optional[str] = None
    additional_documents: Dict[str, str] = None

class BaseAdapter(ABC):
    """Abstract base class for job portal adapters"""
    
    def __init__(self, manifest_path: str, config: Dict[str, Any] = None):
        self.manifest = PortalManifest.load(manifest_path)
        self.config = config or {}
        self.captcha_handler = CaptchaHandler(self.config.get('captcha', {}))
        self.verification_engine = VerificationEngine()
        
        self.page: Optional[Page] = None
        self.context: Optional[BrowserContext] = None
        self.start_time: float = 0
        self.current_step: str = ""
        
        # Progress tracking
        self.progress_callback: Optional[callable] = None
        self.checkpoint_callback: Optional[callable] = None
        
    def set_page(self, page: Page, context: BrowserContext):
        """Set the Playwright page and context for this adapter"""
        self.page = page
        self.context = context
    
    def set_callbacks(self, progress_callback: callable = None, checkpoint_callback: callable = None):
        """Set progress and checkpoint callbacks"""
        self.progress_callback = progress_callback
        self.checkpoint_callback = checkpoint_callback
    
    async def submit_application(self, job_url: str, user_profile: UserProfile) -> SubmissionResult:
        """Main entry point for job application submission"""
        
        self.start_time = time.time()
        logger.info(f"Starting application submission for {self.manifest.portal_name}")
        
        try:
            # Step 1: Navigate to job posting
            await self._checkpoint("navigation", "Navigating to job posting")
            await self.navigate_to_job(job_url)
            
            # Step 2: Handle login if required
            if await self._requires_login():
                await self._checkpoint("login", "Logging into portal")
                await self.login()
            
            # Step 3: Start application process
            await self._checkpoint("application_start", "Starting application process")
            await self.start_application()
            
            # Step 4: Fill application form
            await self._checkpoint("form_filling", "Filling application form")
            await self.fill_form(user_profile)
            
            # Step 5: Handle file uploads
            await self._checkpoint("file_upload", "Uploading documents")
            await self.upload_documents(user_profile)
            
            # Step 6: Review and submit
            await self._checkpoint("review", "Reviewing application")
            await self.review_application()
            
            await self._checkpoint("submission", "Submitting application")
            result = await self.submit_form()
            
            # Step 7: Verify submission
            await self._checkpoint("verification", "Verifying submission")
            await self.verify_submission()
            
            duration = time.time() - self.start_time
            result.duration = duration
            
            logger.info(f"Application submitted successfully in {duration:.2f}s")
            return result
            
        except Exception as e:
            duration = time.time() - self.start_time
            logger.error(f"Application submission failed: {e}")
            
            # Take error screenshot
            screenshot_path = await self._take_screenshot("error")
            
            return SubmissionResult(
                status=SubmissionStatus.FAILED,
                message=f"Submission failed: {str(e)}",
                error_details=str(e),
                duration=duration,
                screenshots=[screenshot_path] if screenshot_path else []
            )
    
    @abstractmethod
    async def navigate_to_job(self, job_url: str):
        """Navigate to the specific job posting"""
        pass
    
    @abstractmethod
    async def login(self) -> bool:
        """Handle login process if required"""
        pass
    
    @abstractmethod
    async def start_application(self):
        """Start the application process (click Apply button, etc.)"""
        pass
    
    @abstractmethod
    async def fill_form(self, user_profile: UserProfile):
        """Fill out the application form with user data"""
        pass
    
    @abstractmethod
    async def upload_documents(self, user_profile: UserProfile):
        """Upload resume, cover letter, and other documents"""
        pass
    
    @abstractmethod
    async def review_application(self):
        """Review the application before submission"""
        pass
    
    @abstractmethod
    async def submit_form(self) -> SubmissionResult:
        """Submit the completed application form"""
        pass
    
    @abstractmethod
    async def verify_submission(self) -> bool:
        """Verify that the application was submitted successfully"""
        pass
    
    # Common utility methods
    
    async def _requires_login(self) -> bool:
        """Check if login is required for this portal"""
        login_indicators = self.manifest.selectors.get('login_indicators', [])
        
        for indicator in login_indicators:
            try:
                element = await self.page.query_selector(indicator)
                if element:
                    return True
            except:
                continue
        
        return False
    
    async def _wait_for_element(self, selector: str, timeout: int = 10000) -> bool:
        """Wait for an element to appear on the page"""
        try:
            await self.page.wait_for_selector(selector, timeout=timeout)
            return True
        except:
            return False
    
    async def _click_element(self, selector: str, timeout: int = 10000):
        """Click an element with error handling"""
        try:
            await self.page.click(selector, timeout=timeout)
            await self.page.wait_for_load_state("networkidle", timeout=5000)
        except Exception as e:
            logger.warning(f"Failed to click element {selector}: {e}")
            raise
    
    async def _type_text(self, selector: str, text: str, clear: bool = True):
        """Type text into an input field"""
        try:
            if clear:
                await self.page.fill(selector, "")
            await self.page.type(selector, text, delay=50)
        except Exception as e:
            logger.warning(f"Failed to type into {selector}: {e}")
            raise
    
    async def _select_option(self, selector: str, value: str):
        """Select an option from a dropdown"""
        try:
            await self.page.select_option(selector, value)
        except Exception as e:
            logger.warning(f"Failed to select option {value} in {selector}: {e}")
            raise
    
    async def _upload_file(self, selector: str, file_path: str):
        """Upload a file to a file input"""
        try:
            await self.page.set_input_files(selector, file_path)
            await asyncio.sleep(1)  # Wait for upload to process
        except Exception as e:
            logger.warning(f"Failed to upload file {file_path} to {selector}: {e}")
            raise
    
    async def _handle_captcha(self) -> bool:
        """Handle CAPTCHA if present"""
        captcha_selectors = self.manifest.selectors.get('captcha', [])
        
        for selector in captcha_selectors:
            captcha_element = await self.page.query_selector(selector)
            if captcha_element:
                logger.info("CAPTCHA detected, attempting to solve")
                
                # Use captcha handler
                success = await self.captcha_handler.solve_captcha(self.page, selector)
                if success:
                    logger.info("CAPTCHA solved successfully")
                    return True
                else:
                    logger.warning("CAPTCHA solving failed")
                    return False
        
        return True  # No CAPTCHA found
    
    async def _take_screenshot(self, name: str) -> str:
        """Take a screenshot for debugging/verification"""
        try:
            timestamp = int(time.time())
            screenshot_path = f"/tmp/{self.manifest.portal_name}_{name}_{timestamp}.png"
            await self.page.screenshot(path=screenshot_path, full_page=True)
            return screenshot_path
        except Exception as e:
            logger.warning(f"Failed to take screenshot: {e}")
            return None
    
    async def _checkpoint(self, checkpoint_name: str, description: str):
        """Record a checkpoint for progress tracking"""
        self.current_step = checkpoint_name
        
        logger.info(f"[CHECKPOINT] {checkpoint_name}: {description}")
        print(f"[CHECKPOINT] {checkpoint_name}: {description}")
        
        if self.checkpoint_callback:
            checkpoint_data = {
                "checkpoint": checkpoint_name,
                "description": description,
                "timestamp": time.time(),
                "url": self.page.url if self.page else None,
                "portal": self.manifest.portal_name
            }
            self.checkpoint_callback(checkpoint_data)
    
    async def _progress_update(self, current: int, total: int, message: str = ""):
        """Update progress for the current operation"""
        if self.progress_callback:
            self.progress_callback(current, total, message)
    
    def _get_selector(self, key: str, fallback: str = None) -> str:
        """Get a selector from the manifest with fallback"""
        return self.manifest.selectors.get(key, fallback)
    
    def _get_config(self, key: str, default: Any = None) -> Any:
        """Get a configuration value with default"""
        return self.config.get(key, default)
    
    async def _wait_for_navigation(self, timeout: int = 30000):
        """Wait for page navigation to complete"""
        try:
            await self.page.wait_for_load_state("networkidle", timeout=timeout)
        except Exception as e:
            logger.warning(f"Navigation wait timeout: {e}")
    
    async def _scroll_to_element(self, selector: str):
        """Scroll an element into view"""
        try:
            element = await self.page.query_selector(selector)
            if element:
                await element.scroll_into_view_if_needed()
        except Exception as e:
            logger.warning(f"Failed to scroll to element {selector}: {e}")
    
    async def _wait_for_stable_page(self, timeout: int = 5000):
        """Wait for page to become stable (no network activity)"""
        try:
            await self.page.wait_for_load_state("networkidle", timeout=timeout)
        except:
            pass  # Continue even if timeout
    
    def get_portal_info(self) -> Dict[str, Any]:
        """Get information about this portal adapter"""
        return {
            "name": self.manifest.portal_name,
            "base_url": self.manifest.base_url,
            "version": self.manifest.version,
            "supported_features": self.manifest.features,
            "current_step": self.current_step,
            "duration": time.time() - self.start_time if self.start_time else 0
        }
