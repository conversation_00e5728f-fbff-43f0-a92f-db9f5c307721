package websocket

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/gorilla/websocket"
	"github.com/cvleap/terminald/pkg/types"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		// In production, implement proper origin checking
		return true
	},
	Subprotocols: []string{"terminal"},
}

// <PERSON><PERSON> manages WebSocket connections for terminal sessions
type Handler struct {
	terminalManager types.TerminalManager
	authService     types.AuthService
}

// NewHandler creates a new WebSocket handler
func NewHandler(terminalManager types.TerminalManager, authService types.AuthService) *Handler {
	return &Handler{
		terminalManager: terminalManager,
		authService:     authService,
	}
}

// HandleWebSocket handles WebSocket upgrade and connection
func (h *Handler) HandleWebSocket(w http.ResponseWriter, r *http.Request) {
	// Extract token from Sec-WebSocket-Protocol header
	protocols := r.Header.Get("Sec-WebSocket-Protocol")
	token := ""
	for _, protocol := range strings.Split(protocols, ",") {
		protocol = strings.TrimSpace(protocol)
		if strings.HasPrefix(protocol, "token-") {
			token = strings.TrimPrefix(protocol, "token-")
			break
		}
	}

	if token == "" {
		http.Error(w, "Missing authentication token", http.StatusUnauthorized)
		return
	}

	// Get client IP
	clientIP := getClientIP(r)

	// Validate JWT token
	claims, err := h.authService.ValidateToken(token, clientIP)
	if err != nil {
		log.Printf("Authentication failed: %v", err)
		http.Error(w, "Authentication failed", http.StatusUnauthorized)
		return
	}

	// Get or create terminal session
	session, err := h.terminalManager.GetSession(claims.SessionID)
	if err != nil {
		// Try to create session if it doesn't exist
		session, err = h.terminalManager.CreateSession(r.Context(), claims.SessionID)
		if err != nil {
			log.Printf("Failed to create session: %v", err)
			http.Error(w, "Failed to create session", http.StatusInternalServerError)
			return
		}
	}

	// Upgrade to WebSocket
	conn, err := upgrader.Upgrade(w, r, http.Header{
		"Sec-WebSocket-Protocol": []string{"terminal"},
	})
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}

	// Handle the connection
	h.handleConnection(conn, session, claims)
}

// handleConnection manages a WebSocket connection
func (h *Handler) handleConnection(conn *websocket.Conn, session *types.TerminalSession, claims *types.JWTClaims) {
	defer conn.Close()

	clientID := fmt.Sprintf("%s-%d", claims.UserID, time.Now().UnixNano())
	
	// Create client
	client := &types.WebSocketClient{
		ID:       clientID,
		UserID:   claims.UserID,
		IP:       claims.IP,
		ConnTime: time.Now(),
		Send:     make(chan []byte, 256),
		Done:     make(chan struct{}),
	}

	// Add client to session
	session.Clients[clientID] = client

	// Send ring buffer content to new client
	if bufferData := session.RingBuffer.ReadAll(); len(bufferData) > 0 {
		select {
		case client.Send <- bufferData:
		default:
			log.Printf("Failed to send buffer data to client %s", clientID)
		}
	}

	// Start goroutines for reading and writing
	go h.writePump(conn, client)
	go h.readPump(conn, client, session, claims)

	// Wait for connection to close
	<-client.Done

	// Clean up
	delete(session.Clients, clientID)
	close(client.Send)
}

// writePump handles sending data to WebSocket client
func (h *Handler) writePump(conn *websocket.Conn, client *types.WebSocketClient) {
	ticker := time.NewTicker(54 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case data, ok := <-client.Send:
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := conn.WriteMessage(websocket.BinaryMessage, data); err != nil {
				log.Printf("WebSocket write error: %v", err)
				return
			}

		case <-ticker.C:
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}

		case <-client.Done:
			return
		}
	}
}

// readPump handles receiving data from WebSocket client
func (h *Handler) readPump(conn *websocket.Conn, client *types.WebSocketClient, session *types.TerminalSession, claims *types.JWTClaims) {
	defer close(client.Done)

	conn.SetReadLimit(512)
	conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	conn.SetPongHandler(func(string) error {
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		// Only allow input for advanced users
		if h.authService.IsAdvancedUser(claims.Role) {
			if _, err := session.PTY.Write(message); err != nil {
				log.Printf("Failed to write to PTY: %v", err)
				break
			}
		}
	}
}

// getClientIP extracts the client IP from the request
func getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		ips := strings.Split(xff, ",")
		return strings.TrimSpace(ips[0])
	}

	// Check X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}

	// Fall back to RemoteAddr
	ip := r.RemoteAddr
	if colon := strings.LastIndex(ip, ":"); colon != -1 {
		ip = ip[:colon]
	}
	return ip
}
