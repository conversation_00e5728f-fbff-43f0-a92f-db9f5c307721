package pty

import (
	"context"
	"fmt"
	"io"
	"os"
	"os/exec"
	"sync"
	"time"

	"github.com/creack/pty"
	"github.com/cvleap/terminald/internal/buffer"
	"github.com/cvleap/terminald/pkg/types"
)

// Manager handles PTY sessions and tmux integration
type Manager struct {
	mu       sync.RWMutex
	sessions map[string]*types.TerminalSession
	config   *types.Config
}

// NewManager creates a new PTY manager
func NewManager(config *types.Config) *Manager {
	return &Manager{
		sessions: make(map[string]*types.TerminalSession),
		config:   config,
	}
}

// CreateSession creates a new terminal session with tmux
func (m *Manager) CreateSession(ctx context.Context, sessionID string) (*types.TerminalSession, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Check if session already exists
	if session, exists := m.sessions[sessionID]; exists {
		session.LastAccess = time.Now()
		return session, nil
	}

	// Create tmux session
	cmd := exec.CommandContext(ctx, "tmux", "new-session", "-d", "-s", m.config.TmuxSession+"-"+sessionID)
	
	// Start the PTY
	ptmx, err := pty.Start(cmd)
	if err != nil {
		return nil, fmt.Errorf("failed to start PTY: %w", err)
	}

	// Create ring buffer
	ringBuffer := buffer.NewRingBuffer(m.config.RingBufferSize)

	// Create session
	session := &types.TerminalSession{
		ID:         sessionID,
		PTY:        ptmx,
		RingBuffer: ringBuffer,
		CreatedAt:  time.Now(),
		LastAccess: time.Now(),
		Clients:    make(map[string]*types.WebSocketClient),
	}

	// Start copying PTY output to ring buffer
	go m.copyPTYOutput(session)

	m.sessions[sessionID] = session
	return session, nil
}

// GetSession retrieves an existing session
func (m *Manager) GetSession(sessionID string) (*types.TerminalSession, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	session, exists := m.sessions[sessionID]
	if !exists {
		return nil, fmt.Errorf("session %s not found", sessionID)
	}

	session.LastAccess = time.Now()
	return session, nil
}

// CloseSession closes a terminal session
func (m *Manager) CloseSession(sessionID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	session, exists := m.sessions[sessionID]
	if !exists {
		return fmt.Errorf("session %s not found", sessionID)
	}

	// Close all WebSocket clients
	for _, client := range session.Clients {
		close(client.Done)
	}

	// Close PTY
	if session.PTY != nil {
		session.PTY.Close()
	}

	// Kill tmux session
	exec.Command("tmux", "kill-session", "-t", m.config.TmuxSession+"-"+sessionID).Run()

	delete(m.sessions, sessionID)
	return nil
}

// ListSessions returns all active sessions
func (m *Manager) ListSessions() []*types.TerminalSession {
	m.mu.RLock()
	defer m.mu.RUnlock()

	sessions := make([]*types.TerminalSession, 0, len(m.sessions))
	for _, session := range m.sessions {
		sessions = append(sessions, session)
	}
	return sessions
}

// WriteToSession writes data to a specific session's PTY
func (m *Manager) WriteToSession(sessionID string, data []byte) error {
	session, err := m.GetSession(sessionID)
	if err != nil {
		return err
	}

	_, err = session.PTY.Write(data)
	return err
}

// copyPTYOutput continuously copies PTY output to the ring buffer
func (m *Manager) copyPTYOutput(session *types.TerminalSession) {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("PTY copy goroutine panic for session %s: %v\n", session.ID, r)
		}
	}()

	buffer := make([]byte, 4096)
	for {
		n, err := session.PTY.Read(buffer)
		if err != nil {
			if err != io.EOF {
				fmt.Printf("Error reading from PTY for session %s: %v\n", session.ID, err)
			}
			break
		}

		if n > 0 {
			// Write to ring buffer
			session.RingBuffer.Write(buffer[:n])

			// Broadcast to all connected WebSocket clients
			m.broadcastToClients(session, buffer[:n])
		}
	}
}

// broadcastToClients sends data to all WebSocket clients for a session
func (m *Manager) broadcastToClients(session *types.TerminalSession, data []byte) {
	for clientID, client := range session.Clients {
		select {
		case client.Send <- data:
		default:
			// Client channel is full, remove client
			close(client.Done)
			delete(session.Clients, clientID)
		}
	}
}
