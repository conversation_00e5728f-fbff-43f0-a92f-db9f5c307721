package auth

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/cvleap/terminald/pkg/types"
)

// Service handles JWT authentication
type Service struct {
	secret []byte
}

// NewService creates a new authentication service
func NewService(secret string) *Service {
	return &Service{
		secret: []byte(secret),
	}
}

// ValidateToken validates a JWT token and returns claims
func (s *Service) ValidateToken(tokenString string, clientIP string) (*types.JWTClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.secret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	// Extract claims
	userID, ok := claims["user_id"].(string)
	if !ok {
		return nil, fmt.Errorf("missing or invalid user_id claim")
	}

	sessionID, ok := claims["session_id"].(string)
	if !ok {
		return nil, fmt.Errorf("missing or invalid session_id claim")
	}

	tokenIP, ok := claims["ip"].(string)
	if !ok {
		return nil, fmt.Errorf("missing or invalid ip claim")
	}

	role, ok := claims["role"].(string)
	if !ok {
		role = "user" // default role
	}

	exp, ok := claims["exp"].(float64)
	if !ok {
		return nil, fmt.Errorf("missing or invalid exp claim")
	}

	// Validate IP binding
	if tokenIP != clientIP {
		return nil, fmt.Errorf("token IP mismatch: expected %s, got %s", tokenIP, clientIP)
	}

	// Validate expiration
	if time.Now().Unix() > int64(exp) {
		return nil, fmt.Errorf("token expired")
	}

	return &types.JWTClaims{
		UserID:    userID,
		SessionID: sessionID,
		IP:        tokenIP,
		Role:      role,
		Exp:       int64(exp),
	}, nil
}

// IsAdvancedUser checks if the user has advanced privileges
func (s *Service) IsAdvancedUser(role string) bool {
	return role == "Advanced ✚" || role == "admin"
}

// GenerateToken generates a new JWT token (for testing purposes)
func (s *Service) GenerateToken(userID, sessionID, ip, role string, duration time.Duration) (string, error) {
	claims := jwt.MapClaims{
		"user_id":    userID,
		"session_id": sessionID,
		"ip":         ip,
		"role":       role,
		"exp":        time.Now().Add(duration).Unix(),
		"iat":        time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(s.secret)
}
