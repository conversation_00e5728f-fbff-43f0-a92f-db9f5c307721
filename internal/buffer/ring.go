package buffer

import (
	"sync"
)

// RingBuffer implements a thread-safe circular buffer for terminal output
type RingBuffer struct {
	mu      sync.RWMutex
	data    []byte
	head    int
	tail    int
	full    bool
	maxSize int
}

// NewRingBuffer creates a new ring buffer with the specified maximum size
func NewRingBuffer(maxSize int) *RingBuffer {
	return &RingBuffer{
		data:    make([]byte, maxSize),
		maxSize: maxSize,
	}
}

// Write appends data to the ring buffer
func (rb *RingBuffer) Write(p []byte) (n int, err error) {
	rb.mu.Lock()
	defer rb.mu.Unlock()

	n = len(p)
	for _, b := range p {
		rb.data[rb.head] = b
		rb.head = (rb.head + 1) % rb.maxSize

		if rb.full {
			rb.tail = (rb.tail + 1) % rb.maxSize
		} else if rb.head == rb.tail {
			rb.full = true
		}
	}

	return n, nil
}

// ReadAll returns all data currently in the buffer
func (rb *<PERSON>uffer) ReadAll() []byte {
	rb.mu.RLock()
	defer rb.mu.RUnlock()

	if !rb.full && rb.head == rb.tail {
		return nil
	}

	var result []byte
	if rb.full {
		// Buffer is full, read from tail to end, then from start to head
		result = make([]byte, rb.maxSize)
		copy(result, rb.data[rb.tail:])
		copy(result[rb.maxSize-rb.tail:], rb.data[:rb.head])
	} else {
		// Buffer is not full, read from tail to head
		size := rb.head - rb.tail
		if size < 0 {
			size += rb.maxSize
		}
		result = make([]byte, size)
		if rb.head > rb.tail {
			copy(result, rb.data[rb.tail:rb.head])
		} else {
			copy(result, rb.data[rb.tail:])
			copy(result[rb.maxSize-rb.tail:], rb.data[:rb.head])
		}
	}

	return result
}

// Size returns the current size of data in the buffer
func (rb *RingBuffer) Size() int {
	rb.mu.RLock()
	defer rb.mu.RUnlock()

	if rb.full {
		return rb.maxSize
	}

	size := rb.head - rb.tail
	if size < 0 {
		size += rb.maxSize
	}
	return size
}

// Clear empties the ring buffer
func (rb *RingBuffer) Clear() {
	rb.mu.Lock()
	defer rb.mu.Unlock()

	rb.head = 0
	rb.tail = 0
	rb.full = false
}
