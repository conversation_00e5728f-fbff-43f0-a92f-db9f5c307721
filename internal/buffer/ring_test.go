package buffer

import (
	"bytes"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRingBuffer_Write(t *testing.T) {
	rb := NewRingBuffer(10)

	// Test writing less than buffer size
	data := []byte("hello")
	n, err := rb.Write(data)
	assert.NoError(t, err)
	assert.Equal(t, 5, n)
	assert.Equal(t, 5, rb.<PERSON><PERSON>())

	// Test reading back
	result := rb.ReadAll()
	assert.Equal(t, data, result)
}

func TestRingBuffer_Overflow(t *testing.T) {
	rb := NewRingBuffer(5)

	// Write more data than buffer size
	data1 := []byte("hello")
	data2 := []byte("world")

	rb.Write(data1)
	rb.Write(data2)

	// Should only contain the last 5 bytes
	result := rb.ReadAll()
	assert.Equal(t, []byte("world"), result)
	assert.Equal(t, 5, rb.Size())
}

func TestRingBuffer_PartialOverflow(t *testing.T) {
	rb := NewRingBuffer(8)

	// Write data that partially overflows
	rb.Write([]byte("hello"))   // 5 bytes
	rb.Write([]byte("world!"))  // 6 bytes, total 11 > 8

	// Should contain last 8 bytes: "loworld!"
	result := rb.ReadAll()
	assert.Equal(t, []byte("loworld!"), result)
	assert.Equal(t, 8, rb.Size())
}

func TestRingBuffer_Empty(t *testing.T) {
	rb := NewRingBuffer(10)

	// Empty buffer should return nil
	result := rb.ReadAll()
	assert.Nil(t, result)
	assert.Equal(t, 0, rb.Size())
}

func TestRingBuffer_Clear(t *testing.T) {
	rb := NewRingBuffer(10)

	// Write some data
	rb.Write([]byte("test"))
	assert.Equal(t, 4, rb.Size())

	// Clear buffer
	rb.Clear()
	assert.Equal(t, 0, rb.Size())
	assert.Nil(t, rb.ReadAll())
}

func TestRingBuffer_ConcurrentAccess(t *testing.T) {
	rb := NewRingBuffer(100)

	// Test concurrent writes and reads
	done := make(chan bool, 2)

	// Writer goroutine
	go func() {
		for i := 0; i < 50; i++ {
			rb.Write([]byte("test"))
		}
		done <- true
	}()

	// Reader goroutine
	go func() {
		for i := 0; i < 50; i++ {
			rb.ReadAll()
		}
		done <- true
	}()

	// Wait for both goroutines
	<-done
	<-done

	// Buffer should have some data
	assert.True(t, rb.Size() > 0)
}

func TestRingBuffer_LargeData(t *testing.T) {
	rb := NewRingBuffer(1024)

	// Write large chunk of data
	largeData := bytes.Repeat([]byte("A"), 2048)
	rb.Write(largeData)

	// Should only contain last 1024 bytes
	result := rb.ReadAll()
	assert.Equal(t, 1024, len(result))
	assert.Equal(t, bytes.Repeat([]byte("A"), 1024), result)
}

func TestRingBuffer_MultipleSmallWrites(t *testing.T) {
	rb := NewRingBuffer(10)

	// Write multiple small chunks
	chunks := [][]byte{
		[]byte("a"),
		[]byte("bb"),
		[]byte("ccc"),
		[]byte("dddd"),
	}

	for _, chunk := range chunks {
		rb.Write(chunk)
	}

	// Should contain "abbcccdddd" (10 bytes)
	result := rb.ReadAll()
	assert.Equal(t, []byte("abbcccdddd"), result)
	assert.Equal(t, 10, rb.Size())
}

func BenchmarkRingBuffer_Write(b *testing.B) {
	rb := NewRingBuffer(1024 * 1024) // 1MB buffer
	data := []byte("benchmark test data")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		rb.Write(data)
	}
}

func BenchmarkRingBuffer_ReadAll(b *testing.B) {
	rb := NewRingBuffer(1024 * 1024) // 1MB buffer
	data := bytes.Repeat([]byte("test"), 256*1024) // Fill buffer
	rb.Write(data)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		rb.ReadAll()
	}
}
