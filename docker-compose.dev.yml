version: '3.8'

services:
  # Main sandbox application
  sandbox-app:
    build:
      context: .
      dockerfile: infra/docker/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - PORT=8000
      - ENABLE_TERMINAL=false
      - JWT_SECRET=dev-secret-key-change-in-production
    volumes:
      - ./apps:/app/apps:ro
    networks:
      - cvleap-dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Terminal daemon (only when enabled)
  terminald:
    build:
      context: .
      dockerfile: infra/docker/Dockerfile
    command: ["terminald"]
    ports:
      - "7000:7000"
    environment:
      - PORT=7000
      - ENABLE_TERMINAL=true
      - JWT_SECRET=dev-secret-key-change-in-production
      - RING_BUFFER_SIZE=10485760
      - TMUX_SESSION=cvleap-dev
    volumes:
      - terminal-data:/app/data
    networks:
      - cvleap-dev
    depends_on:
      - sandbox-app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API proxy for terminal access
  api-proxy:
    build:
      context: ./apps/api
      dockerfile: Dockerfile
    ports:
      - "9000:9000"
    environment:
      - PORT=9000
      - JWT_SECRET=dev-secret-key-change-in-production
      - JWT_EXPIRY_SECONDS=60
      - KUBERNETES_NAMESPACE=cvleap-dev
    networks:
      - cvleap-dev
    depends_on:
      - terminald
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for session storage (production would use managed Redis)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - cvleap-dev
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Development web server for React components
  web-dev:
    build:
      context: ./apps/web
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://localhost:9000
      - REACT_APP_WS_URL=ws://localhost:7000
    volumes:
      - ./apps/web/src:/app/src:ro
      - ./apps/web/public:/app/public:ro
    networks:
      - cvleap-dev
    depends_on:
      - api-proxy

  # Nginx reverse proxy for development
  nginx-dev:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./infra/nginx/dev.conf:/etc/nginx/nginx.conf:ro
    networks:
      - cvleap-dev
    depends_on:
      - web-dev
      - api-proxy
      - terminald

volumes:
  terminal-data:
    driver: local
  redis-data:
    driver: local

networks:
  cvleap-dev:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
