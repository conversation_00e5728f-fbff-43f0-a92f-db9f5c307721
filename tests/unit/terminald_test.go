package tests

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/cvleap/terminald/internal/auth"
	"github.com/cvleap/terminald/internal/buffer"
	"github.com/cvleap/terminald/internal/pty"
	"github.com/cvleap/terminald/internal/websocket"
	"github.com/cvleap/terminald/pkg/types"
)

const testJWTSecret = "test-secret-key"

func TestRingBufferIntegration(t *testing.T) {
	rb := buffer.NewRingBuffer(100)

	// Test writing and reading
	data := []byte("Hello, World!")
	n, err := rb.Write(data)
	assert.NoError(t, err)
	assert.Equal(t, len(data), n)

	result := rb.ReadAll()
	assert.Equal(t, data, result)

	// Test overflow
	largeData := make([]byte, 150)
	for i := range largeData {
		largeData[i] = byte('A' + (i % 26))
	}

	rb.Write(largeData)
	result = rb.ReadAll()
	assert.Equal(t, 100, len(result))
	assert.Equal(t, largeData[50:], result) // Should contain last 100 bytes
}

func TestJWTAuthentication(t *testing.T) {
	authService := auth.NewService(testJWTSecret)

	// Generate a valid token
	token, err := authService.GenerateToken("user123", "session456", "***********", "user", time.Minute)
	require.NoError(t, err)

	// Validate the token
	claims, err := authService.ValidateToken(token, "***********")
	require.NoError(t, err)
	assert.Equal(t, "user123", claims.UserID)
	assert.Equal(t, "session456", claims.SessionID)
	assert.Equal(t, "***********", claims.IP)
	assert.Equal(t, "user", claims.Role)

	// Test IP mismatch
	_, err = authService.ValidateToken(token, "***********")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "IP mismatch")

	// Test expired token
	expiredToken, err := authService.GenerateToken("user123", "session456", "***********", "user", -time.Minute)
	require.NoError(t, err)

	_, err = authService.ValidateToken(expiredToken, "***********")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "expired")
}

func TestAdvancedUserPermissions(t *testing.T) {
	authService := auth.NewService(testJWTSecret)

	// Test regular user
	assert.False(t, authService.IsAdvancedUser("user"))
	assert.False(t, authService.IsAdvancedUser("basic"))

	// Test advanced users
	assert.True(t, authService.IsAdvancedUser("Advanced ✚"))
	assert.True(t, authService.IsAdvancedUser("admin"))
}

func TestPTYManagerBasics(t *testing.T) {
	config := &types.Config{
		RingBufferSize: 1024,
		TmuxSession:    "test",
	}

	manager := pty.NewManager(config)

	// Test session creation
	ctx := context.Background()
	session, err := manager.CreateSession(ctx, "test-session")
	require.NoError(t, err)
	assert.Equal(t, "test-session", session.ID)
	assert.NotNil(t, session.RingBuffer)
	assert.NotNil(t, session.PTY)

	// Test session retrieval
	retrievedSession, err := manager.GetSession("test-session")
	require.NoError(t, err)
	assert.Equal(t, session.ID, retrievedSession.ID)

	// Test session listing
	sessions := manager.ListSessions()
	assert.Len(t, sessions, 1)
	assert.Equal(t, "test-session", sessions[0].ID)

	// Test session cleanup
	err = manager.CloseSession("test-session")
	assert.NoError(t, err)

	// Session should no longer exist
	_, err = manager.GetSession("test-session")
	assert.Error(t, err)
}

func TestWebSocketHandlerIntegration(t *testing.T) {
	// Create test services
	config := &types.Config{
		RingBufferSize: 1024,
		TmuxSession:    "test",
	}
	terminalManager := pty.NewManager(config)
	authService := auth.NewService(testJWTSecret)
	wsHandler := websocket.NewHandler(terminalManager, authService)

	// Create test server
	server := httptest.NewServer(http.HandlerFunc(wsHandler.HandleWebSocket))
	defer server.Close()

	// Generate valid JWT token
	token, err := authService.GenerateToken("user123", "test-session", "127.0.0.1", "Advanced ✚", time.Minute)
	require.NoError(t, err)

	// Create WebSocket connection
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/ws"
	dialer := websocket.Dialer{}
	headers := http.Header{}
	headers.Set("Sec-WebSocket-Protocol", "token-"+token)

	conn, _, err := dialer.Dial(wsURL, headers)
	require.NoError(t, err)
	defer conn.Close()

	// Test connection is established
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	_, message, err := conn.ReadMessage()

	// Should receive initial buffer content (may be empty)
	if err == nil {
		t.Logf("Received initial message: %s", string(message))
	}

	// Test sending data (for advanced users)
	testInput := "echo 'test'\n"
	err = conn.WriteMessage(websocket.TextMessage, []byte(testInput))
	assert.NoError(t, err)

	// Note: Full PTY testing would require more complex setup
	// This test verifies the WebSocket connection works
}

func TestCostBudgetCompliance(t *testing.T) {
	// Test that terminald binary size is reasonable
	// This would be run as part of CI to ensure we stay within budget

	config := &types.Config{
		RingBufferSize: 10 * 1024 * 1024, // 10MB
		TmuxSession:    "test",
	}

	// Test memory usage of core components
	rb := buffer.NewRingBuffer(config.RingBufferSize)

	// Write some data to buffer
	testData := make([]byte, 1024)
	for i := 0; i < 100; i++ {
		rb.Write(testData)
	}

	// Buffer should not exceed configured size
	assert.LessOrEqual(t, rb.Size(), config.RingBufferSize)

	// Test that manager doesn't leak memory
	manager := pty.NewManager(config)

	// Create and close multiple sessions
	ctx := context.Background()
	for i := 0; i < 10; i++ {
		sessionID := fmt.Sprintf("test-session-%d", i)
		session, err := manager.CreateSession(ctx, sessionID)
		require.NoError(t, err)

		// Immediately close to test cleanup
		err = manager.CloseSession(sessionID)
		assert.NoError(t, err)
	}

	// Should have no active sessions
	sessions := manager.ListSessions()
	assert.Empty(t, sessions)
}

func TestSecurityConstraints(t *testing.T) {
	authService := auth.NewService(testJWTSecret)

	// Test that tokens are properly IP-bound
	token, err := authService.GenerateToken("user123", "session456", "***********", "user", time.Minute)
	require.NoError(t, err)

	// Should work with correct IP
	_, err = authService.ValidateToken(token, "***********")
	assert.NoError(t, err)

	// Should fail with different IP
	_, err = authService.ValidateToken(token, "********")
	assert.Error(t, err)

	// Test that tokens have reasonable expiry
	shortToken, err := authService.GenerateToken("user123", "session456", "***********", "user", time.Second)
	require.NoError(t, err)

	// Wait for token to expire
	time.Sleep(2 * time.Second)

	_, err = authService.ValidateToken(shortToken, "***********")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "expired")
}

func BenchmarkRingBufferWrite(b *testing.B) {
	rb := buffer.NewRingBuffer(1024 * 1024) // 1MB
	data := []byte("benchmark test data for ring buffer performance")

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		rb.Write(data)
	}
}

func BenchmarkJWTValidation(b *testing.B) {
	authService := auth.NewService(testJWTSecret)
	token, _ := authService.GenerateToken("user123", "session456", "***********", "user", time.Hour)

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		authService.ValidateToken(token, "***********")
	}
}
