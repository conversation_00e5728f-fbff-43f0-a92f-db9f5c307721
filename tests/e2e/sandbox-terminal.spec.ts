import { test, expect, Page } from '@playwright/test';

// Test configuration
const TEST_SESSION_ID = 'test-session-' + Date.now();
const TEST_USER_ID = 'test-user-123';
const TERMINAL_ATTACH_TIMEOUT = 500; // ms
const MIN_BACKSCROLL_LINES = 2000;

test.describe('Sandbox Terminal Feature', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    
    // Mock API responses for testing
    await page.route('/api/sandbox/*/watch', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            websocket_url: 'ws://localhost:7000/ws',
            token: 'mock-jwt-token',
            expires_in: 60,
            protocol: 'terminal',
            session_id: TEST_SESSION_ID
          })
        });
      }
    });

    await page.route('/api/sandbox/*/status', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          session_id: TEST_SESSION_ID,
          application: {
            status: 'running',
            overall_progress: 0.5
          },
          terminal: {
            available: true,
            clients: 0
          }
        })
      });
    });
  });

  test('headless flow with ENABLE_TERMINAL=false should not expose WebSocket port', async () => {
    // Set environment to disable terminal
    process.env.ENABLE_TERMINAL = 'false';
    
    // Try to connect to terminal port - should fail
    const response = await page.request.get('http://localhost:7000/health').catch(() => null);
    expect(response).toBeNull();
    
    // Main application should still be accessible
    const appResponse = await page.request.get('http://localhost:8000/health');
    expect(appResponse.status()).toBe(200);
  });

  test('terminal attachment latency should be under 500ms', async () => {
    // Navigate to sandbox page
    await page.goto('/sandbox/' + TEST_SESSION_ID);
    
    // Click "Watch Terminal" button
    const startTime = Date.now();
    await page.click('[data-testid="watch-terminal-button"]');
    
    // Wait for terminal to be connected
    await page.waitForSelector('[data-testid="terminal-connected"]', { timeout: 1000 });
    
    const attachTime = Date.now() - startTime;
    expect(attachTime).toBeLessThan(TERMINAL_ATTACH_TIMEOUT);
  });

  test('terminal should support back-scroll of at least 2000 lines', async () => {
    // Mock WebSocket with large buffer content
    await page.addInitScript(() => {
      const originalWebSocket = window.WebSocket;
      window.WebSocket = class extends originalWebSocket {
        constructor(url: string, protocols?: string | string[]) {
          super(url, protocols);
          
          // Simulate connection and send large buffer
          setTimeout(() => {
            if (this.onopen) this.onopen(new Event('open'));
            
            // Send 3000 lines of mock terminal output
            const mockOutput = Array.from({ length: 3000 }, (_, i) => 
              `Line ${i + 1}: Mock terminal output for testing back-scroll\n`
            ).join('');
            
            if (this.onmessage) {
              this.onmessage(new MessageEvent('message', { data: mockOutput }));
            }
          }, 100);
        }
      };
    });

    await page.goto('/sandbox/' + TEST_SESSION_ID);
    await page.click('[data-testid="watch-terminal-button"]');
    
    // Wait for terminal content to load
    await page.waitForTimeout(500);
    
    // Check if we can scroll up to see earlier content
    const terminalElement = page.locator('[data-testid="terminal-content"]');
    await terminalElement.press('PageUp');
    await terminalElement.press('PageUp');
    await terminalElement.press('PageUp');
    
    // Verify we can see early line numbers
    const content = await terminalElement.textContent();
    expect(content).toContain('Line 1:');
    expect(content).toContain('Line 2:');
  });

  test('pause and resume functionality with Workday sandbox', async () => {
    // Mock job application in progress
    await page.route('/rpc/status/*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          session_id: TEST_SESSION_ID,
          status: 'running',
          overall_progress: 0.3,
          steps: [
            { name: 'navigate_to_job_site', status: 'completed', progress: 1.0 },
            { name: 'fill_application_form', status: 'running', progress: 0.6 },
            { name: 'submit_application', status: 'pending', progress: 0.0 }
          ]
        })
      });
    });

    await page.route('/rpc/signal/*', async (route) => {
      const body = await route.request().postDataJSON();
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'signal_sent',
          action: body.action
        })
      });
    });

    await page.goto('/sandbox/' + TEST_SESSION_ID);
    
    // Start watching terminal
    await page.click('[data-testid="watch-terminal-button"]');
    await page.waitForSelector('[data-testid="terminal-connected"]');
    
    // Pause the application
    await page.click('[data-testid="pause-button"]');
    
    // Verify pause signal was sent
    const pauseRequest = await page.waitForRequest(req => 
      req.url().includes('/rpc/signal/') && 
      req.postDataJSON()?.action === 'pause'
    );
    expect(pauseRequest).toBeTruthy();
    
    // Edit selectors.yaml (simulate user interaction)
    await page.click('[data-testid="edit-selectors-button"]');
    await page.fill('[data-testid="selector-input"]', '.workday-submit-btn');
    await page.click('[data-testid="save-selectors-button"]');
    
    // Resume the application
    await page.click('[data-testid="resume-button"]');
    
    // Verify resume signal was sent
    const resumeRequest = await page.waitForRequest(req => 
      req.url().includes('/rpc/signal/') && 
      req.postDataJSON()?.action === 'resume'
    );
    expect(resumeRequest).toBeTruthy();
    
    // Wait for successful completion
    await page.waitForSelector('[data-testid="status-completed"]', { timeout: 10000 });
  });

  test('advanced user can send input, regular user cannot', async () => {
    // Test with regular user
    await page.goto('/sandbox/' + TEST_SESSION_ID + '?role=user');
    await page.click('[data-testid="watch-terminal-button"]');
    await page.waitForSelector('[data-testid="terminal-connected"]');
    
    // Should show read-only indicator
    await expect(page.locator('[data-testid="readonly-indicator"]')).toBeVisible();
    
    // Terminal input should be disabled
    const terminalInput = page.locator('[data-testid="terminal-content"]');
    await terminalInput.type('ls -la');
    
    // No input should be sent to WebSocket
    // (This would need WebSocket mocking to verify)
    
    // Test with advanced user
    await page.goto('/sandbox/' + TEST_SESSION_ID + '?role=Advanced%20✚');
    await page.click('[data-testid="watch-terminal-button"]');
    await page.waitForSelector('[data-testid="terminal-connected"]');
    
    // Should not show read-only indicator
    await expect(page.locator('[data-testid="readonly-indicator"]')).not.toBeVisible();
    
    // Terminal input should be enabled
    await terminalInput.type('echo "test"');
    // Input should be sent to WebSocket (would need mocking to verify)
  });

  test('hotkey ⌘K captures terminal pane', async () => {
    await page.goto('/sandbox/' + TEST_SESSION_ID);
    await page.click('[data-testid="watch-terminal-button"]');
    await page.waitForSelector('[data-testid="terminal-connected"]');
    
    // Mock clipboard API
    await page.addInitScript(() => {
      Object.assign(navigator, {
        clipboard: {
          writeText: (text: string) => {
            (window as any).clipboardContent = text;
            return Promise.resolve();
          }
        }
      });
    });
    
    // Press ⌘K (or Ctrl+K on non-Mac)
    await page.keyboard.press(process.platform === 'darwin' ? 'Meta+k' : 'Control+k');
    
    // Verify clipboard was written to
    const clipboardContent = await page.evaluate(() => (window as any).clipboardContent);
    expect(clipboardContent).toBeTruthy();
    expect(typeof clipboardContent).toBe('string');
  });

  test('network policy restricts egress to job domains only', async () => {
    // This test would need to be run in a Kubernetes environment
    // with actual NetworkPolicy enforcement
    test.skip(process.env.CI !== 'true', 'Requires Kubernetes environment');
    
    // Attempt to access allowed job domain
    const allowedResponse = await page.request.get('https://workday.com');
    expect(allowedResponse.status()).toBeLessThan(400);
    
    // Attempt to access blocked domain
    try {
      await page.request.get('https://malicious-site.com', { timeout: 5000 });
      expect(false).toBe(true); // Should not reach here
    } catch (error) {
      // Connection should be blocked by NetworkPolicy
      expect(error).toBeTruthy();
    }
  });

  test('cost budget validation - idle resources under 5 MiB RSS', async () => {
    // This test would need access to container metrics
    test.skip(process.env.CI !== 'true', 'Requires container metrics access');
    
    // Start terminal service but don't connect any clients
    process.env.ENABLE_TERMINAL = 'true';
    
    // Wait for service to stabilize
    await page.waitForTimeout(5000);
    
    // Check memory usage (would need actual metrics endpoint)
    const metricsResponse = await page.request.get('/metrics');
    const metricsText = await metricsResponse.text();
    
    // Parse memory usage from metrics
    const memoryMatch = metricsText.match(/process_resident_memory_bytes (\d+)/);
    if (memoryMatch) {
      const memoryBytes = parseInt(memoryMatch[1]);
      const memoryMiB = memoryBytes / (1024 * 1024);
      expect(memoryMiB).toBeLessThan(5);
    }
  });

  test.afterEach(async () => {
    await page.close();
  });
});
