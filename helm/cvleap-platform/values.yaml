# CVLeap Platform Configuration
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""
  
# Platform Configuration
platform:
  environment: production  # development, staging, production
  region: us-east-1
  costBudget:
    dailyLimit: 100.0  # USD
    perSubmissionTarget: 0.002  # USD
  
# Runtime Configuration
runtimes:
  dockerInPod:
    enabled: true
    image:
      repository: cvleap/sandbox
      tag: "1.0.0"
      pullPolicy: IfNotPresent
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 500m
        memory: 256Mi
    startupTimeTarget: 1000ms  # 1 second
    
  firecracker:
    enabled: false  # Premium feature
    image:
      repository: cvleap/firecracker-sandbox
      tag: "1.0.0"
    resources:
      requests:
        cpu: 200m
        memory: 256Mi
      limits:
        cpu: 1000m
        memory: 512Mi
    startupTimeTarget: 800ms
    
  kubevirt:
    enabled: false  # Fallback option
    resources:
      requests:
        cpu: 500m
        memory: 512Mi
      limits:
        cpu: 2000m
        memory: 1Gi
    startupTimeTarget: 2000ms

# Sandbox Controller
controller:
  enabled: true
  replicaCount: 2
  image:
    repository: cvleap/controller
    tag: "1.0.0"
    pullPolicy: IfNotPresent
  
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 500m
      memory: 256Mi
  
  # Controller configuration
  config:
    reconcileInterval: 30s
    maxConcurrentReconciles: 10
    leaderElection: true
    
  # RBAC
  rbac:
    create: true
    rules:
      - apiGroups: ["cvleap.io"]
        resources: ["sandboxrequests"]
        verbs: ["*"]
      - apiGroups: [""]
        resources: ["pods", "secrets"]
        verbs: ["*"]
      - apiGroups: ["networking.k8s.io"]
        resources: ["networkpolicies"]
        verbs: ["*"]

# Terminal Service
terminal:
  enabled: true
  image:
    repository: cvleap/terminald
    tag: "1.0.0"
    pullPolicy: IfNotPresent
  
  service:
    type: ClusterIP
    port: 7000
  
  config:
    ringBufferSize: 10485760  # 10MB
    jwtSecret: ""  # Set via secret
    
  resources:
    requests:
      cpu: 50m
      memory: 32Mi
    limits:
      cpu: 200m
      memory: 128Mi

# Temporal Workflow Engine
temporal:
  enabled: true
  server:
    replicaCount: 1
    config:
      persistence:
        defaultStore: default
        visibilityStore: visibility
        datastores:
          default:
            driver: "postgres"
            postgres:
              user: temporal
              password: temporal
              host: postgresql
              port: 5432
              database: temporal
              maxConns: 20
              maxConnLifetime: "1h"
          visibility:
            driver: "postgres"
            postgres:
              user: temporal
              password: temporal
              host: postgresql
              port: 5432
              database: temporal_visibility
              maxConns: 10
              maxConnLifetime: "1h"
  
  web:
    enabled: true
    service:
      type: ClusterIP
      port: 8080
  
  # Temporal worker configuration
  worker:
    enabled: true
    replicaCount: 2
    image:
      repository: cvleap/temporal-worker
      tag: "1.0.0"
    
    config:
      taskQueue: cvleap-job-submission
      maxConcurrentActivities: 10
      maxConcurrentWorkflows: 100

# Security Configuration
security:
  # Sealed Secrets
  sealedSecrets:
    enabled: true
    keyrenewperiod: 720h  # 30 days
    
  # Network Policies
  networkPolicies:
    enabled: true
    defaultDeny: true
    allowedDomains:
      - workday.com
      - lever.co
      - greenhouse.io
      - bamboohr.com
      - linkedin.com
      - indeed.com
      - recaptcha.net
      - hcaptcha.com
  
  # Pod Security
  podSecurityPolicy:
    enabled: true
    allowPrivilegeEscalation: false
    runAsNonRoot: true
    runAsUser: 1001
    fsGroup: 1001
  
  # Secret Management
  secretManager:
    enabled: true
    image:
      repository: cvleap/secret-manager
      tag: "1.0.0"
    
    config:
      defaultTTL: 3600  # 1 hour
      maxTTL: 86400     # 24 hours
      cleanupInterval: 300  # 5 minutes

# Observability Stack
observability:
  # Prometheus
  prometheus:
    enabled: true
    server:
      retention: "15d"
      resources:
        requests:
          cpu: 500m
          memory: 512Mi
        limits:
          cpu: 2000m
          memory: 2Gi
    
    alertmanager:
      enabled: true
      config:
        global:
          smtp_smarthost: 'localhost:587'
          smtp_from: '<EMAIL>'
        route:
          group_by: ['alertname']
          group_wait: 10s
          group_interval: 10s
          repeat_interval: 1h
          receiver: 'web.hook'
        receivers:
        - name: 'web.hook'
          webhook_configs:
          - url: 'http://cvleap-api:8080/webhooks/alerts'
  
  # Grafana
  grafana:
    enabled: true
    adminPassword: ""  # Set via secret
    
    dashboardProviders:
      dashboardproviders.yaml:
        apiVersion: 1
        providers:
        - name: 'cvleap'
          orgId: 1
          folder: 'CVLeap'
          type: file
          disableDeletion: false
          editable: true
          options:
            path: /var/lib/grafana/dashboards/cvleap
    
    dashboards:
      cvleap:
        cvleap-platform:
          gnetId: null
          datasource: Prometheus
          json: |
            # Dashboard JSON content from grafana-dashboard.json
    
    datasources:
      datasources.yaml:
        apiVersion: 1
        datasources:
        - name: Prometheus
          type: prometheus
          url: http://prometheus-server:80
          access: proxy
          isDefault: true
        - name: Loki
          type: loki
          url: http://loki:3100
          access: proxy
        - name: Jaeger
          type: jaeger
          url: http://jaeger-query:16686
          access: proxy
  
  # Loki
  loki:
    enabled: true
    config:
      auth_enabled: false
      server:
        http_listen_port: 3100
      limits_config:
        ingestion_rate_mb: 16
        ingestion_burst_size_mb: 32
        per_stream_rate_limit: 8MB
        per_stream_rate_limit_burst: 16MB
    
    persistence:
      enabled: true
      size: 10Gi
  
  # Jaeger
  jaeger:
    enabled: true
    provisionDataStore:
      cassandra: false
      elasticsearch: true
    
    elasticsearch:
      replicas: 1
      minimumMasterNodes: 1
      resources:
        requests:
          cpu: 500m
          memory: 1Gi
        limits:
          cpu: 1000m
          memory: 2Gi
    
    collector:
      service:
        otlp:
          grpc:
            port: 14250
          http:
            port: 14268
    
    query:
      service:
        type: ClusterIP
        port: 16686

# Autoscaling
autoscaling:
  enabled: true
  
  # Horizontal Pod Autoscaler
  hpa:
    controller:
      enabled: true
      minReplicas: 2
      maxReplicas: 10
      targetCPUUtilizationPercentage: 70
      targetMemoryUtilizationPercentage: 80
    
    worker:
      enabled: true
      minReplicas: 2
      maxReplicas: 20
      targetCPUUtilizationPercentage: 80
  
  # Vertical Pod Autoscaler
  vpa:
    enabled: false
    updateMode: "Auto"
  
  # Cluster Autoscaler
  clusterAutoscaler:
    enabled: true
    nodeGroups:
      - name: cvleap-workers
        minSize: 2
        maxSize: 50
        instanceType: t3.medium

# Networking
networking:
  # Service Mesh (Istio)
  serviceMesh:
    enabled: false
    mtls: true
    
  # Ingress
  ingress:
    enabled: true
    className: nginx
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    hosts:
      - host: cvleap.example.com
        paths:
          - path: /
            pathType: Prefix
            service:
              name: cvleap-api
              port: 80
          - path: /terminal
            pathType: Prefix
            service:
              name: cvleap-terminal
              port: 7000
    
    tls:
      - secretName: cvleap-tls
        hosts:
          - cvleap.example.com

# Storage
storage:
  # Persistent Volumes
  persistence:
    enabled: true
    storageClass: ""
    accessMode: ReadWriteOnce
    size: 10Gi
  
  # Object Storage (S3-compatible)
  objectStorage:
    enabled: true
    provider: aws  # aws, gcp, azure, minio
    bucket: cvleap-artifacts
    region: us-east-1

# External Dependencies
external:
  # PostgreSQL for Temporal
  postgresql:
    enabled: true
    auth:
      postgresPassword: ""  # Set via secret
      username: temporal
      password: ""  # Set via secret
      database: temporal
    
    primary:
      persistence:
        enabled: true
        size: 20Gi
  
  # Redis for caching
  redis:
    enabled: true
    auth:
      enabled: true
      password: ""  # Set via secret
    
    master:
      persistence:
        enabled: true
        size: 8Gi

# Development/Testing
development:
  enabled: false
  
  # Mock services for testing
  mockServices:
    enabled: false
    jobSites:
      - workday.com
      - lever.co
  
  # Debug mode
  debug:
    enabled: false
    logLevel: debug
    
  # Local development
  localDev:
    enabled: false
    hostPaths:
      - /tmp/cvleap-dev
