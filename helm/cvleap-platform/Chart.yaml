apiVersion: v2
name: cvleap-platform
description: CVLeap Micro-VM/Container Hybrid Platform for Automated Job Applications
type: application
version: 1.0.0
appVersion: "1.0.0"

keywords:
  - cvleap
  - job-applications
  - automation
  - micro-vm
  - containers
  - temporal
  - observability

home: https://cvleap.com
sources:
  - https://github.com/cvleap/platform

maintainers:
  - name: CVLeap Engineering Team
    email: <EMAIL>
    url: https://cvleap.com

dependencies:
  - name: temporal
    version: "0.31.0"
    repository: https://go.temporal.io/helm-charts
    condition: temporal.enabled
  
  - name: prometheus
    version: "25.8.0"
    repository: https://prometheus-community.github.io/helm-charts
    condition: observability.prometheus.enabled
  
  - name: grafana
    version: "7.0.19"
    repository: https://grafana.github.io/helm-charts
    condition: observability.grafana.enabled
  
  - name: loki
    version: "5.41.4"
    repository: https://grafana.github.io/helm-charts
    condition: observability.loki.enabled
  
  - name: jaeger
    version: "0.71.14"
    repository: https://jaegertracing.github.io/helm-charts
    condition: observability.jaeger.enabled
  
  - name: sealed-secrets
    version: "2.13.3"
    repository: https://bitnami-labs.github.io/sealed-secrets
    condition: security.sealedSecrets.enabled

annotations:
  category: Application Platform
  licenses: Apache-2.0
  images: |
    - name: cvleap-sandbox
      image: cvleap/sandbox:1.0.0
    - name: cvleap-controller
      image: cvleap/controller:1.0.0
    - name: cvleap-terminal
      image: cvleap/terminald:1.0.0
    - name: cvleap-secret-manager
      image: cvleap/secret-manager:1.0.0
