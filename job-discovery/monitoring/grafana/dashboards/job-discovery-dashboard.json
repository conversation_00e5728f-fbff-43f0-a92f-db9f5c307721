{"dashboard": {"id": null, "title": "Job Discovery System - Data Freshness & Health", "tags": ["job-discovery", "scraping", "data-quality"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-24h", "to": "now"}, "panels": [{"id": 1, "title": "System Overview", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "sum(job_discovery_jobs_total)", "legendFormat": "Total Jobs", "refId": "A"}, {"expr": "sum(job_discovery_jobs_active)", "legendFormat": "Active Jobs", "refId": "B"}, {"expr": "sum(rate(job_discovery_jobs_scraped_total[5m])) * 60", "legendFormat": "Jobs/min", "refId": "C"}, {"expr": "avg(job_discovery_data_freshness_minutes)", "legendFormat": "Avg Freshness (min)", "refId": "D"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "horizontal", "textMode": "auto", "colorMode": "value", "graphMode": "area", "justifyMode": "auto"}}, {"id": 2, "title": "Data Freshness by Source", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "job_discovery_data_freshness_minutes", "legendFormat": "{{source}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "m", "color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 30}, {"color": "red", "value": 60}]}}}, "options": {"tooltip": {"mode": "single", "sort": "none"}, "legend": {"displayMode": "list", "placement": "bottom"}}}, {"id": 3, "title": "Scraping Success Rate", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "rate(job_discovery_scraping_success_total[5m]) / rate(job_discovery_scraping_attempts_total[5m]) * 100", "legendFormat": "{{source}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "color": {"mode": "palette-classic"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}}}}, {"id": 4, "title": "Jobs by Source", "type": "piechart", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 16}, "targets": [{"expr": "job_discovery_jobs_active", "legendFormat": "{{source}}", "refId": "A"}], "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "pieType": "pie", "tooltip": {"mode": "single", "sort": "none"}, "legend": {"displayMode": "list", "placement": "right"}, "displayLabels": ["name", "value"]}}, {"id": 5, "title": "Queue Status", "type": "table", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 16}, "targets": [{"expr": "job_discovery_queue_waiting", "legendFormat": "{{queue}}_waiting", "refId": "A"}, {"expr": "job_discovery_queue_active", "legendFormat": "{{queue}}_active", "refId": "B"}, {"expr": "job_discovery_queue_completed", "legendFormat": "{{queue}}_completed", "refId": "C"}, {"expr": "job_discovery_queue_failed", "legendFormat": "{{queue}}_failed", "refId": "D"}], "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"queue": "Queue", "Value #A": "Waiting", "Value #B": "Active", "Value #C": "Completed", "Value #D": "Failed"}}}]}, {"id": 6, "title": "Data Quality Score", "type": "gauge", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 16}, "targets": [{"expr": "avg(job_discovery_quality_score)", "legendFormat": "Overall Quality", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "min": 0, "max": 1, "color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.7}, {"color": "green", "value": 0.85}]}}}, "options": {"orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "showThresholdLabels": false, "showThresholdMarkers": true}}, {"id": 7, "title": "Processing Pipeline Performance", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "rate(job_discovery_processing_duration_seconds_sum[5m]) / rate(job_discovery_processing_duration_seconds_count[5m])", "legendFormat": "Avg Processing Time", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(job_discovery_processing_duration_seconds_bucket[5m]))", "legendFormat": "95th Percentile", "refId": "B"}, {"expr": "rate(job_discovery_jobs_processed_total[5m]) * 60", "legendFormat": "Jobs Processed/min", "refId": "C"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "palette-classic"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Jobs Processed/min"}, "properties": [{"id": "unit", "value": "short"}, {"id": "custom.axisPlacement", "value": "right"}]}]}}, {"id": 8, "title": "Error Rate by Stage", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "rate(job_discovery_errors_total[5m]) * 100", "legendFormat": "{{stage}} - {{error_type}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineWidth": 2, "fillOpacity": 10}}}}, {"id": 9, "title": "Storage & Search Performance", "type": "timeseries", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}, "targets": [{"expr": "job_discovery_postgres_connections_active", "legendFormat": "Active DB Connections", "refId": "A"}, {"expr": "job_discovery_elasticsearch_index_size_bytes / 1024 / 1024", "legendFormat": "ES Index Size (MB)", "refId": "B"}, {"expr": "rate(job_discovery_search_requests_total[5m]) * 60", "legendFormat": "Search Requests/min", "refId": "C"}, {"expr": "histogram_quantile(0.95, rate(job_discovery_search_duration_seconds_bucket[5m])) * 1000", "legendFormat": "Search Latency p95 (ms)", "refId": "D"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "ES Index Size (MB)"}, "properties": [{"id": "unit", "value": "decbytes"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Search Latency p95 (ms)"}, "properties": [{"id": "unit", "value": "ms"}]}]}}, {"id": 10, "title": "Recent Alerts & Issues", "type": "logs", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}, "targets": [{"expr": "{job=\"job-discovery\"} |= \"ERROR\" or \"WARN\"", "refId": "A"}], "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": false, "prettifyLogMessage": false, "enableLogDetails": true, "dedupStrategy": "none", "sortOrder": "Descending"}}], "templating": {"list": [{"name": "source", "type": "query", "query": "label_values(job_discovery_jobs_total, source)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true, "current": {"selected": true, "text": "All", "value": "$__all"}}, {"name": "interval", "type": "interval", "query": "1m,5m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "refresh": 2, "current": {"selected": true, "text": "5m", "value": "5m"}}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "prometheus", "enable": true, "expr": "job_discovery_deployment_timestamp", "iconColor": "blue", "titleFormat": "Deployment", "textFormat": "Version: {{version}}"}, {"name": "<PERSON><PERSON><PERSON>", "datasource": "prometheus", "enable": true, "expr": "ALERTS{alertname=~\"JobDiscovery.*\"}", "iconColor": "red", "titleFormat": "{{alertname}}", "textFormat": "{{description}}"}]}, "links": [{"title": "Job Discovery API", "url": "/api/jobs", "type": "link", "icon": "external link"}, {"title": "Queue Dashboard", "url": "/admin/queues", "type": "link", "icon": "external link"}, {"title": "ElasticSearch", "url": "http://localhost:5601", "type": "link", "icon": "external link"}]}, "overwrite": true}