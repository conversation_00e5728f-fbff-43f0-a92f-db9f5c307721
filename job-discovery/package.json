{"name": "job-discovery-system", "version": "1.0.0", "description": "Scalable job aggregation platform with 40+ sources, deduplication, and intelligent search", "main": "dist/index.js", "scripts": {"build": "tsc && npm run copy-assets", "copy-assets": "cp -r src/schemas dist/ && cp -r monitoring dist/", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --config jest.integration.config.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "docker:build": "docker build -t job-discovery:latest .", "docker:run": "docker-compose up -d", "docker:stop": "docker-compose down", "docker:logs": "docker-compose logs -f", "db:migrate": "ts-node scripts/migrate.ts", "db:seed": "ts-node scripts/seed.ts", "scrape:linkedin": "ts-node scripts/scrape-linkedin.ts", "scrape:workday": "ts-node scripts/scrape-workday.ts", "scheduler:start": "ts-node src/scheduler/index.ts", "api:start": "ts-node src/api/index.ts", "health:check": "curl -f http://localhost:8000/health || exit 1", "metrics:export": "ts-node scripts/export-metrics.ts", "cleanup:old-data": "ts-node scripts/cleanup.ts"}, "keywords": ["job-scraping", "job-aggregation", "elasticsearch", "timescaledb", "puppeteer", "bullmq", "typescript", "microservices"], "author": "Job Discovery Team", "license": "MIT", "dependencies": {"@elastic/elasticsearch": "^8.11.0", "bullmq": "^4.15.0", "express": "^4.18.2", "fastify": "^4.24.3", "ioredis": "^5.3.2", "joi": "^17.11.0", "pg": "^8.11.3", "puppeteer": "^21.5.2", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "winston": "^3.11.0", "axios": "^1.6.0", "lodash": "^4.17.21", "date-fns": "^2.30.0", "nanoid": "^5.0.3", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "rate-limiter-flexible": "^4.0.1", "prom-client": "^15.0.0", "dotenv": "^16.3.1", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "reflect-metadata": "^0.1.13", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "jsonschema": "^1.4.1", "fuzzywuzzy": "^1.0.0", "natural": "^6.7.0", "compromise": "^14.10.0", "geolib": "^3.3.4", "country-list": "^2.3.0", "timezone-support": "^3.1.0"}, "devDependencies": {"@types/node": "^20.8.10", "@types/express": "^4.17.21", "@types/pg": "^8.10.7", "@types/lodash": "^4.14.201", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.0.3", "typescript": "^5.2.2", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.16", "nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/company/job-discovery-system.git"}, "bugs": {"url": "https://github.com/company/job-discovery-system/issues"}, "homepage": "https://github.com/company/job-discovery-system#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts", "!src/**/*.interface.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "setupFilesAfterEnv": ["<rootDir>/tests/setup.ts"]}, "eslintConfig": {"parser": "@typescript-eslint/parser", "extends": ["eslint:recommended", "@typescript-eslint/recommended", "prettier"], "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-inferrable-types": "off", "@typescript-eslint/no-non-null-assertion": "warn"}, "env": {"node": true, "es6": true, "jest": true}}, "prettier": {"semi": false, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false}}