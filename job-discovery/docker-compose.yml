version: '3.8'

services:
  # PostgreSQL with TimescaleDB
  postgres:
    image: timescale/timescaledb:latest-pg15
    container_name: job-discovery-postgres
    environment:
      POSTGRES_DB: jobdb
      POSTGRES_USER: jobuser
      POSTGRES_PASSWORD: jobpass
      TIMESCALEDB_TELEMETRY: 'off'
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - job-discovery
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U jobuser -d jobdb"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ElasticSearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: job-discovery-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms2g -Xmx2g"
      - bootstrap.memory_lock=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - job-discovery
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: job-discovery-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
      XPACK_SECURITY_ENABLED: false
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - job-discovery
    restart: unless-stopped

  # Redis
  redis:
    image: redis:7-alpine
    container_name: job-discovery-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - job-discovery
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: job-discovery-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - job-discovery
    restart: unless-stopped

  # Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: job-discovery-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: grafana-piechart-panel,grafana-worldmap-panel
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    networks:
      - job-discovery
    restart: unless-stopped

  # Job Discovery API
  job-discovery-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: job-discovery-api
    environment:
      NODE_ENV: production
      POSTGRES_URL: ******************************************/jobdb
      ELASTICSEARCH_URL: http://elasticsearch:9200
      REDIS_URL: redis://redis:6379
      BRIGHTDATA_USERNAME: ${BRIGHTDATA_USERNAME}
      BRIGHTDATA_PASSWORD: ${BRIGHTDATA_PASSWORD}
      LOG_LEVEL: info
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - job-discovery
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Job Scheduler (separate service for scaling)
  job-scheduler:
    build:
      context: .
      dockerfile: Dockerfile.scheduler
    container_name: job-discovery-scheduler
    environment:
      NODE_ENV: production
      POSTGRES_URL: ******************************************/jobdb
      ELASTICSEARCH_URL: http://elasticsearch:9200
      REDIS_URL: redis://redis:6379
      BRIGHTDATA_USERNAME: ${BRIGHTDATA_USERNAME}
      BRIGHTDATA_PASSWORD: ${BRIGHTDATA_PASSWORD}
      LOG_LEVEL: info
      SCHEDULER_CONCURRENCY: 5
    depends_on:
      - postgres
      - elasticsearch
      - redis
      - job-discovery-api
    networks:
      - job-discovery
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    deploy:
      replicas: 2

  # BullMQ Dashboard
  bullmq-dashboard:
    image: deadly0/bull-board
    container_name: job-discovery-bullmq
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ""
      REDIS_DB: 0
    ports:
      - "3001:3000"
    depends_on:
      - redis
    networks:
      - job-discovery
    restart: unless-stopped

  # Nginx (Load Balancer & Reverse Proxy)
  nginx:
    image: nginx:alpine
    container_name: job-discovery-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - job-discovery-api
      - grafana
      - kibana
    networks:
      - job-discovery
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  elasticsearch_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  job-discovery:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
