/**
 * BullMQ Job Scheduler
 * Rate-aware scheduling with domain-specific rate limiting and monitoring
 */

import { Queue, Worker, Job, QueueEvents, JobsOptions } from 'bullmq'
import { Redis } from 'ioredis'
import { Logger } from 'winston'
import { LinkedInScraper } from '../scrapers/adapters/linkedin-scraper'
import { WorkdayScraper } from '../scrapers/adapters/workday-scraper'
import { JobProcessor } from '../pipeline/job-processor'
import { TimescaleStorage } from '../storage/postgres/timescale-storage'
import { SearchEngine } from '../storage/elasticsearch/search-engine'
import { ProxyManager } from '../scrapers/utils/proxy-manager'
import { RateLimiter } from '../scrapers/utils/rate-limiter'

export interface SchedulerConfig {
  redis: {
    host: string
    port: number
    password?: string
    db: number
  }
  concurrency: {
    global: number
    perSource: number
  }
  retries: {
    attempts: number
    backoff: {
      type: 'exponential' | 'fixed'
      delay: number
    }
  }
  monitoring: {
    enableMetrics: boolean
    metricsInterval: number
  }
}

export interface ScrapingJobData {
  source: string
  config: any
  searchParams?: any
  priority?: number
  sessionId?: string
}

export interface ProcessingJobData {
  jobs: any[]
  sessionId: string
  source: string
}

export interface IndexingJobData {
  jobs: any[]
  operation: 'index' | 'update' | 'delete'
}

export class JobScheduler {
  private logger: Logger
  private config: SchedulerConfig
  private redis: Redis
  
  // Queues
  private scrapingQueue: Queue<ScrapingJobData>
  private processingQueue: Queue<ProcessingJobData>
  private indexingQueue: Queue<IndexingJobData>
  
  // Workers
  private scrapingWorker: Worker<ScrapingJobData>
  private processingWorker: Worker<ProcessingJobData>
  private indexingWorker: Worker<IndexingJobData>
  
  // Queue Events
  private scrapingEvents: QueueEvents
  private processingEvents: QueueEvents
  private indexingEvents: QueueEvents
  
  // Services
  private proxyManager: ProxyManager
  private rateLimiter: RateLimiter
  private jobProcessor: JobProcessor
  private storage: TimescaleStorage
  private searchEngine: SearchEngine
  
  // Metrics
  private metrics: {
    jobsProcessed: number
    jobsSucceeded: number
    jobsFailed: number
    avgProcessingTime: number
    lastProcessedAt: Date
  }

  constructor(
    logger: Logger,
    config: Partial<SchedulerConfig> = {},
    storage: TimescaleStorage,
    searchEngine: SearchEngine
  ) {
    this.logger = logger
    this.storage = storage
    this.searchEngine = searchEngine
    
    this.config = {
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0'),
      },
      concurrency: {
        global: 10,
        perSource: 2,
      },
      retries: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
      monitoring: {
        enableMetrics: true,
        metricsInterval: 60000, // 1 minute
      },
      ...config,
    }

    this.metrics = {
      jobsProcessed: 0,
      jobsSucceeded: 0,
      jobsFailed: 0,
      avgProcessingTime: 0,
      lastProcessedAt: new Date(),
    }

    this.initializeRedis()
    this.initializeServices()
    this.initializeQueues()
    this.initializeWorkers()
    this.initializeMonitoring()
  }

  private initializeRedis(): void {
    this.redis = new Redis({
      host: this.config.redis.host,
      port: this.config.redis.port,
      password: this.config.redis.password,
      db: this.config.redis.db,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
    })

    this.redis.on('connect', () => {
      this.logger.info('Connected to Redis')
    })

    this.redis.on('error', (error) => {
      this.logger.error('Redis connection error', { error: error.message })
    })
  }

  private initializeServices(): void {
    this.proxyManager = new ProxyManager(this.logger)
    this.rateLimiter = new RateLimiter(this.logger)
    this.jobProcessor = new JobProcessor(this.logger)
  }

  private initializeQueues(): void {
    const queueOptions = {
      connection: this.redis,
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: this.config.retries.attempts,
        backoff: {
          type: this.config.retries.backoff.type,
          delay: this.config.retries.backoff.delay,
        },
      } as JobsOptions,
    }

    this.scrapingQueue = new Queue('scraping', queueOptions)
    this.processingQueue = new Queue('processing', queueOptions)
    this.indexingQueue = new Queue('indexing', queueOptions)

    // Queue events for monitoring
    this.scrapingEvents = new QueueEvents('scraping', { connection: this.redis })
    this.processingEvents = new QueueEvents('processing', { connection: this.redis })
    this.indexingEvents = new QueueEvents('indexing', { connection: this.redis })

    this.logger.info('Initialized BullMQ queues')
  }

  private initializeWorkers(): void {
    // Scraping worker
    this.scrapingWorker = new Worker(
      'scraping',
      this.processScraping.bind(this),
      {
        connection: this.redis,
        concurrency: this.config.concurrency.global,
        limiter: {
          max: 10,
          duration: 60000, // 10 jobs per minute globally
        },
      }
    )

    // Processing worker
    this.processingWorker = new Worker(
      'processing',
      this.processJobs.bind(this),
      {
        connection: this.redis,
        concurrency: 5,
      }
    )

    // Indexing worker
    this.indexingWorker = new Worker(
      'indexing',
      this.processIndexing.bind(this),
      {
        connection: this.redis,
        concurrency: 3,
      }
    )

    // Worker event handlers
    this.setupWorkerEvents()

    this.logger.info('Initialized BullMQ workers')
  }

  private setupWorkerEvents(): void {
    const workers = [this.scrapingWorker, this.processingWorker, this.indexingWorker]

    workers.forEach(worker => {
      worker.on('completed', (job) => {
        this.metrics.jobsSucceeded++
        this.metrics.jobsProcessed++
        this.metrics.lastProcessedAt = new Date()
        
        this.logger.debug('Job completed', {
          queue: worker.name,
          jobId: job.id,
          duration: Date.now() - job.processedOn!,
        })
      })

      worker.on('failed', (job, error) => {
        this.metrics.jobsFailed++
        this.metrics.jobsProcessed++
        
        this.logger.error('Job failed', {
          queue: worker.name,
          jobId: job?.id,
          error: error.message,
          attempts: job?.attemptsMade,
        })
      })

      worker.on('error', (error) => {
        this.logger.error('Worker error', {
          worker: worker.name,
          error: error.message,
        })
      })
    })
  }

  private initializeMonitoring(): void {
    if (!this.config.monitoring.enableMetrics) return

    setInterval(() => {
      this.collectMetrics()
    }, this.config.monitoring.metricsInterval)
  }

  /**
   * Schedule scraping job for a source
   */
  async scheduleScraping(
    source: string,
    config: any,
    searchParams?: any,
    options?: {
      priority?: number
      delay?: number
      repeat?: {
        pattern: string
        tz?: string
      }
    }
  ): Promise<string> {
    const jobData: ScrapingJobData = {
      source,
      config,
      searchParams,
      priority: options?.priority || 0,
    }

    const jobOptions: JobsOptions = {
      priority: options?.priority || 0,
      delay: options?.delay || 0,
    }

    if (options?.repeat) {
      jobOptions.repeat = {
        pattern: options.repeat.pattern,
        tz: options.repeat.tz,
      }
    }

    const job = await this.scrapingQueue.add(
      `scrape-${source}`,
      jobData,
      jobOptions
    )

    this.logger.info('Scheduled scraping job', {
      jobId: job.id,
      source,
      priority: options?.priority,
      delay: options?.delay,
      repeat: options?.repeat,
    })

    return job.id!
  }

  /**
   * Process scraping job
   */
  private async processScraping(job: Job<ScrapingJobData>): Promise<void> {
    const { source, config, searchParams, sessionId } = job.data
    
    this.logger.info('Processing scraping job', {
      jobId: job.id,
      source,
      sessionId,
    })

    try {
      // Start crawl session
      const crawlSessionId = sessionId || await this.storage.startCrawlSession(source, {
        jobId: job.id,
        searchParams,
      })

      // Create appropriate scraper
      const scraper = await this.createScraper(source, config)
      
      // Perform scraping
      const result = await scraper.searchJobs(searchParams || {})
      
      // Update job progress
      await job.updateProgress(50)

      if (result.jobs.length > 0) {
        // Schedule processing job
        await this.processingQueue.add(
          `process-${source}`,
          {
            jobs: result.jobs,
            sessionId: crawlSessionId,
            source,
          },
          { priority: job.data.priority || 0 }
        )
      }

      // Complete crawl session
      await this.storage.completeCrawlSession(crawlSessionId, {
        jobsFound: result.jobs.length,
        jobsProcessed: 0,
        jobsNew: 0,
        jobsUpdated: 0,
        jobsDeleted: 0,
      })

      await job.updateProgress(100)
      
      this.logger.info('Scraping job completed', {
        jobId: job.id,
        source,
        jobsFound: result.jobs.length,
        sessionId: crawlSessionId,
      })

    } catch (error) {
      this.logger.error('Scraping job failed', {
        jobId: job.id,
        source,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      throw error
    }
  }

  /**
   * Process job data through pipeline
   */
  private async processJobs(job: Job<ProcessingJobData>): Promise<void> {
    const { jobs, sessionId, source } = job.data
    
    this.logger.info('Processing jobs through pipeline', {
      jobId: job.id,
      jobCount: jobs.length,
      source,
      sessionId,
    })

    try {
      // Process jobs through pipeline
      const result = await this.jobProcessor.processBatch(jobs)
      
      await job.updateProgress(50)

      // Store processed jobs
      let newJobs = 0
      let updatedJobs = 0
      
      for (const processedJob of result.processed) {
        const upsertResult = await this.storage.upsertJob(processedJob, sessionId)
        if (upsertResult.isNew) {
          newJobs++
        } else if (upsertResult.hasChanges) {
          updatedJobs++
        }
      }

      await job.updateProgress(75)

      // Schedule indexing
      if (result.processed.length > 0) {
        await this.indexingQueue.add(
          `index-${source}`,
          {
            jobs: result.processed,
            operation: 'index',
          },
          { priority: job.opts.priority || 0 }
        )
      }

      // Update crawl session
      await this.storage.completeCrawlSession(sessionId, {
        jobsFound: jobs.length,
        jobsProcessed: result.processed.length,
        jobsNew: newJobs,
        jobsUpdated: updatedJobs,
        jobsDeleted: 0,
      })

      await job.updateProgress(100)
      
      this.logger.info('Job processing completed', {
        jobId: job.id,
        source,
        processed: result.processed.length,
        duplicates: result.duplicates.length,
        invalid: result.invalid.length,
        newJobs,
        updatedJobs,
      })

    } catch (error) {
      this.logger.error('Job processing failed', {
        jobId: job.id,
        source,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      throw error
    }
  }

  /**
   * Process indexing job
   */
  private async processIndexing(job: Job<IndexingJobData>): Promise<void> {
    const { jobs, operation } = job.data
    
    this.logger.info('Processing indexing job', {
      jobId: job.id,
      jobCount: jobs.length,
      operation,
    })

    try {
      switch (operation) {
        case 'index':
        case 'update':
          const result = await this.searchEngine.bulkIndexJobs(jobs)
          this.logger.info('Bulk indexing completed', {
            jobId: job.id,
            indexed: result.indexed,
            errors: result.errors.length,
          })
          break
          
        case 'delete':
          for (const jobToDelete of jobs) {
            await this.searchEngine.deleteJob(jobToDelete.id)
          }
          break
      }

      await job.updateProgress(100)
      
    } catch (error) {
      this.logger.error('Indexing job failed', {
        jobId: job.id,
        operation,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      throw error
    }
  }

  private async createScraper(source: string, config: any): Promise<any> {
    switch (source) {
      case 'linkedin':
        return new LinkedInScraper(
          this.logger,
          this.proxyManager,
          this.rateLimiter,
          config
        )
        
      case 'workday':
        return new WorkdayScraper(
          config.workdayConfig,
          this.logger,
          this.proxyManager,
          this.rateLimiter,
          config.scrapingConfig
        )
        
      default:
        throw new Error(`Unsupported source: ${source}`)
    }
  }

  /**
   * Schedule recurring scraping jobs
   */
  async scheduleRecurringJobs(): Promise<void> {
    const sources = [
      {
        name: 'linkedin',
        pattern: '0 */15 * * *', // Every 15 minutes
        config: {},
        searchParams: { keywords: 'software engineer', maxPages: 3 },
        priority: 10,
      },
      {
        name: 'workday',
        pattern: '0 */30 * * *', // Every 30 minutes
        config: {
          workdayConfig: {
            companyName: 'Example Corp',
            baseUrl: 'https://example.wd1.myworkdayjobs.com',
            jobSearchPath: '/example/jobs',
          },
        },
        searchParams: { keywords: 'engineer', maxPages: 2 },
        priority: 8,
      },
    ]

    for (const source of sources) {
      await this.scheduleScraping(
        source.name,
        source.config,
        source.searchParams,
        {
          priority: source.priority,
          repeat: { pattern: source.pattern },
        }
      )
    }

    this.logger.info('Scheduled recurring scraping jobs', {
      sources: sources.map(s => ({ name: s.name, pattern: s.pattern })),
    })
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    scraping: any
    processing: any
    indexing: any
    metrics: typeof this.metrics
  }> {
    const [scrapingStats, processingStats, indexingStats] = await Promise.all([
      this.scrapingQueue.getJobCounts(),
      this.processingQueue.getJobCounts(),
      this.indexingQueue.getJobCounts(),
    ])

    return {
      scraping: scrapingStats,
      processing: processingStats,
      indexing: indexingStats,
      metrics: this.metrics,
    }
  }

  /**
   * Collect and store metrics
   */
  private async collectMetrics(): Promise<void> {
    try {
      const stats = await this.getQueueStats()
      
      // Store metrics in Redis for Grafana
      const metricsKey = `metrics:${Date.now()}`
      await this.redis.setex(metricsKey, 3600, JSON.stringify({
        timestamp: new Date().toISOString(),
        queues: {
          scraping: stats.scraping,
          processing: stats.processing,
          indexing: stats.indexing,
        },
        metrics: stats.metrics,
        system: {
          memory: process.memoryUsage(),
          uptime: process.uptime(),
        },
      }))

      // Keep only last 24 hours of metrics
      const keys = await this.redis.keys('metrics:*')
      const cutoff = Date.now() - 24 * 60 * 60 * 1000
      
      for (const key of keys) {
        const timestamp = parseInt(key.split(':')[1])
        if (timestamp < cutoff) {
          await this.redis.del(key)
        }
      }

    } catch (error) {
      this.logger.error('Failed to collect metrics', {
        error: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  /**
   * Pause all queues
   */
  async pauseAll(): Promise<void> {
    await Promise.all([
      this.scrapingQueue.pause(),
      this.processingQueue.pause(),
      this.indexingQueue.pause(),
    ])
    
    this.logger.info('All queues paused')
  }

  /**
   * Resume all queues
   */
  async resumeAll(): Promise<void> {
    await Promise.all([
      this.scrapingQueue.resume(),
      this.processingQueue.resume(),
      this.indexingQueue.resume(),
    ])
    
    this.logger.info('All queues resumed')
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    this.logger.info('Shutting down job scheduler...')

    // Close workers
    await Promise.all([
      this.scrapingWorker.close(),
      this.processingWorker.close(),
      this.indexingWorker.close(),
    ])

    // Close queue events
    await Promise.all([
      this.scrapingEvents.close(),
      this.processingEvents.close(),
      this.indexingEvents.close(),
    ])

    // Close queues
    await Promise.all([
      this.scrapingQueue.close(),
      this.processingQueue.close(),
      this.indexingQueue.close(),
    ])

    // Close Redis connection
    await this.redis.quit()

    this.logger.info('Job scheduler shutdown complete')
  }
}
