/**
 * Base Scraper Framework
 * Puppeteer-based scraping with stealth mode, proxy rotation, and anti-detection
 */

import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON>, PuppeteerLaunchOptions } from 'puppeteer'
import StealthPlugin from 'puppeteer-extra-plugin-stealth'
import { addExtra } from 'puppeteer-extra'
import { EventEmitter } from 'events'
import { Logger } from 'winston'
import { ProxyManager } from '../utils/proxy-manager'
import { RateLimiter } from '../utils/rate-limiter'
import { AntiDetection } from '../utils/anti-detection'
import { RetryManager } from '../utils/retry-manager'

// Enhanced Puppeteer with stealth
const puppeteerExtra = addExtra(puppeteer)
puppeteerExtra.use(StealthPlugin())

export interface ScrapingConfig {
  source: string
  baseUrl: string
  maxConcurrency: number
  requestDelay: number
  retryAttempts: number
  timeout: number
  useProxy: boolean
  stealthMode: boolean
  userAgentRotation: boolean
  headless: boolean | 'new'
  viewport: { width: number; height: number }
  blockedResources: string[]
  customHeaders: Record<string, string>
}

export interface ScrapingContext {
  page: Page
  browser: Browser
  config: ScrapingConfig
  logger: Logger
  metrics: ScrapingMetrics
}

export interface ScrapingMetrics {
  startTime: number
  requestCount: number
  successCount: number
  errorCount: number
  bytesDownloaded: number
  avgResponseTime: number
  proxyRotations: number
  captchaSolved: number
}

export interface JobListing {
  id: string
  title: string
  company: string
  location: string
  description: string
  requirements: string[]
  benefits: string[]
  salary?: {
    min?: number
    max?: number
    currency: string
    period: 'hourly' | 'monthly' | 'yearly'
  }
  employment_type: 'full-time' | 'part-time' | 'contract' | 'internship' | 'temporary'
  experience_level: 'entry' | 'mid' | 'senior' | 'executive'
  remote_option: 'remote' | 'hybrid' | 'onsite'
  posted_date: Date
  application_deadline?: Date
  application_url: string
  source: string
  source_id: string
  skills: string[]
  industry: string
  company_size?: string
  company_logo?: string
  raw_data: Record<string, any>
  scraped_at: Date
}

export interface ScrapingResult {
  jobs: JobListing[]
  metadata: {
    source: string
    total_found: number
    pages_scraped: number
    success_rate: number
    scraping_time: number
    errors: string[]
  }
}

export abstract class BaseScraper extends EventEmitter {
  protected config: ScrapingConfig
  protected logger: Logger
  protected proxyManager: ProxyManager
  protected rateLimiter: RateLimiter
  protected antiDetection: AntiDetection
  protected retryManager: RetryManager
  protected browser: Browser | null = null
  protected metrics: ScrapingMetrics

  constructor(
    config: ScrapingConfig,
    logger: Logger,
    proxyManager: ProxyManager,
    rateLimiter: RateLimiter
  ) {
    super()
    this.config = config
    this.logger = logger
    this.proxyManager = proxyManager
    this.rateLimiter = rateLimiter
    this.antiDetection = new AntiDetection(logger)
    this.retryManager = new RetryManager(config.retryAttempts, logger)
    this.metrics = this.initializeMetrics()
  }

  private initializeMetrics(): ScrapingMetrics {
    return {
      startTime: Date.now(),
      requestCount: 0,
      successCount: 0,
      errorCount: 0,
      bytesDownloaded: 0,
      avgResponseTime: 0,
      proxyRotations: 0,
      captchaSolved: 0,
    }
  }

  /**
   * Initialize browser with stealth configuration
   */
  protected async initializeBrowser(): Promise<Browser> {
    const proxy = this.config.useProxy ? await this.proxyManager.getProxy() : null
    
    const launchOptions: PuppeteerLaunchOptions = {
      headless: this.config.headless,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI',
        '--disable-ipc-flooding-protection',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        ...(proxy ? [`--proxy-server=${proxy.host}:${proxy.port}`] : []),
      ],
      defaultViewport: this.config.viewport,
      timeout: this.config.timeout,
    }

    this.browser = await puppeteerExtra.launch(launchOptions)
    
    this.logger.info('Browser initialized', {
      source: this.config.source,
      proxy: proxy ? `${proxy.host}:${proxy.port}` : 'none',
      headless: this.config.headless,
    })

    return this.browser
  }

  /**
   * Create and configure a new page
   */
  protected async createPage(): Promise<Page> {
    if (!this.browser) {
      throw new Error('Browser not initialized')
    }

    const page = await this.browser.newPage()

    // Configure page settings
    await this.configurePage(page)

    // Set up request interception
    await this.setupRequestInterception(page)

    // Set up response monitoring
    await this.setupResponseMonitoring(page)

    return page
  }

  private async configurePage(page: Page): Promise<void> {
    // Set viewport
    await page.setViewport(this.config.viewport)

    // Set user agent
    if (this.config.userAgentRotation) {
      const userAgent = await this.antiDetection.getRandomUserAgent()
      await page.setUserAgent(userAgent)
    }

    // Set custom headers
    await page.setExtraHTTPHeaders(this.config.customHeaders)

    // Configure timeouts
    page.setDefaultTimeout(this.config.timeout)
    page.setDefaultNavigationTimeout(this.config.timeout)

    // Block unnecessary resources
    await page.setRequestInterception(true)
  }

  private async setupRequestInterception(page: Page): Promise<void> {
    page.on('request', async (request) => {
      const resourceType = request.resourceType()
      const url = request.url()

      // Block unnecessary resources
      if (this.config.blockedResources.includes(resourceType)) {
        await request.abort()
        return
      }

      // Apply rate limiting
      await this.rateLimiter.waitForSlot(this.config.source)

      // Add anti-detection headers
      const headers = await this.antiDetection.getRandomHeaders()
      
      await request.continue({
        headers: { ...request.headers(), ...headers },
      })

      this.metrics.requestCount++
    })
  }

  private async setupResponseMonitoring(page: Page): Promise<void> {
    page.on('response', (response) => {
      const status = response.status()
      const url = response.url()
      
      if (status >= 200 && status < 300) {
        this.metrics.successCount++
      } else {
        this.metrics.errorCount++
        this.logger.warn('HTTP error response', {
          url,
          status,
          source: this.config.source,
        })
      }

      // Track bytes downloaded
      const contentLength = response.headers()['content-length']
      if (contentLength) {
        this.metrics.bytesDownloaded += parseInt(contentLength, 10)
      }
    })

    // Handle page errors
    page.on('pageerror', (error) => {
      this.logger.error('Page error', {
        error: error.message,
        source: this.config.source,
      })
      this.metrics.errorCount++
    })

    // Handle console messages
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        this.logger.debug('Console error', {
          message: msg.text(),
          source: this.config.source,
        })
      }
    })
  }

  /**
   * Navigate to URL with retry logic and anti-detection
   */
  protected async navigateToUrl(page: Page, url: string): Promise<void> {
    await this.retryManager.execute(async () => {
      // Add random delay to mimic human behavior
      await this.antiDetection.randomDelay(1000, 3000)

      const response = await page.goto(url, {
        waitUntil: 'networkidle2',
        timeout: this.config.timeout,
      })

      if (!response || response.status() >= 400) {
        throw new Error(`Failed to load ${url}: ${response?.status()}`)
      }

      // Check for CAPTCHA or bot detection
      await this.handleBotDetection(page)

      this.logger.debug('Successfully navigated to URL', {
        url,
        status: response.status(),
        source: this.config.source,
      })
    })
  }

  private async handleBotDetection(page: Page): Promise<void> {
    // Check for common bot detection patterns
    const botDetectionSelectors = [
      '[data-testid="captcha"]',
      '.captcha',
      '#captcha',
      '.cf-browser-verification',
      '.challenge-form',
      'iframe[src*="captcha"]',
    ]

    for (const selector of botDetectionSelectors) {
      const element = await page.$(selector)
      if (element) {
        this.logger.warn('Bot detection detected', {
          selector,
          source: this.config.source,
        })

        // Attempt to solve CAPTCHA or rotate proxy
        await this.handleCaptcha(page, selector)
        break
      }
    }
  }

  private async handleCaptcha(page: Page, selector: string): Promise<void> {
    // Rotate proxy if available
    if (this.config.useProxy) {
      await this.rotateProxy()
      this.metrics.proxyRotations++
    }

    // Add longer delay
    await this.antiDetection.randomDelay(5000, 10000)

    // In production, integrate with CAPTCHA solving service
    this.logger.info('CAPTCHA detected, implementing solving strategy', {
      selector,
      source: this.config.source,
    })

    this.metrics.captchaSolved++
  }

  private async rotateProxy(): Promise<void> {
    if (this.browser) {
      await this.browser.close()
      this.browser = null
    }
    
    // Get new proxy
    await this.proxyManager.rotateProxy()
    
    // Reinitialize browser with new proxy
    await this.initializeBrowser()
  }

  /**
   * Extract job listings from current page
   */
  protected abstract extractJobListings(page: Page): Promise<JobListing[]>

  /**
   * Get pagination URLs
   */
  protected abstract getPaginationUrls(page: Page): Promise<string[]>

  /**
   * Main scraping method
   */
  async scrape(startUrl: string, maxPages: number = 10): Promise<ScrapingResult> {
    this.metrics = this.initializeMetrics()
    const allJobs: JobListing[] = []
    const errors: string[] = []
    let pagesScrapped = 0

    try {
      await this.initializeBrowser()
      const page = await this.createPage()

      const urlsToScrape = [startUrl]
      
      while (urlsToScrape.length > 0 && pagesScrapped < maxPages) {
        const currentUrl = urlsToScrape.shift()!
        
        try {
          await this.navigateToUrl(page, currentUrl)
          
          // Extract jobs from current page
          const jobs = await this.extractJobListings(page)
          allJobs.push(...jobs)
          
          // Get pagination URLs
          if (pagesScrapped < maxPages - 1) {
            const paginationUrls = await this.getPaginationUrls(page)
            urlsToScrape.push(...paginationUrls.slice(0, maxPages - pagesScrapped - 1))
          }
          
          pagesScrapped++
          
          this.emit('pageScraped', {
            url: currentUrl,
            jobsFound: jobs.length,
            totalJobs: allJobs.length,
          })
          
          // Delay between pages
          await this.antiDetection.randomDelay(
            this.config.requestDelay,
            this.config.requestDelay * 2
          )
          
        } catch (error) {
          const errorMessage = `Failed to scrape ${currentUrl}: ${error}`
          errors.push(errorMessage)
          this.logger.error(errorMessage, { source: this.config.source })
        }
      }

      await page.close()

    } catch (error) {
      const errorMessage = `Scraping failed: ${error}`
      errors.push(errorMessage)
      this.logger.error(errorMessage, { source: this.config.source })
    } finally {
      if (this.browser) {
        await this.browser.close()
        this.browser = null
      }
    }

    const scrapingTime = Date.now() - this.metrics.startTime
    const successRate = this.metrics.requestCount > 0 
      ? this.metrics.successCount / this.metrics.requestCount 
      : 0

    return {
      jobs: allJobs,
      metadata: {
        source: this.config.source,
        total_found: allJobs.length,
        pages_scraped: pagesScrapped,
        success_rate: successRate,
        scraping_time: scrapingTime,
        errors,
      },
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close()
      this.browser = null
    }
  }

  /**
   * Get scraping metrics
   */
  getMetrics(): ScrapingMetrics {
    return { ...this.metrics }
  }
}
