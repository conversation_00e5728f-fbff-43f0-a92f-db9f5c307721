/**
 * Proxy Manager
 * BrightData/Lumibot proxy rotation with health monitoring
 */

import { Logger } from 'winston'
import axios, { AxiosInstance } from 'axios'
import { EventEmitter } from 'events'

export interface ProxyConfig {
  host: string
  port: number
  username: string
  password: string
  protocol: 'http' | 'https' | 'socks5'
  country?: string
  sticky_session?: boolean
  session_id?: string
}

export interface ProxyStats {
  total_requests: number
  successful_requests: number
  failed_requests: number
  avg_response_time: number
  last_used: Date
  health_score: number
  ban_count: number
  success_rate: number
}

export interface ProxyPool {
  residential: ProxyConfig[]
  datacenter: ProxyConfig[]
  mobile: ProxyConfig[]
}

export class ProxyManager extends EventEmitter {
  private logger: Logger
  private currentProxy: ProxyConfig | null = null
  private proxyPool: ProxyPool
  private proxyStats: Map<string, ProxyStats> = new Map()
  private bannedProxies: Set<string> = new Set()
  private rotationInterval: number = 300000 // 5 minutes
  private healthCheckInterval: number = 60000 // 1 minute
  private maxFailureRate: number = 0.3 // 30% failure rate threshold
  private httpClient: AxiosInstance

  constructor(logger: Logger) {
    super()
    this.logger = logger
    this.proxyPool = { residential: [], datacenter: [], mobile: [] }
    this.httpClient = axios.create({ timeout: 10000 })
    this.initializeProxyPool()
    this.startHealthMonitoring()
  }

  private initializeProxyPool(): void {
    // BrightData residential proxies
    const brightDataConfig = {
      host: process.env.BRIGHTDATA_HOST || 'brd.superproxy.io',
      port: parseInt(process.env.BRIGHTDATA_PORT || '22225'),
      username: process.env.BRIGHTDATA_USERNAME || '',
      password: process.env.BRIGHTDATA_PASSWORD || '',
      protocol: 'http' as const,
    }

    // Generate multiple proxy endpoints with different sessions
    for (let i = 0; i < 10; i++) {
      this.proxyPool.residential.push({
        ...brightDataConfig,
        session_id: `session_${i}`,
        username: `${brightDataConfig.username}-session-${i}`,
      })
    }

    // Lumibot datacenter proxies (if configured)
    if (process.env.LUMIBOT_HOST) {
      for (let i = 0; i < 5; i++) {
        this.proxyPool.datacenter.push({
          host: process.env.LUMIBOT_HOST,
          port: parseInt(process.env.LUMIBOT_PORT || '8080'),
          username: process.env.LUMIBOT_USERNAME || '',
          password: process.env.LUMIBOT_PASSWORD || '',
          protocol: 'http',
          session_id: `lumibot_${i}`,
        })
      }
    }

    this.logger.info('Proxy pool initialized', {
      residential: this.proxyPool.residential.length,
      datacenter: this.proxyPool.datacenter.length,
      mobile: this.proxyPool.mobile.length,
    })
  }

  private startHealthMonitoring(): void {
    setInterval(async () => {
      await this.performHealthChecks()
    }, this.healthCheckInterval)

    setInterval(() => {
      this.cleanupBannedProxies()
    }, this.rotationInterval)
  }

  private async performHealthChecks(): Promise<void> {
    const allProxies = [
      ...this.proxyPool.residential,
      ...this.proxyPool.datacenter,
      ...this.proxyPool.mobile,
    ]

    const healthCheckPromises = allProxies.map(proxy => this.checkProxyHealth(proxy))
    await Promise.allSettled(healthCheckPromises)
  }

  private async checkProxyHealth(proxy: ProxyConfig): Promise<void> {
    const proxyKey = this.getProxyKey(proxy)
    
    try {
      const startTime = Date.now()
      
      const response = await this.httpClient.get('https://httpbin.org/ip', {
        proxy: {
          host: proxy.host,
          port: proxy.port,
          auth: {
            username: proxy.username,
            password: proxy.password,
          },
          protocol: proxy.protocol,
        },
        timeout: 5000,
      })

      const responseTime = Date.now() - startTime
      
      if (response.status === 200) {
        this.updateProxyStats(proxyKey, true, responseTime)
        this.bannedProxies.delete(proxyKey)
      } else {
        this.updateProxyStats(proxyKey, false, responseTime)
      }

    } catch (error) {
      this.updateProxyStats(proxyKey, false, 5000)
      this.logger.debug('Proxy health check failed', {
        proxy: proxyKey,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  private updateProxyStats(proxyKey: string, success: boolean, responseTime: number): void {
    const stats = this.proxyStats.get(proxyKey) || {
      total_requests: 0,
      successful_requests: 0,
      failed_requests: 0,
      avg_response_time: 0,
      last_used: new Date(),
      health_score: 1.0,
      ban_count: 0,
      success_rate: 1.0,
    }

    stats.total_requests++
    stats.last_used = new Date()

    if (success) {
      stats.successful_requests++
    } else {
      stats.failed_requests++
    }

    // Update average response time
    stats.avg_response_time = (
      (stats.avg_response_time * (stats.total_requests - 1) + responseTime) /
      stats.total_requests
    )

    // Calculate success rate
    stats.success_rate = stats.successful_requests / stats.total_requests

    // Calculate health score (0-1)
    stats.health_score = Math.max(0, Math.min(1, 
      stats.success_rate * (1 - Math.min(stats.avg_response_time / 5000, 1))
    ))

    // Mark as banned if failure rate is too high
    if (stats.success_rate < this.maxFailureRate && stats.total_requests > 10) {
      this.bannedProxies.add(proxyKey)
      stats.ban_count++
      this.logger.warn('Proxy marked as banned due to high failure rate', {
        proxy: proxyKey,
        success_rate: stats.success_rate,
        total_requests: stats.total_requests,
      })
    }

    this.proxyStats.set(proxyKey, stats)
  }

  private cleanupBannedProxies(): void {
    const cutoffTime = Date.now() - this.rotationInterval * 2 // 10 minutes ago
    
    for (const [proxyKey, stats] of this.proxyStats.entries()) {
      if (stats.last_used.getTime() < cutoffTime && this.bannedProxies.has(proxyKey)) {
        this.bannedProxies.delete(proxyKey)
        this.logger.info('Proxy unbanned after cooldown period', { proxy: proxyKey })
      }
    }
  }

  private getProxyKey(proxy: ProxyConfig): string {
    return `${proxy.host}:${proxy.port}:${proxy.session_id || 'default'}`
  }

  private getHealthyProxies(type?: 'residential' | 'datacenter' | 'mobile'): ProxyConfig[] {
    let proxies: ProxyConfig[]
    
    if (type) {
      proxies = this.proxyPool[type]
    } else {
      proxies = [
        ...this.proxyPool.residential,
        ...this.proxyPool.datacenter,
        ...this.proxyPool.mobile,
      ]
    }

    return proxies.filter(proxy => {
      const proxyKey = this.getProxyKey(proxy)
      const stats = this.proxyStats.get(proxyKey)
      
      return !this.bannedProxies.has(proxyKey) && 
             (!stats || stats.health_score > 0.5)
    })
  }

  async getProxy(type?: 'residential' | 'datacenter' | 'mobile'): Promise<ProxyConfig> {
    const healthyProxies = this.getHealthyProxies(type)
    
    if (healthyProxies.length === 0) {
      throw new Error(`No healthy proxies available${type ? ` for type: ${type}` : ''}`)
    }

    // Select proxy with highest health score
    const bestProxy = healthyProxies.reduce((best, current) => {
      const bestKey = this.getProxyKey(best)
      const currentKey = this.getProxyKey(current)
      const bestStats = this.proxyStats.get(bestKey)
      const currentStats = this.proxyStats.get(currentKey)
      
      const bestScore = bestStats?.health_score || 1.0
      const currentScore = currentStats?.health_score || 1.0
      
      return currentScore > bestScore ? current : best
    })

    this.currentProxy = bestProxy
    
    this.logger.debug('Selected proxy', {
      proxy: this.getProxyKey(bestProxy),
      type: type || 'any',
      health_score: this.proxyStats.get(this.getProxyKey(bestProxy))?.health_score || 1.0,
    })

    return bestProxy
  }

  async rotateProxy(type?: 'residential' | 'datacenter' | 'mobile'): Promise<ProxyConfig> {
    // Mark current proxy as used recently to avoid immediate reselection
    if (this.currentProxy) {
      const currentKey = this.getProxyKey(this.currentProxy)
      const stats = this.proxyStats.get(currentKey)
      if (stats) {
        stats.last_used = new Date()
        this.proxyStats.set(currentKey, stats)
      }
    }

    const newProxy = await this.getProxy(type)
    
    this.emit('proxyRotated', {
      previous: this.currentProxy ? this.getProxyKey(this.currentProxy) : null,
      current: this.getProxyKey(newProxy),
    })

    return newProxy
  }

  getCurrentProxy(): ProxyConfig | null {
    return this.currentProxy
  }

  getProxyStats(): Map<string, ProxyStats> {
    return new Map(this.proxyStats)
  }

  getBannedProxies(): Set<string> {
    return new Set(this.bannedProxies)
  }

  async testProxy(proxy: ProxyConfig): Promise<boolean> {
    try {
      const response = await this.httpClient.get('https://httpbin.org/ip', {
        proxy: {
          host: proxy.host,
          port: proxy.port,
          auth: {
            username: proxy.username,
            password: proxy.password,
          },
          protocol: proxy.protocol,
        },
        timeout: 10000,
      })

      return response.status === 200
    } catch (error) {
      return false
    }
  }

  reportProxyIssue(proxy: ProxyConfig, issue: 'banned' | 'slow' | 'error'): void {
    const proxyKey = this.getProxyKey(proxy)
    
    switch (issue) {
      case 'banned':
        this.bannedProxies.add(proxyKey)
        this.logger.warn('Proxy reported as banned', { proxy: proxyKey })
        break
      case 'slow':
        this.updateProxyStats(proxyKey, false, 10000)
        break
      case 'error':
        this.updateProxyStats(proxyKey, false, 5000)
        break
    }

    this.emit('proxyIssue', { proxy: proxyKey, issue })
  }

  getHealthReport(): {
    total_proxies: number
    healthy_proxies: number
    banned_proxies: number
    avg_health_score: number
    proxy_types: Record<string, number>
  } {
    const allProxies = [
      ...this.proxyPool.residential,
      ...this.proxyPool.datacenter,
      ...this.proxyPool.mobile,
    ]

    const healthyProxies = this.getHealthyProxies()
    const totalHealthScore = Array.from(this.proxyStats.values())
      .reduce((sum, stats) => sum + stats.health_score, 0)

    return {
      total_proxies: allProxies.length,
      healthy_proxies: healthyProxies.length,
      banned_proxies: this.bannedProxies.size,
      avg_health_score: this.proxyStats.size > 0 ? totalHealthScore / this.proxyStats.size : 1.0,
      proxy_types: {
        residential: this.proxyPool.residential.length,
        datacenter: this.proxyPool.datacenter.length,
        mobile: this.proxyPool.mobile.length,
      },
    }
  }
}
