/**
 * Rate Limiter
 * Domain-aware rate limiting with exponential backoff and burst control
 */

import { Logger } from 'winston'
import { EventEmitter } from 'events'

export interface RateLimitConfig {
  maxRequestsPerSecond: number
  maxRequestsPerMinute: number
  maxRequestsPerHour: number
  burstSize: number
  exponentialBackoffBase: number
  maxBackoffDelay: number
  windowSizeMs: number
}

export interface DomainStats {
  domain: string
  requestCount: number
  lastRequestTime: number
  consecutiveErrors: number
  currentBackoffDelay: number
  isBlocked: boolean
  blockUntil: number
  requestTimes: number[]
  errorTimes: number[]
}

export class RateLimiter extends EventEmitter {
  private logger: Logger
  private domainStats: Map<string, DomainStats> = new Map()
  private globalConfig: RateLimitConfig
  private domainConfigs: Map<string, RateLimitConfig> = new Map()
  private cleanupInterval: NodeJS.Timeout

  constructor(logger: Logger, globalConfig?: Partial<RateLimitConfig>) {
    super()
    this.logger = logger
    this.globalConfig = {
      maxRequestsPerSecond: 3,
      maxRequestsPerMinute: 180,
      maxRequestsPerHour: 10800,
      burstSize: 5,
      exponentialBackoffBase: 2,
      maxBackoffDelay: 300000, // 5 minutes
      windowSizeMs: 60000, // 1 minute
      ...globalConfig,
    }

    // Initialize domain-specific configurations
    this.initializeDomainConfigs()

    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldStats()
    }, this.globalConfig.windowSizeMs)
  }

  private initializeDomainConfigs(): void {
    // LinkedIn - more restrictive
    this.domainConfigs.set('linkedin.com', {
      ...this.globalConfig,
      maxRequestsPerSecond: 1,
      maxRequestsPerMinute: 60,
      maxRequestsPerHour: 3600,
      burstSize: 2,
    })

    // Indeed - moderate
    this.domainConfigs.set('indeed.com', {
      ...this.globalConfig,
      maxRequestsPerSecond: 2,
      maxRequestsPerMinute: 120,
      maxRequestsPerHour: 7200,
      burstSize: 3,
    })

    // Glassdoor - moderate
    this.domainConfigs.set('glassdoor.com', {
      ...this.globalConfig,
      maxRequestsPerSecond: 2,
      maxRequestsPerMinute: 100,
      maxRequestsPerHour: 6000,
      burstSize: 3,
    })

    // AngelList - less restrictive
    this.domainConfigs.set('angel.co', {
      ...this.globalConfig,
      maxRequestsPerSecond: 5,
      maxRequestsPerMinute: 300,
      maxRequestsPerHour: 18000,
      burstSize: 10,
    })

    // Generic job boards - moderate
    const genericDomains = [
      'monster.com',
      'careerbuilder.com',
      'ziprecruiter.com',
      'simplyhired.com',
    ]

    genericDomains.forEach(domain => {
      this.domainConfigs.set(domain, {
        ...this.globalConfig,
        maxRequestsPerSecond: 3,
        maxRequestsPerMinute: 180,
        maxRequestsPerHour: 10800,
        burstSize: 5,
      })
    })
  }

  private extractDomain(url: string): string {
    try {
      const urlObj = new URL(url)
      return urlObj.hostname.replace(/^www\./, '')
    } catch {
      return 'unknown'
    }
  }

  private getDomainConfig(domain: string): RateLimitConfig {
    return this.domainConfigs.get(domain) || this.globalConfig
  }

  private getDomainStats(domain: string): DomainStats {
    if (!this.domainStats.has(domain)) {
      this.domainStats.set(domain, {
        domain,
        requestCount: 0,
        lastRequestTime: 0,
        consecutiveErrors: 0,
        currentBackoffDelay: 0,
        isBlocked: false,
        blockUntil: 0,
        requestTimes: [],
        errorTimes: [],
      })
    }
    return this.domainStats.get(domain)!
  }

  private cleanupOldStats(): void {
    const now = Date.now()
    const cutoffTime = now - this.globalConfig.windowSizeMs * 2

    for (const [domain, stats] of this.domainStats.entries()) {
      // Remove old request times
      stats.requestTimes = stats.requestTimes.filter(time => time > cutoffTime)
      stats.errorTimes = stats.errorTimes.filter(time => time > cutoffTime)

      // Reset consecutive errors if last error was long ago
      if (stats.errorTimes.length === 0) {
        stats.consecutiveErrors = 0
        stats.currentBackoffDelay = 0
      }

      // Unblock if block period has expired
      if (stats.isBlocked && now > stats.blockUntil) {
        stats.isBlocked = false
        stats.blockUntil = 0
        this.logger.info('Domain unblocked', { domain })
      }

      // Remove stats for domains with no recent activity
      if (stats.requestTimes.length === 0 && stats.lastRequestTime < cutoffTime) {
        this.domainStats.delete(domain)
      }
    }
  }

  private calculateDelay(domain: string, config: RateLimitConfig, stats: DomainStats): number {
    const now = Date.now()
    const windowStart = now - config.windowSizeMs

    // Filter recent requests
    const recentRequests = stats.requestTimes.filter(time => time > windowStart)
    
    // Check if blocked
    if (stats.isBlocked && now < stats.blockUntil) {
      return stats.blockUntil - now
    }

    // Calculate delays for different time windows
    const delays: number[] = []

    // Per-second rate limiting
    const lastSecondRequests = recentRequests.filter(time => time > now - 1000)
    if (lastSecondRequests.length >= config.maxRequestsPerSecond) {
      const oldestInSecond = Math.min(...lastSecondRequests)
      delays.push(1000 - (now - oldestInSecond))
    }

    // Per-minute rate limiting
    if (recentRequests.length >= config.maxRequestsPerMinute) {
      const oldestInWindow = Math.min(...recentRequests)
      delays.push(config.windowSizeMs - (now - oldestInWindow))
    }

    // Burst control
    const burstWindow = 5000 // 5 seconds
    const burstRequests = recentRequests.filter(time => time > now - burstWindow)
    if (burstRequests.length >= config.burstSize) {
      const oldestInBurst = Math.min(...burstRequests)
      delays.push(burstWindow - (now - oldestInBurst))
    }

    // Exponential backoff for consecutive errors
    if (stats.consecutiveErrors > 0) {
      const backoffDelay = Math.min(
        config.maxBackoffDelay,
        Math.pow(config.exponentialBackoffBase, stats.consecutiveErrors) * 1000
      )
      delays.push(backoffDelay)
    }

    // Minimum delay between requests
    const timeSinceLastRequest = now - stats.lastRequestTime
    const minDelay = 1000 / config.maxRequestsPerSecond
    if (timeSinceLastRequest < minDelay) {
      delays.push(minDelay - timeSinceLastRequest)
    }

    return Math.max(0, ...delays)
  }

  async waitForSlot(urlOrDomain: string): Promise<void> {
    const domain = urlOrDomain.includes('://') 
      ? this.extractDomain(urlOrDomain) 
      : urlOrDomain

    const config = this.getDomainConfig(domain)
    const stats = this.getDomainStats(domain)
    const now = Date.now()

    // Calculate required delay
    const delay = this.calculateDelay(domain, config, stats)

    if (delay > 0) {
      this.logger.debug('Rate limiting delay applied', {
        domain,
        delay,
        consecutiveErrors: stats.consecutiveErrors,
        recentRequests: stats.requestTimes.length,
      })

      this.emit('rateLimited', {
        domain,
        delay,
        reason: this.getRateLimitReason(domain, config, stats),
      })

      await this.sleep(delay)
    }

    // Record the request
    stats.requestTimes.push(now)
    stats.lastRequestTime = now
    stats.requestCount++

    this.emit('requestAllowed', {
      domain,
      requestCount: stats.requestCount,
      consecutiveErrors: stats.consecutiveErrors,
    })
  }

  private getRateLimitReason(domain: string, config: RateLimitConfig, stats: DomainStats): string {
    const now = Date.now()
    const recentRequests = stats.requestTimes.filter(time => time > now - config.windowSizeMs)
    const lastSecondRequests = recentRequests.filter(time => time > now - 1000)

    if (stats.isBlocked) {
      return 'domain_blocked'
    }
    if (stats.consecutiveErrors > 0) {
      return 'exponential_backoff'
    }
    if (lastSecondRequests.length >= config.maxRequestsPerSecond) {
      return 'per_second_limit'
    }
    if (recentRequests.length >= config.maxRequestsPerMinute) {
      return 'per_minute_limit'
    }
    return 'minimum_delay'
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  reportError(urlOrDomain: string, errorType: 'http_error' | 'timeout' | 'blocked' | 'captcha'): void {
    const domain = urlOrDomain.includes('://') 
      ? this.extractDomain(urlOrDomain) 
      : urlOrDomain

    const stats = this.getDomainStats(domain)
    const config = this.getDomainConfig(domain)
    const now = Date.now()

    stats.consecutiveErrors++
    stats.errorTimes.push(now)

    // Calculate new backoff delay
    stats.currentBackoffDelay = Math.min(
      config.maxBackoffDelay,
      Math.pow(config.exponentialBackoffBase, stats.consecutiveErrors) * 1000
    )

    // Block domain if too many consecutive errors
    if (stats.consecutiveErrors >= 5 || errorType === 'blocked') {
      stats.isBlocked = true
      stats.blockUntil = now + stats.currentBackoffDelay
      
      this.logger.warn('Domain blocked due to consecutive errors', {
        domain,
        consecutiveErrors: stats.consecutiveErrors,
        errorType,
        blockDuration: stats.currentBackoffDelay,
      })
    }

    this.emit('errorReported', {
      domain,
      errorType,
      consecutiveErrors: stats.consecutiveErrors,
      backoffDelay: stats.currentBackoffDelay,
      isBlocked: stats.isBlocked,
    })
  }

  reportSuccess(urlOrDomain: string): void {
    const domain = urlOrDomain.includes('://') 
      ? this.extractDomain(urlOrDomain) 
      : urlOrDomain

    const stats = this.getDomainStats(domain)

    // Reset error counters on success
    if (stats.consecutiveErrors > 0) {
      this.logger.debug('Resetting error count after successful request', {
        domain,
        previousErrors: stats.consecutiveErrors,
      })

      stats.consecutiveErrors = 0
      stats.currentBackoffDelay = 0
    }

    this.emit('successReported', {
      domain,
      requestCount: stats.requestCount,
    })
  }

  getDomainStatus(domain: string): {
    domain: string
    isBlocked: boolean
    consecutiveErrors: number
    requestCount: number
    recentRequestCount: number
    nextAvailableSlot: number
  } {
    const stats = this.getDomainStats(domain)
    const config = this.getDomainConfig(domain)
    const now = Date.now()
    const windowStart = now - config.windowSizeMs

    const recentRequests = stats.requestTimes.filter(time => time > windowStart)
    const delay = this.calculateDelay(domain, config, stats)

    return {
      domain,
      isBlocked: stats.isBlocked,
      consecutiveErrors: stats.consecutiveErrors,
      requestCount: stats.requestCount,
      recentRequestCount: recentRequests.length,
      nextAvailableSlot: now + delay,
    }
  }

  getAllDomainStats(): Map<string, DomainStats> {
    return new Map(this.domainStats)
  }

  setDomainConfig(domain: string, config: Partial<RateLimitConfig>): void {
    const existingConfig = this.getDomainConfig(domain)
    this.domainConfigs.set(domain, { ...existingConfig, ...config })
    
    this.logger.info('Domain rate limit configuration updated', {
      domain,
      config: { ...existingConfig, ...config },
    })
  }

  resetDomainStats(domain: string): void {
    this.domainStats.delete(domain)
    this.logger.info('Domain stats reset', { domain })
  }

  getGlobalStats(): {
    totalDomains: number
    blockedDomains: number
    totalRequests: number
    totalErrors: number
    avgRequestsPerDomain: number
  } {
    const stats = Array.from(this.domainStats.values())
    const blockedDomains = stats.filter(s => s.isBlocked).length
    const totalRequests = stats.reduce((sum, s) => sum + s.requestCount, 0)
    const totalErrors = stats.reduce((sum, s) => sum + s.errorTimes.length, 0)

    return {
      totalDomains: stats.length,
      blockedDomains,
      totalRequests,
      totalErrors,
      avgRequestsPerDomain: stats.length > 0 ? totalRequests / stats.length : 0,
    }
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.domainStats.clear()
    this.removeAllListeners()
  }
}
