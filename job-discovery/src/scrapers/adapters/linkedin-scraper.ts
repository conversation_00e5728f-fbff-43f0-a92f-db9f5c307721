/**
 * LinkedIn Jobs Scraper
 * Specialized scraper for LinkedIn job listings with anti-detection measures
 */

import { Page } from 'puppeteer'
import { Logger } from 'winston'
import { BaseScraper, JobListing, ScrapingConfig, ScrapingResult } from '../framework/base-scraper'
import { ProxyManager } from '../utils/proxy-manager'
import { RateLimiter } from '../utils/rate-limiter'

export interface LinkedInJobData {
  jobId: string
  title: string
  company: string
  location: string
  description: string
  postedDate: string
  applicationUrl: string
  salaryInfo?: string
  experienceLevel?: string
  employmentType?: string
  companySize?: string
  industry?: string
  skills?: string[]
}

export class LinkedInScraper extends BaseScraper {
  private readonly baseUrl = 'https://www.linkedin.com'
  private readonly jobSearchPath = '/jobs/search'

  constructor(
    logger: Logger,
    proxyManager: ProxyManager,
    rateLimiter: RateLimiter,
    customConfig?: Partial<ScrapingConfig>
  ) {
    const defaultConfig: ScrapingConfig = {
      source: 'linkedin',
      baseUrl: 'https://www.linkedin.com',
      maxConcurrency: 1, // LinkedIn is very strict
      requestDelay: 3000, // 3 seconds between requests
      retryAttempts: 3,
      timeout: 30000,
      useProxy: true,
      stealthMode: true,
      userAgentRotation: true,
      headless: 'new',
      viewport: { width: 1366, height: 768 },
      blockedResources: ['image', 'stylesheet', 'font', 'media'],
      customHeaders: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
      ...customConfig,
    }

    super(defaultConfig, logger, proxyManager, rateLimiter)
  }

  /**
   * Build LinkedIn job search URL with parameters
   */
  private buildSearchUrl(params: {
    keywords?: string
    location?: string
    experienceLevel?: string[]
    jobType?: string[]
    datePosted?: string
    sortBy?: string
    start?: number
  }): string {
    const searchParams = new URLSearchParams()

    if (params.keywords) {
      searchParams.append('keywords', params.keywords)
    }
    if (params.location) {
      searchParams.append('location', params.location)
    }
    if (params.experienceLevel?.length) {
      searchParams.append('f_E', params.experienceLevel.join(','))
    }
    if (params.jobType?.length) {
      searchParams.append('f_JT', params.jobType.join(','))
    }
    if (params.datePosted) {
      searchParams.append('f_TPR', params.datePosted)
    }
    if (params.sortBy) {
      searchParams.append('sortBy', params.sortBy)
    }
    if (params.start) {
      searchParams.append('start', params.start.toString())
    }

    return `${this.baseUrl}${this.jobSearchPath}?${searchParams.toString()}`
  }

  /**
   * Handle LinkedIn's authentication and bot detection
   */
  private async handleLinkedInAuth(page: Page): Promise<void> {
    const currentUrl = page.url()

    // Check if redirected to login page
    if (currentUrl.includes('/login') || currentUrl.includes('/uas/login')) {
      this.logger.warn('LinkedIn redirected to login page', {
        currentUrl,
        source: this.config.source,
      })

      // Try to access jobs without login (public job listings)
      await page.goto(`${this.baseUrl}/jobs/search?keywords=software+engineer`, {
        waitUntil: 'networkidle2',
      })
    }

    // Check for CAPTCHA or verification
    const captchaSelectors = [
      '[data-test-id="captcha"]',
      '.challenge-form',
      '#captcha-internal',
      '.captcha-container',
    ]

    for (const selector of captchaSelectors) {
      const captchaElement = await page.$(selector)
      if (captchaElement) {
        this.logger.warn('LinkedIn CAPTCHA detected', {
          selector,
          source: this.config.source,
        })
        throw new Error('CAPTCHA detected - manual intervention required')
      }
    }

    // Check for bot detection messages
    const botDetectionTexts = [
      'unusual traffic',
      'automated requests',
      'verify you are human',
      'security check',
    ]

    const pageContent = await page.content()
    for (const text of botDetectionTexts) {
      if (pageContent.toLowerCase().includes(text)) {
        this.logger.warn('LinkedIn bot detection triggered', {
          detectedText: text,
          source: this.config.source,
        })
        throw new Error(`Bot detection: ${text}`)
      }
    }
  }

  /**
   * Extract job listings from LinkedIn search results page
   */
  protected async extractJobListings(page: Page): Promise<JobListing[]> {
    await this.handleLinkedInAuth(page)

    // Wait for job listings to load
    try {
      await page.waitForSelector('.jobs-search__results-list', { timeout: 10000 })
    } catch (error) {
      this.logger.warn('Job listings container not found', {
        url: page.url(),
        source: this.config.source,
      })
      return []
    }

    // Extract job cards
    const jobCards = await page.$$('.base-card.relative.w-full.hover\\:no-underline.focus\\:no-underline.base-card--link.base-search-card.base-search-card--link.job-search-card')

    const jobs: JobListing[] = []

    for (const card of jobCards) {
      try {
        const jobData = await this.extractJobFromCard(page, card)
        if (jobData) {
          const job = await this.convertToJobListing(jobData)
          jobs.push(job)
        }
      } catch (error) {
        this.logger.debug('Failed to extract job from card', {
          error: error instanceof Error ? error.message : 'Unknown error',
          source: this.config.source,
        })
      }
    }

    this.logger.info('Extracted jobs from LinkedIn page', {
      jobCount: jobs.length,
      url: page.url(),
      source: this.config.source,
    })

    return jobs
  }

  private async extractJobFromCard(page: Page, card: any): Promise<LinkedInJobData | null> {
    try {
      // Extract basic job information
      const titleElement = await card.$('.base-search-card__title')
      const title = titleElement ? await page.evaluate(el => el.textContent?.trim(), titleElement) : ''

      const companyElement = await card.$('.base-search-card__subtitle')
      const company = companyElement ? await page.evaluate(el => el.textContent?.trim(), companyElement) : ''

      const locationElement = await card.$('.job-search-card__location')
      const location = locationElement ? await page.evaluate(el => el.textContent?.trim(), locationElement) : ''

      const linkElement = await card.$('a[data-control-name="job_search_job_result_click"]')
      const jobUrl = linkElement ? await page.evaluate(el => el.href, linkElement) : ''

      // Extract job ID from URL
      const jobIdMatch = jobUrl.match(/\/view\/(\d+)/)
      const jobId = jobIdMatch ? jobIdMatch[1] : ''

      if (!title || !company || !jobId) {
        return null
      }

      // Extract additional information if available
      const postedDateElement = await card.$('.job-search-card__listdate')
      const postedDate = postedDateElement 
        ? await page.evaluate(el => el.textContent?.trim(), postedDateElement) 
        : ''

      // Get detailed job information by visiting the job page
      const detailedData = await this.getJobDetails(page, jobUrl)

      return {
        jobId,
        title,
        company,
        location,
        description: detailedData?.description || '',
        postedDate,
        applicationUrl: jobUrl,
        salaryInfo: detailedData?.salaryInfo,
        experienceLevel: detailedData?.experienceLevel,
        employmentType: detailedData?.employmentType,
        companySize: detailedData?.companySize,
        industry: detailedData?.industry,
        skills: detailedData?.skills || [],
      }
    } catch (error) {
      this.logger.debug('Error extracting job card data', {
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      return null
    }
  }

  private async getJobDetails(page: Page, jobUrl: string): Promise<{
    description: string
    salaryInfo?: string
    experienceLevel?: string
    employmentType?: string
    companySize?: string
    industry?: string
    skills?: string[]
  } | null> {
    try {
      // Navigate to job details page
      await page.goto(jobUrl, { waitUntil: 'networkidle2' })
      await this.handleLinkedInAuth(page)

      // Wait for job description to load
      await page.waitForSelector('.show-more-less-html__markup', { timeout: 5000 })

      // Extract job description
      const descriptionElement = await page.$('.show-more-less-html__markup')
      const description = descriptionElement 
        ? await page.evaluate(el => el.textContent?.trim(), descriptionElement) 
        : ''

      // Extract job criteria
      const criteriaElements = await page.$$('.description__job-criteria-item')
      const criteria: Record<string, string> = {}

      for (const element of criteriaElements) {
        const labelElement = await element.$('.description__job-criteria-subheader')
        const valueElement = await element.$('.description__job-criteria-text')

        if (labelElement && valueElement) {
          const label = await page.evaluate(el => el.textContent?.trim(), labelElement)
          const value = await page.evaluate(el => el.textContent?.trim(), valueElement)
          
          if (label && value) {
            criteria[label.toLowerCase()] = value
          }
        }
      }

      // Extract skills from job description
      const skills = this.extractSkillsFromDescription(description)

      return {
        description,
        experienceLevel: criteria['seniority level'],
        employmentType: criteria['employment type'],
        industry: criteria['industries'],
        skills,
      }
    } catch (error) {
      this.logger.debug('Failed to get job details', {
        jobUrl,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      return null
    }
  }

  private extractSkillsFromDescription(description: string): string[] {
    const commonSkills = [
      'JavaScript', 'TypeScript', 'Python', 'Java', 'C++', 'C#', 'Go', 'Rust',
      'React', 'Vue', 'Angular', 'Node.js', 'Express', 'Django', 'Flask',
      'AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'Jenkins', 'Git',
      'SQL', 'PostgreSQL', 'MySQL', 'MongoDB', 'Redis', 'Elasticsearch',
      'Machine Learning', 'AI', 'Data Science', 'Analytics', 'TensorFlow', 'PyTorch',
    ]

    const foundSkills: string[] = []
    const lowerDescription = description.toLowerCase()

    for (const skill of commonSkills) {
      if (lowerDescription.includes(skill.toLowerCase())) {
        foundSkills.push(skill)
      }
    }

    return foundSkills
  }

  private async convertToJobListing(data: LinkedInJobData): Promise<JobListing> {
    const postedDate = this.parseLinkedInDate(data.postedDate)
    
    return {
      id: `linkedin_${data.jobId}`,
      title: data.title,
      company: data.company,
      location: data.location,
      description: data.description,
      requirements: this.extractRequirements(data.description),
      benefits: this.extractBenefits(data.description),
      employment_type: this.mapEmploymentType(data.employmentType),
      experience_level: this.mapExperienceLevel(data.experienceLevel),
      remote_option: this.detectRemoteOption(data.location, data.description),
      posted_date: postedDate,
      application_url: data.applicationUrl,
      source: 'linkedin',
      source_id: data.jobId,
      skills: data.skills || [],
      industry: data.industry || '',
      raw_data: data,
      scraped_at: new Date(),
    }
  }

  private parseLinkedInDate(dateString: string): Date {
    const now = new Date()
    
    if (dateString.includes('hour')) {
      const hours = parseInt(dateString.match(/(\d+)/)?.[1] || '1')
      return new Date(now.getTime() - hours * 60 * 60 * 1000)
    }
    
    if (dateString.includes('day')) {
      const days = parseInt(dateString.match(/(\d+)/)?.[1] || '1')
      return new Date(now.getTime() - days * 24 * 60 * 60 * 1000)
    }
    
    if (dateString.includes('week')) {
      const weeks = parseInt(dateString.match(/(\d+)/)?.[1] || '1')
      return new Date(now.getTime() - weeks * 7 * 24 * 60 * 60 * 1000)
    }
    
    return now
  }

  private extractRequirements(description: string): string[] {
    const requirementPatterns = [
      /requirements?:?\s*(.+?)(?:\n\n|\n[A-Z]|$)/gi,
      /qualifications?:?\s*(.+?)(?:\n\n|\n[A-Z]|$)/gi,
      /must have:?\s*(.+?)(?:\n\n|\n[A-Z]|$)/gi,
    ]

    const requirements: string[] = []
    
    for (const pattern of requirementPatterns) {
      const matches = description.match(pattern)
      if (matches) {
        matches.forEach(match => {
          const items = match.split(/[•\-\*\n]/).filter(item => item.trim().length > 10)
          requirements.push(...items.map(item => item.trim()))
        })
      }
    }

    return requirements.slice(0, 10) // Limit to 10 requirements
  }

  private extractBenefits(description: string): string[] {
    const benefitPatterns = [
      /benefits?:?\s*(.+?)(?:\n\n|\n[A-Z]|$)/gi,
      /we offer:?\s*(.+?)(?:\n\n|\n[A-Z]|$)/gi,
      /perks?:?\s*(.+?)(?:\n\n|\n[A-Z]|$)/gi,
    ]

    const benefits: string[] = []
    
    for (const pattern of benefitPatterns) {
      const matches = description.match(pattern)
      if (matches) {
        matches.forEach(match => {
          const items = match.split(/[•\-\*\n]/).filter(item => item.trim().length > 5)
          benefits.push(...items.map(item => item.trim()))
        })
      }
    }

    return benefits.slice(0, 10) // Limit to 10 benefits
  }

  private mapEmploymentType(type?: string): JobListing['employment_type'] {
    if (!type) return 'full-time'
    
    const lowerType = type.toLowerCase()
    if (lowerType.includes('part')) return 'part-time'
    if (lowerType.includes('contract')) return 'contract'
    if (lowerType.includes('intern')) return 'internship'
    if (lowerType.includes('temp')) return 'temporary'
    
    return 'full-time'
  }

  private mapExperienceLevel(level?: string): JobListing['experience_level'] {
    if (!level) return 'mid'
    
    const lowerLevel = level.toLowerCase()
    if (lowerLevel.includes('entry') || lowerLevel.includes('junior')) return 'entry'
    if (lowerLevel.includes('senior') || lowerLevel.includes('lead')) return 'senior'
    if (lowerLevel.includes('executive') || lowerLevel.includes('director')) return 'executive'
    
    return 'mid'
  }

  private detectRemoteOption(location: string, description: string): JobListing['remote_option'] {
    const text = `${location} ${description}`.toLowerCase()
    
    if (text.includes('remote') && !text.includes('no remote')) {
      if (text.includes('hybrid') || text.includes('flexible')) {
        return 'hybrid'
      }
      return 'remote'
    }
    
    return 'onsite'
  }

  /**
   * Get pagination URLs from LinkedIn search results
   */
  protected async getPaginationUrls(page: Page): Promise<string[]> {
    const urls: string[] = []
    
    try {
      // LinkedIn uses "start" parameter for pagination
      const currentUrl = new URL(page.url())
      const currentStart = parseInt(currentUrl.searchParams.get('start') || '0')
      const nextStart = currentStart + 25 // LinkedIn shows 25 jobs per page
      
      // Check if there are more results
      const hasNextPage = await page.$('.artdeco-pagination__button--next:not([disabled])')
      
      if (hasNextPage) {
        currentUrl.searchParams.set('start', nextStart.toString())
        urls.push(currentUrl.toString())
      }
    } catch (error) {
      this.logger.debug('Failed to get pagination URLs', {
        error: error instanceof Error ? error.message : 'Unknown error',
        source: this.config.source,
      })
    }
    
    return urls
  }

  /**
   * Search for jobs with specific criteria
   */
  async searchJobs(params: {
    keywords?: string
    location?: string
    experienceLevel?: string[]
    jobType?: string[]
    datePosted?: string
    maxPages?: number
  }): Promise<ScrapingResult> {
    const searchUrl = this.buildSearchUrl(params)
    
    this.logger.info('Starting LinkedIn job search', {
      searchUrl,
      params,
      source: this.config.source,
    })

    return this.scrape(searchUrl, params.maxPages || 5)
  }
}
