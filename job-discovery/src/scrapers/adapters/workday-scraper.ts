/**
 * Workday Jobs Scraper
 * Generic scraper for Workday-powered career sites
 */

import { Page } from 'puppeteer'
import { Logger } from 'winston'
import { BaseScraper, JobListing, ScrapingConfig, ScrapingResult } from '../framework/base-scraper'
import { ProxyManager } from '../utils/proxy-manager'
import { RateLimiter } from '../utils/rate-limiter'

export interface WorkdayJobData {
  jobId: string
  title: string
  company: string
  location: string
  description: string
  postedDate: string
  applicationUrl: string
  jobType?: string
  category?: string
  department?: string
  requisitionId?: string
}

export interface WorkdayConfig {
  companyName: string
  baseUrl: string
  jobSearchPath: string
  customSelectors?: {
    jobCard?: string
    title?: string
    location?: string
    description?: string
    applyButton?: string
  }
}

export class WorkdayScraper extends BaseScraper {
  private workdayConfig: WorkdayConfig

  constructor(
    workdayConfig: WorkdayConfig,
    logger: Logger,
    proxyManager: ProxyManager,
    rateLimiter: RateLimiter,
    customConfig?: Partial<ScrapingConfig>
  ) {
    const defaultConfig: ScrapingConfig = {
      source: `workday_${workdayConfig.companyName.toLowerCase().replace(/\s+/g, '_')}`,
      baseUrl: workdayConfig.baseUrl,
      maxConcurrency: 2,
      requestDelay: 2000,
      retryAttempts: 3,
      timeout: 20000,
      useProxy: true,
      stealthMode: true,
      userAgentRotation: true,
      headless: 'new',
      viewport: { width: 1920, height: 1080 },
      blockedResources: ['image', 'stylesheet', 'font'],
      customHeaders: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
      ...customConfig,
    }

    super(defaultConfig, logger, proxyManager, rateLimiter)
    this.workdayConfig = workdayConfig
  }

  /**
   * Build Workday search URL with filters
   */
  private buildSearchUrl(params: {
    keywords?: string
    location?: string
    category?: string
    jobType?: string
    offset?: number
  }): string {
    const url = new URL(this.workdayConfig.jobSearchPath, this.workdayConfig.baseUrl)
    
    if (params.keywords) {
      url.searchParams.append('q', params.keywords)
    }
    if (params.location) {
      url.searchParams.append('location', params.location)
    }
    if (params.category) {
      url.searchParams.append('category', params.category)
    }
    if (params.jobType) {
      url.searchParams.append('jobType', params.jobType)
    }
    if (params.offset) {
      url.searchParams.append('offset', params.offset.toString())
    }

    return url.toString()
  }

  /**
   * Wait for Workday page to load completely
   */
  private async waitForWorkdayLoad(page: Page): Promise<void> {
    // Wait for Workday's dynamic content to load
    await page.waitForFunction(
      () => {
        // Check if loading indicators are gone
        const loadingElements = document.querySelectorAll('[data-automation-id="loadingIndicator"]')
        return loadingElements.length === 0
      },
      { timeout: 15000 }
    )

    // Additional wait for job listings
    try {
      await page.waitForSelector('[data-automation-id="jobPostingItem"]', { timeout: 10000 })
    } catch (error) {
      // Try alternative selectors
      const alternativeSelectors = [
        '.css-1q2dra3',
        '[data-automation-id="job-posting"]',
        '.job-tile',
        '.job-card',
      ]

      for (const selector of alternativeSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 5000 })
          break
        } catch {
          continue
        }
      }
    }
  }

  /**
   * Extract job listings from Workday search results
   */
  protected async extractJobListings(page: Page): Promise<JobListing[]> {
    await this.waitForWorkdayLoad(page)

    // Try multiple selectors for job cards
    const jobCardSelectors = [
      this.workdayConfig.customSelectors?.jobCard || '[data-automation-id="jobPostingItem"]',
      '.css-1q2dra3',
      '[data-automation-id="job-posting"]',
      '.job-tile',
      '.job-card',
    ]

    let jobCards: any[] = []
    for (const selector of jobCardSelectors) {
      jobCards = await page.$$(selector)
      if (jobCards.length > 0) {
        this.logger.debug('Found job cards with selector', {
          selector,
          count: jobCards.length,
          source: this.config.source,
        })
        break
      }
    }

    if (jobCards.length === 0) {
      this.logger.warn('No job cards found on page', {
        url: page.url(),
        source: this.config.source,
      })
      return []
    }

    const jobs: JobListing[] = []

    for (const card of jobCards) {
      try {
        const jobData = await this.extractJobFromCard(page, card)
        if (jobData) {
          const job = await this.convertToJobListing(jobData)
          jobs.push(job)
        }
      } catch (error) {
        this.logger.debug('Failed to extract job from card', {
          error: error instanceof Error ? error.message : 'Unknown error',
          source: this.config.source,
        })
      }
    }

    this.logger.info('Extracted jobs from Workday page', {
      jobCount: jobs.length,
      company: this.workdayConfig.companyName,
      url: page.url(),
      source: this.config.source,
    })

    return jobs
  }

  private async extractJobFromCard(page: Page, card: any): Promise<WorkdayJobData | null> {
    try {
      // Extract title
      const titleSelectors = [
        this.workdayConfig.customSelectors?.title || '[data-automation-id="jobPostingTitle"]',
        '.css-cygeeu',
        'h3 a',
        '.job-title',
        '.posting-title',
      ]

      let title = ''
      for (const selector of titleSelectors) {
        const titleElement = await card.$(selector)
        if (titleElement) {
          title = await page.evaluate(el => el.textContent?.trim() || '', titleElement)
          if (title) break
        }
      }

      // Extract location
      const locationSelectors = [
        this.workdayConfig.customSelectors?.location || '[data-automation-id="jobPostingLocation"]',
        '.css-129m7dg',
        '.job-location',
        '.posting-location',
      ]

      let location = ''
      for (const selector of locationSelectors) {
        const locationElement = await card.$(selector)
        if (locationElement) {
          location = await page.evaluate(el => el.textContent?.trim() || '', locationElement)
          if (location) break
        }
      }

      // Extract job link
      const linkElement = await card.$('a[href*="/job/"]') || await card.$('a')
      const jobUrl = linkElement ? await page.evaluate(el => el.href, linkElement) : ''

      // Extract job ID from URL or data attributes
      let jobId = ''
      if (jobUrl) {
        const jobIdMatch = jobUrl.match(/\/job\/([^\/\?]+)/) || jobUrl.match(/jobId=([^&]+)/)
        jobId = jobIdMatch ? jobIdMatch[1] : ''
      }

      if (!jobId) {
        // Try to get from data attributes
        jobId = await page.evaluate(el => {
          return el.getAttribute('data-job-id') || 
                 el.getAttribute('data-automation-id') || 
                 Math.random().toString(36).substr(2, 9)
        }, card)
      }

      if (!title || !jobId) {
        return null
      }

      // Extract additional metadata
      const postedDateElement = await card.$('[data-automation-id="postedDate"]')
      const postedDate = postedDateElement 
        ? await page.evaluate(el => el.textContent?.trim() || '', postedDateElement)
        : ''

      const jobTypeElement = await card.$('[data-automation-id="jobType"]')
      const jobType = jobTypeElement 
        ? await page.evaluate(el => el.textContent?.trim() || '', jobTypeElement)
        : ''

      // Get full job details if URL is available
      let description = ''
      let additionalData = {}

      if (jobUrl && !jobUrl.startsWith('javascript:')) {
        try {
          additionalData = await this.getJobDetails(page, jobUrl)
          description = (additionalData as any).description || ''
        } catch (error) {
          this.logger.debug('Failed to get job details', {
            jobUrl,
            error: error instanceof Error ? error.message : 'Unknown error',
          })
        }
      }

      return {
        jobId,
        title,
        company: this.workdayConfig.companyName,
        location: location || 'Not specified',
        description,
        postedDate,
        applicationUrl: jobUrl || `${this.workdayConfig.baseUrl}/job/${jobId}`,
        jobType,
        ...additionalData,
      }
    } catch (error) {
      this.logger.debug('Error extracting job card data', {
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      return null
    }
  }

  private async getJobDetails(page: Page, jobUrl: string): Promise<{
    description: string
    category?: string
    department?: string
    requisitionId?: string
  }> {
    const originalUrl = page.url()
    
    try {
      await page.goto(jobUrl, { waitUntil: 'networkidle2' })
      await this.waitForWorkdayLoad(page)

      // Extract job description
      const descriptionSelectors = [
        this.workdayConfig.customSelectors?.description || '[data-automation-id="jobPostingDescription"]',
        '.css-1t5f0fr',
        '.job-description',
        '.posting-description',
        '#jobdescription',
      ]

      let description = ''
      for (const selector of descriptionSelectors) {
        const descElement = await page.$(selector)
        if (descElement) {
          description = await page.evaluate(el => el.textContent?.trim() || '', descElement)
          if (description) break
        }
      }

      // Extract additional job metadata
      const metadataSelectors = {
        category: '[data-automation-id="jobCategory"]',
        department: '[data-automation-id="department"]',
        requisitionId: '[data-automation-id="requisitionId"]',
      }

      const metadata: any = {}
      for (const [key, selector] of Object.entries(metadataSelectors)) {
        const element = await page.$(selector)
        if (element) {
          metadata[key] = await page.evaluate(el => el.textContent?.trim() || '', element)
        }
      }

      return {
        description,
        ...metadata,
      }
    } catch (error) {
      this.logger.debug('Failed to extract job details', {
        jobUrl,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      return { description: '' }
    } finally {
      // Navigate back to search results
      try {
        await page.goto(originalUrl, { waitUntil: 'networkidle2' })
      } catch (error) {
        // Ignore navigation errors
      }
    }
  }

  private async convertToJobListing(data: WorkdayJobData): Promise<JobListing> {
    const postedDate = this.parseWorkdayDate(data.postedDate)
    
    return {
      id: `${this.config.source}_${data.jobId}`,
      title: data.title,
      company: data.company,
      location: data.location,
      description: data.description,
      requirements: this.extractRequirements(data.description),
      benefits: this.extractBenefits(data.description),
      employment_type: this.mapEmploymentType(data.jobType),
      experience_level: this.inferExperienceLevel(data.title, data.description),
      remote_option: this.detectRemoteOption(data.location, data.description),
      posted_date: postedDate,
      application_url: data.applicationUrl,
      source: this.config.source,
      source_id: data.jobId,
      skills: this.extractSkills(data.description),
      industry: data.category || '',
      raw_data: data,
      scraped_at: new Date(),
    }
  }

  private parseWorkdayDate(dateString: string): Date {
    const now = new Date()
    
    if (!dateString) return now
    
    // Handle relative dates
    if (dateString.includes('day')) {
      const days = parseInt(dateString.match(/(\d+)/)?.[1] || '1')
      return new Date(now.getTime() - days * 24 * 60 * 60 * 1000)
    }
    
    if (dateString.includes('week')) {
      const weeks = parseInt(dateString.match(/(\d+)/)?.[1] || '1')
      return new Date(now.getTime() - weeks * 7 * 24 * 60 * 60 * 1000)
    }
    
    // Try to parse absolute dates
    try {
      return new Date(dateString)
    } catch {
      return now
    }
  }

  private extractRequirements(description: string): string[] {
    const patterns = [
      /(?:requirements?|qualifications?|must have):?\s*(.+?)(?:\n\n|\n[A-Z]|$)/gi,
      /(?:required|essential):?\s*(.+?)(?:\n\n|\n[A-Z]|$)/gi,
    ]

    const requirements: string[] = []
    
    for (const pattern of patterns) {
      const matches = description.match(pattern)
      if (matches) {
        matches.forEach(match => {
          const items = match.split(/[•\-\*\n]/).filter(item => item.trim().length > 10)
          requirements.push(...items.map(item => item.trim()))
        })
      }
    }

    return requirements.slice(0, 8)
  }

  private extractBenefits(description: string): string[] {
    const patterns = [
      /(?:benefits?|we offer|perks?):?\s*(.+?)(?:\n\n|\n[A-Z]|$)/gi,
    ]

    const benefits: string[] = []
    
    for (const pattern of patterns) {
      const matches = description.match(pattern)
      if (matches) {
        matches.forEach(match => {
          const items = match.split(/[•\-\*\n]/).filter(item => item.trim().length > 5)
          benefits.push(...items.map(item => item.trim()))
        })
      }
    }

    return benefits.slice(0, 8)
  }

  private extractSkills(description: string): string[] {
    const skillKeywords = [
      'JavaScript', 'TypeScript', 'Python', 'Java', 'C#', 'Go', 'Rust', 'PHP',
      'React', 'Vue', 'Angular', 'Node.js', 'Django', 'Flask', 'Spring',
      'AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'Jenkins', 'Git',
      'SQL', 'PostgreSQL', 'MySQL', 'MongoDB', 'Redis',
      'Machine Learning', 'AI', 'Data Science', 'Analytics',
    ]

    const foundSkills: string[] = []
    const lowerDescription = description.toLowerCase()

    for (const skill of skillKeywords) {
      if (lowerDescription.includes(skill.toLowerCase())) {
        foundSkills.push(skill)
      }
    }

    return foundSkills
  }

  private mapEmploymentType(type?: string): JobListing['employment_type'] {
    if (!type) return 'full-time'
    
    const lowerType = type.toLowerCase()
    if (lowerType.includes('part')) return 'part-time'
    if (lowerType.includes('contract')) return 'contract'
    if (lowerType.includes('intern')) return 'internship'
    if (lowerType.includes('temp')) return 'temporary'
    
    return 'full-time'
  }

  private inferExperienceLevel(title: string, description: string): JobListing['experience_level'] {
    const text = `${title} ${description}`.toLowerCase()
    
    if (text.includes('senior') || text.includes('lead') || text.includes('principal')) {
      return 'senior'
    }
    if (text.includes('junior') || text.includes('entry') || text.includes('associate')) {
      return 'entry'
    }
    if (text.includes('director') || text.includes('vp') || text.includes('executive')) {
      return 'executive'
    }
    
    return 'mid'
  }

  private detectRemoteOption(location: string, description: string): JobListing['remote_option'] {
    const text = `${location} ${description}`.toLowerCase()
    
    if (text.includes('remote')) {
      if (text.includes('hybrid') || text.includes('flexible')) {
        return 'hybrid'
      }
      return 'remote'
    }
    
    return 'onsite'
  }

  /**
   * Get pagination URLs from Workday search results
   */
  protected async getPaginationUrls(page: Page): Promise<string[]> {
    const urls: string[] = []
    
    try {
      // Check for next page button
      const nextButton = await page.$('[data-automation-id="nextPage"]') || 
                        await page.$('.css-1hwfws3[aria-label*="next"]')
      
      if (nextButton) {
        const isDisabled = await page.evaluate(el => 
          el.hasAttribute('disabled') || el.getAttribute('aria-disabled') === 'true'
        , nextButton)
        
        if (!isDisabled) {
          // Calculate next page URL
          const currentUrl = new URL(page.url())
          const currentOffset = parseInt(currentUrl.searchParams.get('offset') || '0')
          const nextOffset = currentOffset + 20 // Workday typically shows 20 jobs per page
          
          currentUrl.searchParams.set('offset', nextOffset.toString())
          urls.push(currentUrl.toString())
        }
      }
    } catch (error) {
      this.logger.debug('Failed to get pagination URLs', {
        error: error instanceof Error ? error.message : 'Unknown error',
        source: this.config.source,
      })
    }
    
    return urls
  }

  /**
   * Search for jobs with specific criteria
   */
  async searchJobs(params: {
    keywords?: string
    location?: string
    category?: string
    jobType?: string
    maxPages?: number
  }): Promise<ScrapingResult> {
    const searchUrl = this.buildSearchUrl(params)
    
    this.logger.info('Starting Workday job search', {
      searchUrl,
      company: this.workdayConfig.companyName,
      params,
      source: this.config.source,
    })

    return this.scrape(searchUrl, params.maxPages || 5)
  }

  /**
   * Create Workday scraper for specific company
   */
  static createForCompany(
    companyName: string,
    baseUrl: string,
    jobSearchPath: string,
    logger: Logger,
    proxyManager: ProxyManager,
    rateLimiter: RateLimiter,
    customSelectors?: WorkdayConfig['customSelectors']
  ): WorkdayScraper {
    const config: WorkdayConfig = {
      companyName,
      baseUrl,
      jobSearchPath,
      customSelectors,
    }

    return new WorkdayScraper(config, logger, proxyManager, rateLimiter)
  }
}
