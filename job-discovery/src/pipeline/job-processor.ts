/**
 * Job Processing Pipeline
 * Handles deduplication, normalization, validation, and enrichment
 */

import { Logger } from 'winston'
import Jo<PERSON> from 'joi'
import { JobListing } from '../scrapers/framework/base-scraper'
import { DeduplicationEngine } from './deduplication/deduplication-engine'
import { NormalizationEngine } from './normalization/normalization-engine'
import { EnrichmentEngine } from './enrichment/enrichment-engine'
import { ValidationEngine } from './validation/validation-engine'
import { GeocodingService } from './enrichment/geocoding-service'
import { SalaryEstimationService } from './enrichment/salary-estimation-service'
import { SkillExtractionService } from './enrichment/skill-extraction-service'

export interface ProcessingConfig {
  enableDeduplication: boolean
  enableNormalization: boolean
  enableEnrichment: boolean
  enableValidation: boolean
  qualityThreshold: number
  batchSize: number
  maxRetries: number
}

export interface ProcessingResult {
  processed: JobListing[]
  duplicates: JobListing[]
  invalid: JobListing[]
  errors: ProcessingError[]
  stats: ProcessingStats
}

export interface ProcessingError {
  jobId: string
  stage: string
  error: string
  timestamp: Date
}

export interface ProcessingStats {
  totalJobs: number
  processedJobs: number
  duplicateJobs: number
  invalidJobs: number
  processingTimeMs: number
  averageQualityScore: number
  stageTimings: Record<string, number>
}

export class JobProcessor {
  private logger: Logger
  private config: ProcessingConfig
  private deduplicationEngine: DeduplicationEngine
  private normalizationEngine: NormalizationEngine
  private enrichmentEngine: EnrichmentEngine
  private validationEngine: ValidationEngine

  constructor(
    logger: Logger,
    config: Partial<ProcessingConfig> = {}
  ) {
    this.logger = logger
    this.config = {
      enableDeduplication: true,
      enableNormalization: true,
      enableEnrichment: true,
      enableValidation: true,
      qualityThreshold: 0.7,
      batchSize: 100,
      maxRetries: 3,
      ...config,
    }

    this.initializeEngines()
  }

  private initializeEngines(): void {
    this.deduplicationEngine = new DeduplicationEngine(this.logger)
    this.normalizationEngine = new NormalizationEngine(this.logger)
    this.validationEngine = new ValidationEngine(this.logger)
    
    // Initialize enrichment services
    const geocodingService = new GeocodingService(this.logger)
    const salaryService = new SalaryEstimationService(this.logger)
    const skillService = new SkillExtractionService(this.logger)
    
    this.enrichmentEngine = new EnrichmentEngine(
      this.logger,
      geocodingService,
      salaryService,
      skillService
    )
  }

  /**
   * Process a batch of job listings through the complete pipeline
   */
  async processBatch(jobs: JobListing[]): Promise<ProcessingResult> {
    const startTime = Date.now()
    const stageTimings: Record<string, number> = {}
    const errors: ProcessingError[] = []
    
    this.logger.info('Starting job processing batch', {
      jobCount: jobs.length,
      config: this.config,
    })

    let processedJobs = [...jobs]
    let duplicates: JobListing[] = []
    let invalid: JobListing[] = []

    try {
      // Stage 1: Validation
      if (this.config.enableValidation) {
        const validationStart = Date.now()
        const validationResult = await this.runValidation(processedJobs)
        processedJobs = validationResult.valid
        invalid = validationResult.invalid
        errors.push(...validationResult.errors)
        stageTimings.validation = Date.now() - validationStart
      }

      // Stage 2: Normalization
      if (this.config.enableNormalization && processedJobs.length > 0) {
        const normalizationStart = Date.now()
        const normalizationResult = await this.runNormalization(processedJobs)
        processedJobs = normalizationResult.normalized
        errors.push(...normalizationResult.errors)
        stageTimings.normalization = Date.now() - normalizationStart
      }

      // Stage 3: Deduplication
      if (this.config.enableDeduplication && processedJobs.length > 0) {
        const deduplicationStart = Date.now()
        const deduplicationResult = await this.runDeduplication(processedJobs)
        processedJobs = deduplicationResult.unique
        duplicates = deduplicationResult.duplicates
        errors.push(...deduplicationResult.errors)
        stageTimings.deduplication = Date.now() - deduplicationStart
      }

      // Stage 4: Enrichment
      if (this.config.enableEnrichment && processedJobs.length > 0) {
        const enrichmentStart = Date.now()
        const enrichmentResult = await this.runEnrichment(processedJobs)
        processedJobs = enrichmentResult.enriched
        errors.push(...enrichmentResult.errors)
        stageTimings.enrichment = Date.now() - enrichmentStart
      }

      // Stage 5: Quality filtering
      const qualityStart = Date.now()
      const qualityResult = this.filterByQuality(processedJobs)
      processedJobs = qualityResult.passed
      invalid.push(...qualityResult.failed)
      stageTimings.quality = Date.now() - qualityStart

    } catch (error) {
      this.logger.error('Critical error in job processing pipeline', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stage: 'pipeline',
      })
      
      errors.push({
        jobId: 'batch',
        stage: 'pipeline',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      })
    }

    const totalTime = Date.now() - startTime
    const averageQualityScore = processedJobs.length > 0
      ? processedJobs.reduce((sum, job) => sum + (job.quality_score || 0), 0) / processedJobs.length
      : 0

    const stats: ProcessingStats = {
      totalJobs: jobs.length,
      processedJobs: processedJobs.length,
      duplicateJobs: duplicates.length,
      invalidJobs: invalid.length,
      processingTimeMs: totalTime,
      averageQualityScore,
      stageTimings,
    }

    this.logger.info('Job processing batch completed', {
      stats,
      errorCount: errors.length,
    })

    return {
      processed: processedJobs,
      duplicates,
      invalid,
      errors,
      stats,
    }
  }

  private async runValidation(jobs: JobListing[]): Promise<{
    valid: JobListing[]
    invalid: JobListing[]
    errors: ProcessingError[]
  }> {
    const valid: JobListing[] = []
    const invalid: JobListing[] = []
    const errors: ProcessingError[] = []

    for (const job of jobs) {
      try {
        const validationResult = await this.validationEngine.validate(job)
        
        if (validationResult.isValid) {
          valid.push({
            ...job,
            metadata: {
              ...job.metadata,
              validation_errors: validationResult.errors,
            },
          })
        } else {
          invalid.push(job)
          errors.push({
            jobId: job.id,
            stage: 'validation',
            error: validationResult.errors.join(', '),
            timestamp: new Date(),
          })
        }
      } catch (error) {
        invalid.push(job)
        errors.push({
          jobId: job.id,
          stage: 'validation',
          error: error instanceof Error ? error.message : 'Validation failed',
          timestamp: new Date(),
        })
      }
    }

    return { valid, invalid, errors }
  }

  private async runNormalization(jobs: JobListing[]): Promise<{
    normalized: JobListing[]
    errors: ProcessingError[]
  }> {
    const normalized: JobListing[] = []
    const errors: ProcessingError[] = []

    for (const job of jobs) {
      try {
        const normalizedJob = await this.normalizationEngine.normalize(job)
        normalized.push(normalizedJob)
      } catch (error) {
        // Keep original job if normalization fails
        normalized.push(job)
        errors.push({
          jobId: job.id,
          stage: 'normalization',
          error: error instanceof Error ? error.message : 'Normalization failed',
          timestamp: new Date(),
        })
      }
    }

    return { normalized, errors }
  }

  private async runDeduplication(jobs: JobListing[]): Promise<{
    unique: JobListing[]
    duplicates: JobListing[]
    errors: ProcessingError[]
  }> {
    try {
      const result = await this.deduplicationEngine.deduplicate(jobs)
      return {
        unique: result.unique,
        duplicates: result.duplicates,
        errors: [],
      }
    } catch (error) {
      this.logger.error('Deduplication failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      
      return {
        unique: jobs, // Return all jobs if deduplication fails
        duplicates: [],
        errors: [{
          jobId: 'batch',
          stage: 'deduplication',
          error: error instanceof Error ? error.message : 'Deduplication failed',
          timestamp: new Date(),
        }],
      }
    }
  }

  private async runEnrichment(jobs: JobListing[]): Promise<{
    enriched: JobListing[]
    errors: ProcessingError[]
  }> {
    const enriched: JobListing[] = []
    const errors: ProcessingError[] = []

    // Process in batches to avoid overwhelming external services
    const batches = this.chunkArray(jobs, this.config.batchSize)

    for (const batch of batches) {
      const batchPromises = batch.map(async (job) => {
        try {
          const enrichedJob = await this.enrichmentEngine.enrich(job)
          return enrichedJob
        } catch (error) {
          errors.push({
            jobId: job.id,
            stage: 'enrichment',
            error: error instanceof Error ? error.message : 'Enrichment failed',
            timestamp: new Date(),
          })
          return job // Return original job if enrichment fails
        }
      })

      const batchResults = await Promise.allSettled(batchPromises)
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          enriched.push(result.value)
        } else {
          enriched.push(batch[index]) // Use original job
          errors.push({
            jobId: batch[index].id,
            stage: 'enrichment',
            error: result.reason?.message || 'Enrichment failed',
            timestamp: new Date(),
          })
        }
      })

      // Rate limiting between batches
      if (batches.indexOf(batch) < batches.length - 1) {
        await this.sleep(1000) // 1 second delay between batches
      }
    }

    return { enriched, errors }
  }

  private filterByQuality(jobs: JobListing[]): {
    passed: JobListing[]
    failed: JobListing[]
  } {
    const passed: JobListing[] = []
    const failed: JobListing[] = []

    for (const job of jobs) {
      const qualityScore = job.quality_score || 0
      
      if (qualityScore >= this.config.qualityThreshold) {
        passed.push(job)
      } else {
        failed.push(job)
        this.logger.debug('Job failed quality threshold', {
          jobId: job.id,
          qualityScore,
          threshold: this.config.qualityThreshold,
        })
      }
    }

    return { passed, failed }
  }

  /**
   * Process a single job listing
   */
  async processJob(job: JobListing): Promise<JobListing | null> {
    const result = await this.processBatch([job])
    
    if (result.processed.length > 0) {
      return result.processed[0]
    }
    
    if (result.errors.length > 0) {
      this.logger.warn('Job processing failed', {
        jobId: job.id,
        errors: result.errors,
      })
    }
    
    return null
  }

  /**
   * Get processing statistics
   */
  getStats(): {
    config: ProcessingConfig
    engines: {
      deduplication: any
      normalization: any
      enrichment: any
      validation: any
    }
  } {
    return {
      config: this.config,
      engines: {
        deduplication: this.deduplicationEngine.getStats(),
        normalization: this.normalizationEngine.getStats(),
        enrichment: this.enrichmentEngine.getStats(),
        validation: this.validationEngine.getStats(),
      },
    }
  }

  /**
   * Update processing configuration
   */
  updateConfig(newConfig: Partial<ProcessingConfig>): void {
    this.config = { ...this.config, ...newConfig }
    this.logger.info('Processing configuration updated', { config: this.config })
  }

  // Utility methods
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}
