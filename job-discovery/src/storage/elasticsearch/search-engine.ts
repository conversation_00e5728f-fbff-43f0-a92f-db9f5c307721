/**
 * ElasticSearch Integration
 * Full-text and geo search with industry/skill facets and real-time indexing
 */

import { Client } from '@elastic/elasticsearch'
import { Logger } from 'winston'
import { JobListing } from '../../scrapers/framework/base-scraper'

export interface SearchConfig {
  node: string
  auth?: {
    username: string
    password: string
  }
  maxRetries: number
  requestTimeout: number
  sniffOnStart: boolean
  sniffInterval: number
}

export interface SearchQuery {
  keywords?: string
  location?: string
  geoDistance?: {
    lat: number
    lon: number
    distance: string
  }
  filters?: {
    source?: string[]
    employmentType?: string[]
    experienceLevel?: string[]
    remoteOption?: string[]
    industry?: string[]
    skills?: string[]
    salaryRange?: {
      min?: number
      max?: number
      currency?: string
    }
    postedSince?: Date
    qualityScore?: {
      min?: number
      max?: number
    }
  }
  sort?: {
    field: string
    order: 'asc' | 'desc'
  }[]
  facets?: string[]
  size?: number
  from?: number
}

export interface SearchResult {
  jobs: JobListing[]
  total: number
  facets: Record<string, { buckets: Array<{ key: string; doc_count: number }> }>
  aggregations?: Record<string, any>
  took: number
  maxScore: number
}

export interface IndexStats {
  totalDocs: number
  indexSize: string
  lastIndexed: Date
  mappingVersion: string
}

export class SearchEngine {
  private client: Client
  private logger: Logger
  private config: SearchConfig
  private readonly indexName = 'jobs'
  private readonly aliasName = 'jobs-current'

  constructor(logger: Logger, config: Partial<SearchConfig> = {}) {
    this.logger = logger
    this.config = {
      node: process.env.ELASTICSEARCH_URL || 'http://localhost:9200',
      maxRetries: 3,
      requestTimeout: 30000,
      sniffOnStart: true,
      sniffInterval: 300000, // 5 minutes
      ...config,
    }

    this.client = new Client({
      node: this.config.node,
      auth: this.config.auth,
      maxRetries: this.config.maxRetries,
      requestTimeout: this.config.requestTimeout,
      sniffOnStart: this.config.sniffOnStart,
      sniffInterval: this.config.sniffInterval,
    })

    this.initializeIndex()
  }

  private async initializeIndex(): Promise<void> {
    try {
      await this.createIndexTemplate()
      await this.createIndex()
      await this.setupAlias()
      
      this.logger.info('ElasticSearch index initialized successfully')
    } catch (error) {
      this.logger.error('Failed to initialize ElasticSearch index', {
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      throw error
    }
  }

  private async createIndexTemplate(): Promise<void> {
    const template = {
      index_patterns: [`${this.indexName}-*`],
      template: {
        settings: {
          number_of_shards: 3,
          number_of_replicas: 1,
          'index.max_result_window': 50000,
          analysis: {
            analyzer: {
              job_analyzer: {
                type: 'custom',
                tokenizer: 'standard',
                filter: [
                  'lowercase',
                  'stop',
                  'stemmer',
                  'synonym_filter',
                ],
              },
              location_analyzer: {
                type: 'custom',
                tokenizer: 'keyword',
                filter: ['lowercase'],
              },
              skill_analyzer: {
                type: 'custom',
                tokenizer: 'keyword',
                filter: ['lowercase'],
              },
            },
            filter: {
              synonym_filter: {
                type: 'synonym',
                synonyms: [
                  'js,javascript',
                  'ts,typescript',
                  'py,python',
                  'react,reactjs',
                  'vue,vuejs',
                  'angular,angularjs',
                  'aws,amazon web services',
                  'gcp,google cloud platform',
                  'k8s,kubernetes',
                ],
              },
            },
          },
        },
        mappings: {
          properties: {
            id: { type: 'keyword' },
            title: {
              type: 'text',
              analyzer: 'job_analyzer',
              fields: {
                keyword: { type: 'keyword' },
                suggest: { type: 'completion' },
              },
            },
            company: {
              type: 'text',
              analyzer: 'job_analyzer',
              fields: {
                keyword: { type: 'keyword' },
                suggest: { type: 'completion' },
              },
            },
            location: {
              type: 'text',
              analyzer: 'location_analyzer',
              fields: {
                keyword: { type: 'keyword' },
              },
            },
            description: {
              type: 'text',
              analyzer: 'job_analyzer',
            },
            requirements: {
              type: 'text',
              analyzer: 'job_analyzer',
            },
            benefits: {
              type: 'text',
              analyzer: 'job_analyzer',
            },
            salary: {
              properties: {
                min: { type: 'integer' },
                max: { type: 'integer' },
                currency: { type: 'keyword' },
                period: { type: 'keyword' },
              },
            },
            employment_type: { type: 'keyword' },
            experience_level: { type: 'keyword' },
            remote_option: { type: 'keyword' },
            posted_date: { type: 'date' },
            application_deadline: { type: 'date' },
            application_url: { type: 'keyword', index: false },
            source: { type: 'keyword' },
            source_id: { type: 'keyword' },
            skills: {
              type: 'text',
              analyzer: 'skill_analyzer',
              fields: {
                keyword: { type: 'keyword' },
              },
            },
            industry: { type: 'keyword' },
            company_size: { type: 'keyword' },
            company_logo: { type: 'keyword', index: false },
            scraped_at: { type: 'date' },
            updated_at: { type: 'date' },
            is_active: { type: 'boolean' },
            quality_score: { type: 'float' },
            duplicate_of: { type: 'keyword' },
            geo_location: {
              properties: {
                coordinates: { type: 'geo_point' },
                city: { type: 'keyword' },
                state: { type: 'keyword' },
                country: { type: 'keyword' },
                country_code: { type: 'keyword' },
              },
            },
            enrichment: {
              properties: {
                estimated_salary: {
                  properties: {
                    min: { type: 'integer' },
                    max: { type: 'integer' },
                    currency: { type: 'keyword' },
                    confidence: { type: 'float' },
                  },
                },
                skill_match_score: { type: 'float' },
                company_info: {
                  properties: {
                    website: { type: 'keyword', index: false },
                    industry: { type: 'keyword' },
                    size: { type: 'keyword' },
                    founded: { type: 'integer' },
                    headquarters: { type: 'keyword' },
                  },
                },
                similar_jobs: { type: 'keyword' },
              },
            },
            metadata: {
              properties: {
                processing_version: { type: 'keyword' },
                extraction_confidence: { type: 'float' },
                processing_time_ms: { type: 'integer' },
              },
            },
          },
        },
      },
    }

    await this.client.indices.putIndexTemplate({
      name: `${this.indexName}-template`,
      body: template,
    })
  }

  private async createIndex(): Promise<void> {
    const indexName = `${this.indexName}-${Date.now()}`
    
    const exists = await this.client.indices.exists({ index: indexName })
    if (!exists) {
      await this.client.indices.create({ index: indexName })
      this.logger.info('Created ElasticSearch index', { indexName })
    }
  }

  private async setupAlias(): Promise<void> {
    // Get current indices
    const indices = await this.client.cat.indices({
      index: `${this.indexName}-*`,
      format: 'json',
    })

    if (indices.length > 0) {
      // Point alias to the latest index
      const latestIndex = indices.sort((a: any, b: any) => 
        b.index.localeCompare(a.index)
      )[0].index

      await this.client.indices.putAlias({
        index: latestIndex,
        name: this.aliasName,
      })

      this.logger.info('Set up ElasticSearch alias', {
        alias: this.aliasName,
        index: latestIndex,
      })
    }
  }

  /**
   * Index a single job
   */
  async indexJob(job: JobListing): Promise<void> {
    try {
      const document = this.prepareJobDocument(job)
      
      await this.client.index({
        index: this.aliasName,
        id: job.id,
        body: document,
        refresh: 'wait_for',
      })

      this.logger.debug('Indexed job', { jobId: job.id })
    } catch (error) {
      this.logger.error('Failed to index job', {
        jobId: job.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      throw error
    }
  }

  /**
   * Bulk index multiple jobs
   */
  async bulkIndexJobs(jobs: JobListing[]): Promise<{
    indexed: number
    errors: Array<{ jobId: string; error: string }>
  }> {
    if (jobs.length === 0) {
      return { indexed: 0, errors: [] }
    }

    try {
      const body = jobs.flatMap(job => [
        { index: { _index: this.aliasName, _id: job.id } },
        this.prepareJobDocument(job),
      ])

      const response = await this.client.bulk({
        body,
        refresh: 'wait_for',
      })

      const errors: Array<{ jobId: string; error: string }> = []
      let indexed = 0

      if (response.errors) {
        response.items.forEach((item: any, index: number) => {
          if (item.index?.error) {
            errors.push({
              jobId: jobs[index].id,
              error: item.index.error.reason || 'Unknown error',
            })
          } else {
            indexed++
          }
        })
      } else {
        indexed = jobs.length
      }

      this.logger.info('Bulk indexed jobs', {
        total: jobs.length,
        indexed,
        errors: errors.length,
      })

      return { indexed, errors }
    } catch (error) {
      this.logger.error('Bulk indexing failed', {
        jobCount: jobs.length,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      throw error
    }
  }

  /**
   * Search jobs with advanced filtering and faceting
   */
  async searchJobs(query: SearchQuery): Promise<SearchResult> {
    try {
      const searchBody = this.buildSearchQuery(query)
      
      const response = await this.client.search({
        index: this.aliasName,
        body: searchBody,
        size: query.size || 20,
        from: query.from || 0,
      })

      const jobs = response.hits.hits.map((hit: any) => ({
        ...hit._source,
        _score: hit._score,
      }))

      const facets: Record<string, any> = {}
      if (response.aggregations) {
        Object.keys(response.aggregations).forEach(key => {
          facets[key] = response.aggregations![key]
        })
      }

      return {
        jobs,
        total: response.hits.total.value,
        facets,
        aggregations: response.aggregations,
        took: response.took,
        maxScore: response.hits.max_score || 0,
      }
    } catch (error) {
      this.logger.error('Search failed', {
        query,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      throw error
    }
  }

  private buildSearchQuery(query: SearchQuery): any {
    const searchBody: any = {
      query: {
        bool: {
          must: [],
          filter: [],
          should: [],
          must_not: [],
        },
      },
      aggs: {},
    }

    // Always filter for active jobs
    searchBody.query.bool.filter.push({ term: { is_active: true } })

    // Text search
    if (query.keywords) {
      searchBody.query.bool.must.push({
        multi_match: {
          query: query.keywords,
          fields: [
            'title^3',
            'description^2',
            'requirements',
            'skills^2',
            'company^1.5',
          ],
          type: 'best_fields',
          fuzziness: 'AUTO',
        },
      })
    }

    // Location search
    if (query.location) {
      searchBody.query.bool.should.push({
        multi_match: {
          query: query.location,
          fields: ['location', 'geo_location.city', 'geo_location.state', 'geo_location.country'],
          type: 'best_fields',
        },
      })
      searchBody.query.bool.minimum_should_match = 1
    }

    // Geo distance search
    if (query.geoDistance) {
      searchBody.query.bool.filter.push({
        geo_distance: {
          distance: query.geoDistance.distance,
          'geo_location.coordinates': {
            lat: query.geoDistance.lat,
            lon: query.geoDistance.lon,
          },
        },
      })
    }

    // Filters
    if (query.filters) {
      const filters = query.filters

      if (filters.source?.length) {
        searchBody.query.bool.filter.push({ terms: { source: filters.source } })
      }

      if (filters.employmentType?.length) {
        searchBody.query.bool.filter.push({ terms: { employment_type: filters.employmentType } })
      }

      if (filters.experienceLevel?.length) {
        searchBody.query.bool.filter.push({ terms: { experience_level: filters.experienceLevel } })
      }

      if (filters.remoteOption?.length) {
        searchBody.query.bool.filter.push({ terms: { remote_option: filters.remoteOption } })
      }

      if (filters.industry?.length) {
        searchBody.query.bool.filter.push({ terms: { industry: filters.industry } })
      }

      if (filters.skills?.length) {
        searchBody.query.bool.filter.push({ terms: { 'skills.keyword': filters.skills } })
      }

      if (filters.salaryRange) {
        const salaryFilter: any = { bool: { should: [] } }
        
        if (filters.salaryRange.min) {
          salaryFilter.bool.should.push({
            range: { 'salary.min': { gte: filters.salaryRange.min } },
          })
          salaryFilter.bool.should.push({
            range: { 'salary.max': { gte: filters.salaryRange.min } },
          })
        }

        if (filters.salaryRange.max) {
          salaryFilter.bool.should.push({
            range: { 'salary.max': { lte: filters.salaryRange.max } },
          })
        }

        if (filters.salaryRange.currency) {
          salaryFilter.bool.must = [{ term: { 'salary.currency': filters.salaryRange.currency } }]
        }

        searchBody.query.bool.filter.push(salaryFilter)
      }

      if (filters.postedSince) {
        searchBody.query.bool.filter.push({
          range: { posted_date: { gte: filters.postedSince } },
        })
      }

      if (filters.qualityScore) {
        const qualityRange: any = {}
        if (filters.qualityScore.min) qualityRange.gte = filters.qualityScore.min
        if (filters.qualityScore.max) qualityRange.lte = filters.qualityScore.max
        
        searchBody.query.bool.filter.push({
          range: { quality_score: qualityRange },
        })
      }
    }

    // Sorting
    if (query.sort?.length) {
      searchBody.sort = query.sort.map(s => ({ [s.field]: { order: s.order } }))
    } else {
      // Default sort by relevance and recency
      searchBody.sort = [
        '_score',
        { posted_date: { order: 'desc' } },
        { quality_score: { order: 'desc' } },
      ]
    }

    // Facets/Aggregations
    if (query.facets?.length) {
      query.facets.forEach(facet => {
        switch (facet) {
          case 'source':
            searchBody.aggs.sources = { terms: { field: 'source', size: 20 } }
            break
          case 'employment_type':
            searchBody.aggs.employment_types = { terms: { field: 'employment_type' } }
            break
          case 'experience_level':
            searchBody.aggs.experience_levels = { terms: { field: 'experience_level' } }
            break
          case 'remote_option':
            searchBody.aggs.remote_options = { terms: { field: 'remote_option' } }
            break
          case 'industry':
            searchBody.aggs.industries = { terms: { field: 'industry', size: 30 } }
            break
          case 'skills':
            searchBody.aggs.skills = { terms: { field: 'skills.keyword', size: 50 } }
            break
          case 'company_size':
            searchBody.aggs.company_sizes = { terms: { field: 'company_size' } }
            break
          case 'location':
            searchBody.aggs.locations = { terms: { field: 'geo_location.city', size: 30 } }
            break
        }
      })
    }

    // Salary statistics
    searchBody.aggs.salary_stats = {
      filter: { exists: { field: 'salary.min' } },
      aggs: {
        min_salary: { min: { field: 'salary.min' } },
        max_salary: { max: { field: 'salary.max' } },
        avg_salary: { avg: { field: 'salary.min' } },
      },
    }

    return searchBody
  }

  private prepareJobDocument(job: JobListing): any {
    const document: any = { ...job }

    // Prepare geo location for ElasticSearch
    if (job.geo_location?.latitude && job.geo_location?.longitude) {
      document.geo_location = {
        ...job.geo_location,
        coordinates: {
          lat: job.geo_location.latitude,
          lon: job.geo_location.longitude,
        },
      }
    }

    // Ensure arrays are properly formatted
    document.requirements = Array.isArray(job.requirements) ? job.requirements : []
    document.benefits = Array.isArray(job.benefits) ? job.benefits : []
    document.skills = Array.isArray(job.skills) ? job.skills : []

    return document
  }

  /**
   * Get job suggestions for autocomplete
   */
  async getSuggestions(prefix: string, type: 'title' | 'company' | 'location' = 'title'): Promise<string[]> {
    try {
      const field = type === 'title' ? 'title.suggest' : 
                   type === 'company' ? 'company.suggest' : 'location'

      const response = await this.client.search({
        index: this.aliasName,
        body: {
          suggest: {
            suggestions: {
              prefix,
              completion: {
                field,
                size: 10,
              },
            },
          },
        },
      })

      const suggestions = response.suggest?.suggestions?.[0]?.options || []
      return suggestions.map((option: any) => option.text)
    } catch (error) {
      this.logger.error('Failed to get suggestions', {
        prefix,
        type,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      return []
    }
  }

  /**
   * Delete job from index
   */
  async deleteJob(jobId: string): Promise<void> {
    try {
      await this.client.delete({
        index: this.aliasName,
        id: jobId,
        refresh: 'wait_for',
      })

      this.logger.debug('Deleted job from index', { jobId })
    } catch (error) {
      if (error.statusCode !== 404) {
        this.logger.error('Failed to delete job from index', {
          jobId,
          error: error instanceof Error ? error.message : 'Unknown error',
        })
        throw error
      }
    }
  }

  /**
   * Get index statistics
   */
  async getStats(): Promise<IndexStats> {
    try {
      const stats = await this.client.indices.stats({ index: this.aliasName })
      const indexName = Object.keys(stats.indices)[0]
      const indexStats = stats.indices[indexName]

      const mapping = await this.client.indices.getMapping({ index: this.aliasName })
      const mappingVersion = mapping[indexName]?.mappings?._meta?.version || '1.0.0'

      return {
        totalDocs: indexStats.total.docs.count,
        indexSize: `${Math.round(indexStats.total.store.size_in_bytes / 1024 / 1024)}MB`,
        lastIndexed: new Date(),
        mappingVersion,
      }
    } catch (error) {
      this.logger.error('Failed to get index stats', {
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      throw error
    }
  }

  /**
   * Reindex all data to new index
   */
  async reindex(): Promise<void> {
    try {
      const newIndexName = `${this.indexName}-${Date.now()}`
      
      // Create new index
      await this.client.indices.create({ index: newIndexName })
      
      // Reindex data
      await this.client.reindex({
        body: {
          source: { index: this.aliasName },
          dest: { index: newIndexName },
        },
        wait_for_completion: true,
      })

      // Update alias
      await this.client.indices.updateAliases({
        body: {
          actions: [
            { remove: { index: '*', alias: this.aliasName } },
            { add: { index: newIndexName, alias: this.aliasName } },
          ],
        },
      })

      this.logger.info('Reindexing completed', {
        newIndex: newIndexName,
        alias: this.aliasName,
      })
    } catch (error) {
      this.logger.error('Reindexing failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      throw error
    }
  }

  /**
   * Close ElasticSearch client
   */
  async close(): Promise<void> {
    await this.client.close()
    this.logger.info('ElasticSearch client closed')
  }
}
