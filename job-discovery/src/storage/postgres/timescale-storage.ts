/**
 * TimescaleDB Storage Layer
 * Incremental crawl storage with diff patching and time-series optimization
 */

import { Pool, PoolClient, QueryResult } from 'pg'
import { Logger } from 'winston'
import { JobListing } from '../../scrapers/framework/base-scraper'
import { createHash } from 'crypto'

export interface StorageConfig {
  connectionString: string
  maxConnections: number
  idleTimeoutMillis: number
  connectionTimeoutMillis: number
  enableCompression: boolean
  retentionDays: number
  partitionInterval: string
}

export interface JobDiff {
  job_id: string
  field_name: string
  old_value: any
  new_value: any
  change_type: 'insert' | 'update' | 'delete'
  timestamp: Date
  source: string
}

export interface CrawlSession {
  id: string
  source: string
  started_at: Date
  completed_at?: Date
  jobs_found: number
  jobs_processed: number
  jobs_new: number
  jobs_updated: number
  jobs_deleted: number
  status: 'running' | 'completed' | 'failed'
  error_message?: string
}

export class TimescaleStorage {
  private pool: Pool
  private logger: Logger
  private config: StorageConfig

  constructor(logger: Logger, config: Partial<StorageConfig> = {}) {
    this.logger = logger
    this.config = {
      connectionString: process.env.POSTGRES_URL || 'postgresql://localhost:5432/jobdb',
      maxConnections: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 10000,
      enableCompression: true,
      retentionDays: 90,
      partitionInterval: '1 day',
      ...config,
    }

    this.pool = new Pool({
      connectionString: this.config.connectionString,
      max: this.config.maxConnections,
      idleTimeoutMillis: this.config.idleTimeoutMillis,
      connectionTimeoutMillis: this.config.connectionTimeoutMillis,
    })

    this.initializeDatabase()
  }

  private async initializeDatabase(): Promise<void> {
    try {
      await this.createTables()
      await this.createHypertables()
      await this.createIndexes()
      await this.setupRetentionPolicies()
      await this.setupCompressionPolicies()
      
      this.logger.info('TimescaleDB initialized successfully')
    } catch (error) {
      this.logger.error('Failed to initialize TimescaleDB', {
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      throw error
    }
  }

  private async createTables(): Promise<void> {
    const client = await this.pool.connect()
    
    try {
      // Enable TimescaleDB extension
      await client.query('CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE')

      // Jobs table (main data)
      await client.query(`
        CREATE TABLE IF NOT EXISTS jobs (
          id VARCHAR(100) PRIMARY KEY,
          title VARCHAR(200) NOT NULL,
          company VARCHAR(100) NOT NULL,
          location VARCHAR(200) NOT NULL,
          description TEXT NOT NULL,
          requirements JSONB DEFAULT '[]',
          benefits JSONB DEFAULT '[]',
          salary JSONB,
          employment_type VARCHAR(20) NOT NULL,
          experience_level VARCHAR(20) NOT NULL,
          remote_option VARCHAR(20) NOT NULL,
          posted_date TIMESTAMPTZ NOT NULL,
          application_deadline TIMESTAMPTZ,
          application_url TEXT NOT NULL,
          source VARCHAR(50) NOT NULL,
          source_id VARCHAR(100) NOT NULL,
          skills JSONB DEFAULT '[]',
          industry VARCHAR(100),
          company_size VARCHAR(20),
          company_logo TEXT,
          raw_data JSONB DEFAULT '{}',
          scraped_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          is_active BOOLEAN DEFAULT true,
          quality_score DECIMAL(3,2),
          duplicate_of VARCHAR(100),
          geo_location JSONB,
          enrichment JSONB DEFAULT '{}',
          metadata JSONB DEFAULT '{}',
          content_hash VARCHAR(64) NOT NULL,
          UNIQUE(source, source_id)
        )
      `)

      // Job history table (time-series data)
      await client.query(`
        CREATE TABLE IF NOT EXISTS job_history (
          time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          job_id VARCHAR(100) NOT NULL,
          source VARCHAR(50) NOT NULL,
          event_type VARCHAR(20) NOT NULL, -- 'created', 'updated', 'deleted', 'reactivated'
          changes JSONB DEFAULT '{}',
          content_hash VARCHAR(64),
          quality_score DECIMAL(3,2),
          is_active BOOLEAN DEFAULT true
        )
      `)

      // Job diffs table (incremental changes)
      await client.query(`
        CREATE TABLE IF NOT EXISTS job_diffs (
          time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          job_id VARCHAR(100) NOT NULL,
          field_name VARCHAR(100) NOT NULL,
          old_value JSONB,
          new_value JSONB,
          change_type VARCHAR(10) NOT NULL,
          source VARCHAR(50) NOT NULL
        )
      `)

      // Crawl sessions table
      await client.query(`
        CREATE TABLE IF NOT EXISTS crawl_sessions (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          source VARCHAR(50) NOT NULL,
          started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          completed_at TIMESTAMPTZ,
          jobs_found INTEGER DEFAULT 0,
          jobs_processed INTEGER DEFAULT 0,
          jobs_new INTEGER DEFAULT 0,
          jobs_updated INTEGER DEFAULT 0,
          jobs_deleted INTEGER DEFAULT 0,
          status VARCHAR(20) DEFAULT 'running',
          error_message TEXT,
          metadata JSONB DEFAULT '{}'
        )
      `)

      // Source statistics table
      await client.query(`
        CREATE TABLE IF NOT EXISTS source_stats (
          time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          source VARCHAR(50) NOT NULL,
          total_jobs INTEGER NOT NULL,
          active_jobs INTEGER NOT NULL,
          new_jobs_24h INTEGER NOT NULL,
          updated_jobs_24h INTEGER NOT NULL,
          avg_quality_score DECIMAL(3,2),
          last_crawl_at TIMESTAMPTZ,
          crawl_frequency_minutes INTEGER,
          success_rate DECIMAL(3,2)
        )
      `)

    } finally {
      client.release()
    }
  }

  private async createHypertables(): Promise<void> {
    const client = await this.pool.connect()
    
    try {
      // Convert time-series tables to hypertables
      await client.query(`
        SELECT create_hypertable('job_history', 'time', 
          chunk_time_interval => INTERVAL '${this.config.partitionInterval}',
          if_not_exists => TRUE
        )
      `)

      await client.query(`
        SELECT create_hypertable('job_diffs', 'time',
          chunk_time_interval => INTERVAL '${this.config.partitionInterval}',
          if_not_exists => TRUE
        )
      `)

      await client.query(`
        SELECT create_hypertable('source_stats', 'time',
          chunk_time_interval => INTERVAL '1 hour',
          if_not_exists => TRUE
        )
      `)

    } finally {
      client.release()
    }
  }

  private async createIndexes(): Promise<void> {
    const client = await this.pool.connect()
    
    try {
      // Jobs table indexes
      await client.query('CREATE INDEX IF NOT EXISTS idx_jobs_source ON jobs(source)')
      await client.query('CREATE INDEX IF NOT EXISTS idx_jobs_posted_date ON jobs(posted_date)')
      await client.query('CREATE INDEX IF NOT EXISTS idx_jobs_location ON jobs USING gin(to_tsvector(\'english\', location))')
      await client.query('CREATE INDEX IF NOT EXISTS idx_jobs_title ON jobs USING gin(to_tsvector(\'english\', title))')
      await client.query('CREATE INDEX IF NOT EXISTS idx_jobs_company ON jobs USING gin(to_tsvector(\'english\', company))')
      await client.query('CREATE INDEX IF NOT EXISTS idx_jobs_skills ON jobs USING gin(skills)')
      await client.query('CREATE INDEX IF NOT EXISTS idx_jobs_active ON jobs(is_active) WHERE is_active = true')
      await client.query('CREATE INDEX IF NOT EXISTS idx_jobs_quality ON jobs(quality_score) WHERE quality_score IS NOT NULL')
      await client.query('CREATE INDEX IF NOT EXISTS idx_jobs_geo ON jobs USING gin(geo_location)')

      // Job history indexes
      await client.query('CREATE INDEX IF NOT EXISTS idx_job_history_job_id ON job_history(job_id, time DESC)')
      await client.query('CREATE INDEX IF NOT EXISTS idx_job_history_source ON job_history(source, time DESC)')
      await client.query('CREATE INDEX IF NOT EXISTS idx_job_history_event ON job_history(event_type, time DESC)')

      // Job diffs indexes
      await client.query('CREATE INDEX IF NOT EXISTS idx_job_diffs_job_id ON job_diffs(job_id, time DESC)')
      await client.query('CREATE INDEX IF NOT EXISTS idx_job_diffs_field ON job_diffs(field_name, time DESC)')

      // Crawl sessions indexes
      await client.query('CREATE INDEX IF NOT EXISTS idx_crawl_sessions_source ON crawl_sessions(source, started_at DESC)')
      await client.query('CREATE INDEX IF NOT EXISTS idx_crawl_sessions_status ON crawl_sessions(status)')

    } finally {
      client.release()
    }
  }

  private async setupRetentionPolicies(): Promise<void> {
    const client = await this.pool.connect()
    
    try {
      // Retention policy for job history
      await client.query(`
        SELECT add_retention_policy('job_history', INTERVAL '${this.config.retentionDays} days', if_not_exists => TRUE)
      `)

      // Retention policy for job diffs
      await client.query(`
        SELECT add_retention_policy('job_diffs', INTERVAL '${this.config.retentionDays} days', if_not_exists => TRUE)
      `)

      // Retention policy for source stats
      await client.query(`
        SELECT add_retention_policy('source_stats', INTERVAL '1 year', if_not_exists => TRUE)
      `)

    } finally {
      client.release()
    }
  }

  private async setupCompressionPolicies(): Promise<void> {
    if (!this.config.enableCompression) return

    const client = await this.pool.connect()
    
    try {
      // Compression policy for job history (compress data older than 7 days)
      await client.query(`
        ALTER TABLE job_history SET (
          timescaledb.compress,
          timescaledb.compress_segmentby = 'source',
          timescaledb.compress_orderby = 'time DESC'
        )
      `)

      await client.query(`
        SELECT add_compression_policy('job_history', INTERVAL '7 days', if_not_exists => TRUE)
      `)

      // Compression policy for job diffs
      await client.query(`
        ALTER TABLE job_diffs SET (
          timescaledb.compress,
          timescaledb.compress_segmentby = 'source',
          timescaledb.compress_orderby = 'time DESC'
        )
      `)

      await client.query(`
        SELECT add_compression_policy('job_diffs', INTERVAL '7 days', if_not_exists => TRUE)
      `)

    } finally {
      client.release()
    }
  }

  /**
   * Start a new crawl session
   */
  async startCrawlSession(source: string, metadata: any = {}): Promise<string> {
    const client = await this.pool.connect()
    
    try {
      const result = await client.query(`
        INSERT INTO crawl_sessions (source, metadata)
        VALUES ($1, $2)
        RETURNING id
      `, [source, JSON.stringify(metadata)])

      const sessionId = result.rows[0].id
      
      this.logger.info('Started crawl session', {
        sessionId,
        source,
        metadata,
      })

      return sessionId
    } finally {
      client.release()
    }
  }

  /**
   * Complete a crawl session
   */
  async completeCrawlSession(
    sessionId: string,
    stats: {
      jobsFound: number
      jobsProcessed: number
      jobsNew: number
      jobsUpdated: number
      jobsDeleted: number
    },
    error?: string
  ): Promise<void> {
    const client = await this.pool.connect()
    
    try {
      await client.query(`
        UPDATE crawl_sessions 
        SET completed_at = NOW(),
            jobs_found = $2,
            jobs_processed = $3,
            jobs_new = $4,
            jobs_updated = $5,
            jobs_deleted = $6,
            status = $7,
            error_message = $8
        WHERE id = $1
      `, [
        sessionId,
        stats.jobsFound,
        stats.jobsProcessed,
        stats.jobsNew,
        stats.jobsUpdated,
        stats.jobsDeleted,
        error ? 'failed' : 'completed',
        error || null,
      ])

      this.logger.info('Completed crawl session', {
        sessionId,
        stats,
        status: error ? 'failed' : 'completed',
      })
    } finally {
      client.release()
    }
  }

  /**
   * Store or update job with diff tracking
   */
  async upsertJob(job: JobListing, sessionId: string): Promise<{
    isNew: boolean
    hasChanges: boolean
    diffs: JobDiff[]
  }> {
    const client = await this.pool.connect()
    
    try {
      await client.query('BEGIN')

      const contentHash = this.calculateContentHash(job)
      
      // Check if job exists
      const existingResult = await client.query(`
        SELECT id, content_hash, is_active, 
               title, company, location, description, salary, 
               employment_type, experience_level, remote_option,
               skills, industry, quality_score
        FROM jobs 
        WHERE source = $1 AND source_id = $2
      `, [job.source, job.source_id])

      const isNew = existingResult.rows.length === 0
      let hasChanges = false
      let diffs: JobDiff[] = []

      if (isNew) {
        // Insert new job
        await this.insertJob(client, job, contentHash)
        
        // Record creation event
        await this.recordJobEvent(client, job.id, job.source, 'created', {}, contentHash, job.quality_score)
        
        hasChanges = true
      } else {
        const existing = existingResult.rows[0]
        
        // Check if content has changed
        if (existing.content_hash !== contentHash) {
          // Calculate diffs
          diffs = this.calculateDiffs(existing, job)
          
          // Update job
          await this.updateJob(client, job, contentHash)
          
          // Record update event
          await this.recordJobEvent(client, job.id, job.source, 'updated', { diffs }, contentHash, job.quality_score)
          
          // Store individual diffs
          for (const diff of diffs) {
            await this.storeDiff(client, diff)
          }
          
          hasChanges = true
        } else if (!existing.is_active) {
          // Reactivate job
          await client.query(`
            UPDATE jobs 
            SET is_active = true, updated_at = NOW()
            WHERE id = $1
          `, [job.id])
          
          await this.recordJobEvent(client, job.id, job.source, 'reactivated', {}, contentHash, job.quality_score)
          hasChanges = true
        }
      }

      await client.query('COMMIT')
      
      return { isNew, hasChanges, diffs }
      
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  private async insertJob(client: PoolClient, job: JobListing, contentHash: string): Promise<void> {
    await client.query(`
      INSERT INTO jobs (
        id, title, company, location, description, requirements, benefits,
        salary, employment_type, experience_level, remote_option, posted_date,
        application_deadline, application_url, source, source_id, skills,
        industry, company_size, company_logo, raw_data, scraped_at,
        quality_score, duplicate_of, geo_location, enrichment, metadata,
        content_hash
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16,
        $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28
      )
    `, [
      job.id, job.title, job.company, job.location, job.description,
      JSON.stringify(job.requirements), JSON.stringify(job.benefits),
      job.salary ? JSON.stringify(job.salary) : null,
      job.employment_type, job.experience_level, job.remote_option,
      job.posted_date, job.application_deadline, job.application_url,
      job.source, job.source_id, JSON.stringify(job.skills),
      job.industry, job.company_size, job.company_logo,
      JSON.stringify(job.raw_data), job.scraped_at, job.quality_score,
      job.duplicate_of, job.geo_location ? JSON.stringify(job.geo_location) : null,
      JSON.stringify(job.enrichment || {}), JSON.stringify(job.metadata || {}),
      contentHash,
    ])
  }

  private async updateJob(client: PoolClient, job: JobListing, contentHash: string): Promise<void> {
    await client.query(`
      UPDATE jobs SET
        title = $2, company = $3, location = $4, description = $5,
        requirements = $6, benefits = $7, salary = $8, employment_type = $9,
        experience_level = $10, remote_option = $11, posted_date = $12,
        application_deadline = $13, application_url = $14, skills = $15,
        industry = $16, company_size = $17, company_logo = $18,
        raw_data = $19, updated_at = NOW(), quality_score = $20,
        duplicate_of = $21, geo_location = $22, enrichment = $23,
        metadata = $24, content_hash = $25, is_active = true
      WHERE id = $1
    `, [
      job.id, job.title, job.company, job.location, job.description,
      JSON.stringify(job.requirements), JSON.stringify(job.benefits),
      job.salary ? JSON.stringify(job.salary) : null,
      job.employment_type, job.experience_level, job.remote_option,
      job.posted_date, job.application_deadline, job.application_url,
      JSON.stringify(job.skills), job.industry, job.company_size,
      job.company_logo, JSON.stringify(job.raw_data), job.quality_score,
      job.duplicate_of, job.geo_location ? JSON.stringify(job.geo_location) : null,
      JSON.stringify(job.enrichment || {}), JSON.stringify(job.metadata || {}),
      contentHash,
    ])
  }

  private async recordJobEvent(
    client: PoolClient,
    jobId: string,
    source: string,
    eventType: string,
    changes: any,
    contentHash: string,
    qualityScore?: number
  ): Promise<void> {
    await client.query(`
      INSERT INTO job_history (job_id, source, event_type, changes, content_hash, quality_score)
      VALUES ($1, $2, $3, $4, $5, $6)
    `, [jobId, source, eventType, JSON.stringify(changes), contentHash, qualityScore])
  }

  private async storeDiff(client: PoolClient, diff: JobDiff): Promise<void> {
    await client.query(`
      INSERT INTO job_diffs (job_id, field_name, old_value, new_value, change_type, source)
      VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      diff.job_id,
      diff.field_name,
      JSON.stringify(diff.old_value),
      JSON.stringify(diff.new_value),
      diff.change_type,
      diff.source,
    ])
  }

  private calculateContentHash(job: JobListing): string {
    // Create hash of key content fields
    const content = {
      title: job.title,
      company: job.company,
      location: job.location,
      description: job.description,
      requirements: job.requirements,
      benefits: job.benefits,
      salary: job.salary,
      employment_type: job.employment_type,
      experience_level: job.experience_level,
      remote_option: job.remote_option,
      skills: job.skills,
      industry: job.industry,
    }

    return createHash('sha256')
      .update(JSON.stringify(content))
      .digest('hex')
  }

  private calculateDiffs(existing: any, updated: JobListing): JobDiff[] {
    const diffs: JobDiff[] = []
    const fields = [
      'title', 'company', 'location', 'description', 'salary',
      'employment_type', 'experience_level', 'remote_option',
      'skills', 'industry', 'quality_score',
    ]

    for (const field of fields) {
      const oldValue = existing[field]
      const newValue = (updated as any)[field]

      if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
        diffs.push({
          job_id: updated.id,
          field_name: field,
          old_value: oldValue,
          new_value: newValue,
          change_type: 'update',
          timestamp: new Date(),
          source: updated.source,
        })
      }
    }

    return diffs
  }

  /**
   * Mark jobs as inactive if not seen in recent crawl
   */
  async markStaleJobs(source: string, activeCrawlTime: Date): Promise<number> {
    const client = await this.pool.connect()
    
    try {
      const result = await client.query(`
        UPDATE jobs 
        SET is_active = false, updated_at = NOW()
        WHERE source = $1 
          AND is_active = true 
          AND updated_at < $2
        RETURNING id
      `, [source, activeCrawlTime])

      const staleJobIds = result.rows.map(row => row.id)
      
      // Record deletion events
      for (const jobId of staleJobIds) {
        await this.recordJobEvent(client, jobId, source, 'deleted', {}, '', null)
      }

      this.logger.info('Marked stale jobs as inactive', {
        source,
        count: staleJobIds.length,
        cutoffTime: activeCrawlTime,
      })

      return staleJobIds.length
    } finally {
      client.release()
    }
  }

  /**
   * Get job by ID
   */
  async getJob(id: string): Promise<JobListing | null> {
    const client = await this.pool.connect()
    
    try {
      const result = await client.query('SELECT * FROM jobs WHERE id = $1', [id])
      
      if (result.rows.length === 0) {
        return null
      }

      return this.mapRowToJob(result.rows[0])
    } finally {
      client.release()
    }
  }

  /**
   * Search jobs with filters
   */
  async searchJobs(filters: {
    keywords?: string
    location?: string
    source?: string
    employmentType?: string
    experienceLevel?: string
    remoteOption?: string
    minQualityScore?: number
    limit?: number
    offset?: number
  }): Promise<{ jobs: JobListing[]; total: number }> {
    const client = await this.pool.connect()
    
    try {
      let whereClause = 'WHERE is_active = true'
      const params: any[] = []
      let paramIndex = 1

      if (filters.keywords) {
        whereClause += ` AND (
          to_tsvector('english', title) @@ plainto_tsquery('english', $${paramIndex}) OR
          to_tsvector('english', description) @@ plainto_tsquery('english', $${paramIndex})
        )`
        params.push(filters.keywords)
        paramIndex++
      }

      if (filters.location) {
        whereClause += ` AND to_tsvector('english', location) @@ plainto_tsquery('english', $${paramIndex})`
        params.push(filters.location)
        paramIndex++
      }

      if (filters.source) {
        whereClause += ` AND source = $${paramIndex}`
        params.push(filters.source)
        paramIndex++
      }

      if (filters.employmentType) {
        whereClause += ` AND employment_type = $${paramIndex}`
        params.push(filters.employmentType)
        paramIndex++
      }

      if (filters.experienceLevel) {
        whereClause += ` AND experience_level = $${paramIndex}`
        params.push(filters.experienceLevel)
        paramIndex++
      }

      if (filters.remoteOption) {
        whereClause += ` AND remote_option = $${paramIndex}`
        params.push(filters.remoteOption)
        paramIndex++
      }

      if (filters.minQualityScore) {
        whereClause += ` AND quality_score >= $${paramIndex}`
        params.push(filters.minQualityScore)
        paramIndex++
      }

      // Get total count
      const countResult = await client.query(`
        SELECT COUNT(*) as total FROM jobs ${whereClause}
      `, params)

      const total = parseInt(countResult.rows[0].total)

      // Get jobs with pagination
      const limit = filters.limit || 50
      const offset = filters.offset || 0

      const jobsResult = await client.query(`
        SELECT * FROM jobs ${whereClause}
        ORDER BY posted_date DESC, quality_score DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `, [...params, limit, offset])

      const jobs = jobsResult.rows.map(row => this.mapRowToJob(row))

      return { jobs, total }
    } finally {
      client.release()
    }
  }

  private mapRowToJob(row: any): JobListing {
    return {
      id: row.id,
      title: row.title,
      company: row.company,
      location: row.location,
      description: row.description,
      requirements: row.requirements || [],
      benefits: row.benefits || [],
      salary: row.salary,
      employment_type: row.employment_type,
      experience_level: row.experience_level,
      remote_option: row.remote_option,
      posted_date: row.posted_date,
      application_deadline: row.application_deadline,
      application_url: row.application_url,
      source: row.source,
      source_id: row.source_id,
      skills: row.skills || [],
      industry: row.industry,
      company_size: row.company_size,
      company_logo: row.company_logo,
      raw_data: row.raw_data || {},
      scraped_at: row.scraped_at,
      updated_at: row.updated_at,
      is_active: row.is_active,
      quality_score: row.quality_score,
      duplicate_of: row.duplicate_of,
      geo_location: row.geo_location,
      enrichment: row.enrichment || {},
      metadata: row.metadata || {},
    }
  }

  /**
   * Get storage statistics
   */
  async getStats(): Promise<{
    totalJobs: number
    activeJobs: number
    sources: { source: string; count: number }[]
    recentActivity: { date: string; newJobs: number; updatedJobs: number }[]
  }> {
    const client = await this.pool.connect()
    
    try {
      // Total and active jobs
      const totalsResult = await client.query(`
        SELECT 
          COUNT(*) as total_jobs,
          COUNT(*) FILTER (WHERE is_active = true) as active_jobs
        FROM jobs
      `)

      // Jobs by source
      const sourcesResult = await client.query(`
        SELECT source, COUNT(*) as count
        FROM jobs
        WHERE is_active = true
        GROUP BY source
        ORDER BY count DESC
      `)

      // Recent activity (last 7 days)
      const activityResult = await client.query(`
        SELECT 
          DATE(time) as date,
          COUNT(*) FILTER (WHERE event_type = 'created') as new_jobs,
          COUNT(*) FILTER (WHERE event_type = 'updated') as updated_jobs
        FROM job_history
        WHERE time >= NOW() - INTERVAL '7 days'
        GROUP BY DATE(time)
        ORDER BY date DESC
      `)

      return {
        totalJobs: parseInt(totalsResult.rows[0].total_jobs),
        activeJobs: parseInt(totalsResult.rows[0].active_jobs),
        sources: sourcesResult.rows.map(row => ({
          source: row.source,
          count: parseInt(row.count),
        })),
        recentActivity: activityResult.rows.map(row => ({
          date: row.date,
          newJobs: parseInt(row.new_jobs || 0),
          updatedJobs: parseInt(row.updated_jobs || 0),
        })),
      }
    } finally {
      client.release()
    }
  }

  /**
   * Close database connections
   */
  async close(): Promise<void> {
    await this.pool.end()
    this.logger.info('TimescaleDB connections closed')
  }
}
