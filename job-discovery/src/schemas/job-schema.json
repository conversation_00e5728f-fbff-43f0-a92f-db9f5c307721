{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://api.jobdiscovery.com/schemas/job-listing.json", "title": "Job Listing", "description": "Normalized job listing schema for aggregated job data", "type": "object", "required": ["id", "title", "company", "location", "description", "employment_type", "experience_level", "remote_option", "posted_date", "application_url", "source", "source_id", "scraped_at"], "properties": {"id": {"type": "string", "description": "Unique identifier for the job listing", "pattern": "^[a-zA-Z0-9_-]+$", "minLength": 1, "maxLength": 100}, "title": {"type": "string", "description": "Job title", "minLength": 1, "maxLength": 200}, "company": {"type": "string", "description": "Company name", "minLength": 1, "maxLength": 100}, "location": {"type": "string", "description": "Job location (city, state, country)", "minLength": 1, "maxLength": 200}, "description": {"type": "string", "description": "Full job description", "minLength": 10, "maxLength": 50000}, "requirements": {"type": "array", "description": "List of job requirements", "items": {"type": "string", "minLength": 5, "maxLength": 500}, "maxItems": 20}, "benefits": {"type": "array", "description": "List of job benefits", "items": {"type": "string", "minLength": 3, "maxLength": 300}, "maxItems": 15}, "salary": {"type": "object", "description": "Salary information", "properties": {"min": {"type": "number", "description": "Minimum salary", "minimum": 0, "maximum": 10000000}, "max": {"type": "number", "description": "Maximum salary", "minimum": 0, "maximum": 10000000}, "currency": {"type": "string", "description": "Currency code (ISO 4217)", "pattern": "^[A-Z]{3}$", "default": "USD"}, "period": {"type": "string", "description": "Salary period", "enum": ["hourly", "monthly", "yearly"], "default": "yearly"}}, "additionalProperties": false}, "employment_type": {"type": "string", "description": "Type of employment", "enum": ["full-time", "part-time", "contract", "internship", "temporary"]}, "experience_level": {"type": "string", "description": "Required experience level", "enum": ["entry", "mid", "senior", "executive"]}, "remote_option": {"type": "string", "description": "Remote work option", "enum": ["remote", "hybrid", "onsite"]}, "posted_date": {"type": "string", "description": "Date when job was posted", "format": "date-time"}, "application_deadline": {"type": "string", "description": "Application deadline", "format": "date-time"}, "application_url": {"type": "string", "description": "URL to apply for the job", "format": "uri", "maxLength": 2000}, "source": {"type": "string", "description": "Source platform where job was found", "minLength": 1, "maxLength": 50}, "source_id": {"type": "string", "description": "Original job ID from source platform", "minLength": 1, "maxLength": 100}, "skills": {"type": "array", "description": "Required or mentioned skills", "items": {"type": "string", "minLength": 1, "maxLength": 50}, "maxItems": 30, "uniqueItems": true}, "industry": {"type": "string", "description": "Industry category", "maxLength": 100}, "company_size": {"type": "string", "description": "Company size category", "enum": ["startup", "small", "medium", "large", "enterprise"]}, "company_logo": {"type": "string", "description": "URL to company logo", "format": "uri", "maxLength": 500}, "raw_data": {"type": "object", "description": "Original raw data from source", "additionalProperties": true}, "scraped_at": {"type": "string", "description": "Timestamp when job was scraped", "format": "date-time"}, "updated_at": {"type": "string", "description": "Timestamp when job was last updated", "format": "date-time"}, "is_active": {"type": "boolean", "description": "Whether the job is still active", "default": true}, "quality_score": {"type": "number", "description": "Data quality score (0-1)", "minimum": 0, "maximum": 1}, "duplicate_of": {"type": "string", "description": "ID of the canonical job if this is a duplicate", "pattern": "^[a-zA-Z0-9_-]+$"}, "geo_location": {"type": "object", "description": "Geocoded location information", "properties": {"latitude": {"type": "number", "minimum": -90, "maximum": 90}, "longitude": {"type": "number", "minimum": -180, "maximum": 180}, "city": {"type": "string", "maxLength": 100}, "state": {"type": "string", "maxLength": 100}, "country": {"type": "string", "maxLength": 100}, "country_code": {"type": "string", "pattern": "^[A-Z]{2}$"}, "timezone": {"type": "string", "maxLength": 50}}, "additionalProperties": false}, "enrichment": {"type": "object", "description": "Additional enriched data", "properties": {"estimated_salary": {"type": "object", "properties": {"min": {"type": "number"}, "max": {"type": "number"}, "currency": {"type": "string"}, "confidence": {"type": "number", "minimum": 0, "maximum": 1}}}, "skill_match_score": {"type": "number", "description": "Skill relevance score", "minimum": 0, "maximum": 1}, "company_info": {"type": "object", "properties": {"website": {"type": "string", "format": "uri"}, "industry": {"type": "string"}, "size": {"type": "string"}, "founded": {"type": "integer"}, "headquarters": {"type": "string"}, "description": {"type": "string"}}}, "similar_jobs": {"type": "array", "description": "IDs of similar job listings", "items": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$"}, "maxItems": 10}}, "additionalProperties": false}, "metadata": {"type": "object", "description": "Processing metadata", "properties": {"processing_version": {"type": "string", "description": "Version of processing pipeline"}, "extraction_confidence": {"type": "number", "description": "Confidence in data extraction", "minimum": 0, "maximum": 1}, "validation_errors": {"type": "array", "description": "List of validation errors", "items": {"type": "string"}}, "processing_time_ms": {"type": "number", "description": "Time taken to process this job", "minimum": 0}, "source_page_url": {"type": "string", "description": "URL of the page where job was found", "format": "uri"}}, "additionalProperties": false}}, "additionalProperties": false, "examples": [{"id": "linkedin_*********", "title": "Senior Software Engineer", "company": "TechCorp Inc.", "location": "San Francisco, CA, USA", "description": "We are looking for a Senior Software Engineer to join our team...", "requirements": ["5+ years of software development experience", "Proficiency in JavaScript and Python", "Experience with cloud platforms (AWS, Azure, GCP)"], "benefits": ["Competitive salary and equity", "Health, dental, and vision insurance", "Flexible work arrangements"], "salary": {"min": 120000, "max": 180000, "currency": "USD", "period": "yearly"}, "employment_type": "full-time", "experience_level": "senior", "remote_option": "hybrid", "posted_date": "2024-01-15T10:30:00Z", "application_url": "https://linkedin.com/jobs/view/*********", "source": "linkedin", "source_id": "*********", "skills": ["JavaScript", "Python", "AWS", "React", "Node.js"], "industry": "Technology", "company_size": "medium", "scraped_at": "2024-01-15T11:00:00Z", "is_active": true, "quality_score": 0.95, "geo_location": {"latitude": 37.7749, "longitude": -122.4194, "city": "San Francisco", "state": "California", "country": "United States", "country_code": "US", "timezone": "America/Los_Angeles"}, "enrichment": {"estimated_salary": {"min": 125000, "max": 175000, "currency": "USD", "confidence": 0.8}, "skill_match_score": 0.87, "company_info": {"website": "https://techcorp.com", "industry": "Software Development", "size": "201-500 employees", "founded": 2010, "headquarters": "San Francisco, CA"}}, "metadata": {"processing_version": "2.1.0", "extraction_confidence": 0.92, "processing_time_ms": 1250, "source_page_url": "https://linkedin.com/jobs/search?keywords=software+engineer"}}]}