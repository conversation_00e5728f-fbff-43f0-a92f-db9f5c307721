apiVersion: v1
kind: ConfigMap
metadata:
  name: cvleap-network-policy-config
  namespace: cvleap-system
data:
  allowed-domains.yaml: |
    # Whitelisted job site domains for egress traffic
    jobSites:
      tier1: # High-volume, trusted sites
        - workday.com
        - lever.co
        - greenhouse.io
        - bamboohr.com
        - successfactors.com
        - taleo.net
      tier2: # Medium-volume sites
        - linkedin.com
        - indeed.com
        - glassdoor.com
        - monster.com
        - ziprecruiter.com
        - careerbuilder.com
      tier3: # Low-volume, premium sites
        - angellist.com
        - stackoverflow.com/jobs
        - dice.com
        - flexjobs.com
    
    # Additional services required for job applications
    services:
      captcha:
        - recaptcha.net
        - hcaptcha.com
        - funcaptcha.com
      cdn:
        - cloudflare.com
        - amazonaws.com
        - googleusercontent.com
      analytics:
        - google-analytics.com
        - googletagmanager.com
    
    # DNS and infrastructure
    infrastructure:
      dns:
        - *******
        - *******
        - ***************  # AWS metadata
      ntp:
        - pool.ntp.org
        - time.google.com

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cvleap-sandbox-zero-trust
  namespace: cvleap
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/component: sandbox
  policyTypes:
  - Ingress
  - Egress
  
  # Ingress: Only allow traffic from CVLeap API and monitoring
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: cvleap-api
    - namespaceSelector:
        matchLabels:
          name: cvleap-monitoring
    ports:
    - protocol: TCP
      port: 8000  # Main application port
    - protocol: TCP
      port: 7000  # Terminal port
  
  # Egress: Strictly controlled outbound traffic
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow NTP for time synchronization
  - to: []
    ports:
    - protocol: UDP
      port: 123
  
  # Allow HTTPS to whitelisted job sites
  - to:
    - namespaceSelector:
        matchLabels:
          name: internet-gateway
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cvleap-deny-all-default
  namespace: cvleap
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  # Empty ingress/egress rules = deny all by default

---
# Calico GlobalNetworkPolicy for advanced egress filtering
apiVersion: projectcalico.org/v3
kind: GlobalNetworkPolicy
metadata:
  name: cvleap-egress-domain-filter
spec:
  selector: app.kubernetes.io/component == "sandbox"
  types:
  - Egress
  egress:
  # Allow DNS
  - action: Allow
    protocol: UDP
    destination:
      ports: [53]
  - action: Allow
    protocol: TCP
    destination:
      ports: [53]
  
  # Allow NTP
  - action: Allow
    protocol: UDP
    destination:
      ports: [123]
  
  # Allow HTTPS to specific domains (requires Calico Enterprise)
  - action: Allow
    protocol: TCP
    destination:
      ports: [443, 80]
      domains:
      - "workday.com"
      - "*.workday.com"
      - "lever.co"
      - "*.lever.co"
      - "greenhouse.io"
      - "*.greenhouse.io"
      - "bamboohr.com"
      - "*.bamboohr.com"
      - "linkedin.com"
      - "*.linkedin.com"
      - "indeed.com"
      - "*.indeed.com"
      - "recaptcha.net"
      - "*.recaptcha.net"
      - "hcaptcha.com"
      - "*.hcaptcha.com"
  
  # Deny all other egress
  - action: Deny

---
# Istio ServiceEntry for external services
apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: cvleap-job-sites
  namespace: cvleap
spec:
  hosts:
  - workday.com
  - lever.co
  - greenhouse.io
  - bamboohr.com
  - linkedin.com
  - indeed.com
  ports:
  - number: 443
    name: https
    protocol: HTTPS
  - number: 80
    name: http
    protocol: HTTP
  location: MESH_EXTERNAL
  resolution: DNS

---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: cvleap-egress-control
  namespace: cvleap
spec:
  hosts:
  - workday.com
  - lever.co
  - greenhouse.io
  gateways:
  - mesh
  http:
  - match:
    - headers:
        x-cvleap-sandbox:
          regex: "sandbox-.*"
    route:
    - destination:
        host: workday.com
    timeout: 30s
    retries:
      attempts: 3
      perTryTimeout: 10s

---
# Falco rules for network security monitoring
apiVersion: v1
kind: ConfigMap
metadata:
  name: cvleap-falco-network-rules
  namespace: cvleap-monitoring
data:
  network_rules.yaml: |
    - rule: Unauthorized Egress Connection
      desc: Detect connections to non-whitelisted domains
      condition: >
        outbound and
        container.image.repository contains "cvleap/sandbox" and
        not fd.sip in (allowed_ips) and
        not fd.sip.name in (allowed_domains)
      output: >
        Unauthorized egress connection
        (user=%user.name command=%proc.cmdline connection=%fd.name)
      priority: WARNING
      tags: [network, security, cvleap]
    
    - list: allowed_domains
      items: [
        "workday.com", "lever.co", "greenhouse.io", "bamboohr.com",
        "linkedin.com", "indeed.com", "recaptcha.net", "hcaptcha.com"
      ]
    
    - list: allowed_ips
      items: [
        "*******", "*******", "***************"
      ]

---
# OPA Gatekeeper constraint for network policies
apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: cvleapnetworkpolicy
spec:
  crd:
    spec:
      names:
        kind: CVLeapNetworkPolicy
      validation:
        openAPIV3Schema:
          type: object
          properties:
            allowedDomains:
              type: array
              items:
                type: string
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package cvleapnetworkpolicy
        
        violation[{"msg": msg}] {
          input.review.kind.kind == "Pod"
          input.review.object.metadata.labels["app.kubernetes.io/component"] == "sandbox"
          not has_network_policy
          msg := "Sandbox pods must have associated NetworkPolicy"
        }
        
        has_network_policy {
          # Check if NetworkPolicy exists for this pod
          # This would need to be implemented with external data
          true
        }

---
apiVersion: config.gatekeeper.sh/v1alpha1
kind: CVLeapNetworkPolicy
metadata:
  name: enforce-sandbox-network-policy
spec:
  match:
    - apiGroups: [""]
      kinds: ["Pod"]
      namespaces: ["cvleap"]
  parameters:
    allowedDomains:
    - "workday.com"
    - "lever.co"
    - "greenhouse.io"
    - "bamboohr.com"
