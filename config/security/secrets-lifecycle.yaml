apiVersion: v1
kind: ConfigMap
metadata:
  name: cvleap-secrets-config
  namespace: cvleap-system
data:
  secrets-policy.yaml: |
    # Secrets lifecycle configuration
    lifecycle:
      defaultTTL: "1h"
      maxTTL: "24h"
      rotationInterval: "6h"
      
    # Encryption settings
    encryption:
      algorithm: "AES-256-GCM"
      keyRotationDays: 30
      
    # Audit settings
    audit:
      enabled: true
      retentionDays: 90
      
    # Auto-revocation triggers
    revocation:
      onPodTermination: true
      onTimeout: true
      onFailure: true
      onSuspiciousActivity: true

---
# SealedSecret for job application credentials
apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  name: job-credentials-template
  namespace: cvleap
spec:
  encryptedData:
    # These would be encrypted with the cluster's public key
    username: AgBy3i4OJSWK+PiTySYZZA9rO43cGDEQAx...
    password: AgBy3i4OJSWK+PiTySYZZA9rO43cGDEQAx...
    email: AgBy3i4OJSWK+PiTySYZZA9rO43cGDEQAx...
  template:
    metadata:
      name: job-credentials
      namespace: cvleap
      labels:
        app.kubernetes.io/component: sandbox-credentials
        cvleap.io/ttl: "3600"  # 1 hour TTL
        cvleap.io/auto-revoke: "true"
    type: Opaque

---
# CronJob for automatic secret cleanup
apiVersion: batch/v1
kind: CronJob
metadata:
  name: cvleap-secret-cleanup
  namespace: cvleap-system
spec:
  schedule: "*/5 * * * *"  # Every 5 minutes
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: cvleap-secret-manager
          containers:
          - name: secret-cleanup
            image: cvleap/secret-manager:latest
            command:
            - /bin/sh
            - -c
            - |
              # Cleanup expired secrets
              kubectl get secrets -n cvleap -l cvleap.io/auto-revoke=true -o json | \
              jq -r '.items[] | select(.metadata.labels."cvleap.io/ttl" | tonumber < now) | .metadata.name' | \
              xargs -I {} kubectl delete secret {} -n cvleap
              
              # Cleanup orphaned secrets (no associated pods)
              kubectl get secrets -n cvleap -l app.kubernetes.io/component=sandbox-credentials -o json | \
              jq -r '.items[] | select(.metadata.ownerReferences | length == 0) | .metadata.name' | \
              xargs -I {} kubectl delete secret {} -n cvleap
            env:
            - name: NAMESPACE
              value: cvleap
          restartPolicy: OnFailure

---
# ServiceAccount for secret management
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cvleap-secret-manager
  namespace: cvleap-system

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cvleap-secret-manager
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cvleap-secret-manager
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cvleap-secret-manager
subjects:
- kind: ServiceAccount
  name: cvleap-secret-manager
  namespace: cvleap-system

---
# Custom controller for secret lifecycle management
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cvleap-secret-controller
  namespace: cvleap-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cvleap-secret-controller
  template:
    metadata:
      labels:
        app: cvleap-secret-controller
    spec:
      serviceAccountName: cvleap-secret-manager
      containers:
      - name: controller
        image: cvleap/secret-controller:latest
        env:
        - name: NAMESPACE
          value: cvleap
        - name: DEFAULT_TTL
          value: "3600"  # 1 hour
        - name: MAX_TTL
          value: "86400"  # 24 hours
        ports:
        - containerPort: 8080
          name: metrics
        - containerPort: 8081
          name: health
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          limits:
            cpu: 200m
            memory: 128Mi
          requests:
            cpu: 100m
            memory: 64Mi

---
# ValidatingAdmissionWebhook for secret validation
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingAdmissionWebhook
metadata:
  name: cvleap-secret-validator
webhooks:
- name: secret.cvleap.io
  clientConfig:
    service:
      name: cvleap-secret-controller
      namespace: cvleap-system
      path: /validate-secret
  rules:
  - operations: ["CREATE", "UPDATE"]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["secrets"]
  namespaceSelector:
    matchLabels:
      name: cvleap
  admissionReviewVersions: ["v1", "v1beta1"]
  sideEffects: None
  failurePolicy: Fail

---
# MutatingAdmissionWebhook for automatic secret injection
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingAdmissionWebhook
metadata:
  name: cvleap-secret-injector
webhooks:
- name: inject.secret.cvleap.io
  clientConfig:
    service:
      name: cvleap-secret-controller
      namespace: cvleap-system
      path: /mutate-pod
  rules:
  - operations: ["CREATE"]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
  namespaceSelector:
    matchLabels:
      name: cvleap
  admissionReviewVersions: ["v1", "v1beta1"]
  sideEffects: None
  failurePolicy: Fail

---
# Example Pod with tmpfs secret mounting
apiVersion: v1
kind: Pod
metadata:
  name: sandbox-example
  namespace: cvleap
  labels:
    app.kubernetes.io/component: sandbox
    cvleap.io/inject-secrets: "true"
spec:
  serviceAccountName: cvleap-sandbox
  securityContext:
    runAsNonRoot: true
    runAsUser: 1001
    fsGroup: 1001
  containers:
  - name: sandbox
    image: cvleap/sandbox:latest
    env:
    - name: CREDENTIALS_PATH
      value: /tmp/credentials
    volumeMounts:
    # tmpfs mount for credentials (in-memory only)
    - name: credentials
      mountPath: /tmp/credentials
      readOnly: true
    # Regular volume for application data
    - name: app-data
      mountPath: /app/data
    securityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      capabilities:
        drop:
        - ALL
  volumes:
  # tmpfs volume for sensitive credentials
  - name: credentials
    emptyDir:
      medium: Memory
      sizeLimit: 1Mi
  # Regular volume for application data
  - name: app-data
    emptyDir:
      sizeLimit: 100Mi
  # Secret volume (will be copied to tmpfs by init container)
  - name: secret-source
    secret:
      secretName: job-credentials
      defaultMode: 0400  # Read-only for owner only
  initContainers:
  # Init container to copy secrets to tmpfs
  - name: secret-copier
    image: busybox:1.35
    command:
    - /bin/sh
    - -c
    - |
      # Copy secrets to tmpfs with restricted permissions
      cp -r /secret-source/* /tmp/credentials/
      chmod -R 400 /tmp/credentials/*
      chown -R 1001:1001 /tmp/credentials/
    volumeMounts:
    - name: secret-source
      mountPath: /secret-source
      readOnly: true
    - name: credentials
      mountPath: /tmp/credentials
    securityContext:
      runAsUser: 0  # Need root to set ownership
      allowPrivilegeEscalation: false

---
# NetworkPolicy for secret controller
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cvleap-secret-controller-netpol
  namespace: cvleap-system
spec:
  podSelector:
    matchLabels:
      app: cvleap-secret-controller
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow webhook traffic from API server
  - from:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 8443
  # Allow metrics scraping
  - from:
    - namespaceSelector:
        matchLabels:
          name: cvleap-monitoring
    ports:
    - protocol: TCP
      port: 8080
  egress:
  # Allow API server access
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 443
  # Allow DNS
  - to: []
    ports:
    - protocol: UDP
      port: 53
