apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: cvleap-platform-alerts
  namespace: cvleap-monitoring
  labels:
    app: cvleap-platform
    prometheus: kube-prometheus
    role: alert-rules
spec:
  groups:
  - name: cvleap.cost.rules
    interval: 30s
    rules:
    - alert: HighCostPerSubmission
      expr: |
        (
          sum(rate(cvleap_cost_total[5m])) / 
          sum(rate(cvleap_job_submissions_total[5m]))
        ) > 0.002
      for: 2m
      labels:
        severity: warning
        component: cost-monitoring
      annotations:
        summary: "Cost per submission exceeds budget"
        description: "Average cost per submission is {{ $value | humanize }}$, exceeding the $0.002 target"
        runbook_url: "https://docs.cvleap.com/runbooks/cost-optimization"

    - alert: DailyCostBudgetExceeded
      expr: |
        sum(increase(cvleap_cost_total[24h])) > 100
      for: 5m
      labels:
        severity: critical
        component: cost-monitoring
      annotations:
        summary: "Daily cost budget exceeded"
        description: "Daily cost is {{ $value | humanize }}$, exceeding the $100 budget"

  - name: cvleap.performance.rules
    interval: 30s
    rules:
    - alert: SlowSandboxStartup
      expr: |
        histogram_quantile(0.95, 
          rate(cvleap_sandbox_startup_duration_seconds_bucket[5m])
        ) > 1.0
      for: 2m
      labels:
        severity: warning
        component: performance
      annotations:
        summary: "Sandbox startup time is slow"
        description: "95th percentile startup time is {{ $value | humanize }}s, exceeding 1s target"

    - alert: HighMemoryUsage
      expr: |
        sum(container_memory_working_set_bytes{namespace="cvleap"}) / 
        sum(container_spec_memory_limit_bytes{namespace="cvleap"}) > 0.8
      for: 5m
      labels:
        severity: warning
        component: resource-management
      annotations:
        summary: "High memory usage in CVLeap namespace"
        description: "Memory usage is {{ $value | humanizePercentage }}, approaching limits"

  - name: cvleap.reliability.rules
    interval: 30s
    rules:
    - alert: HighErrorRate
      expr: |
        sum(rate(cvleap_job_submissions_total{status="failed"}[5m])) /
        sum(rate(cvleap_job_submissions_total[5m])) > 0.1
      for: 2m
      labels:
        severity: critical
        component: reliability
      annotations:
        summary: "High job submission error rate"
        description: "Error rate is {{ $value | humanizePercentage }}, exceeding 10% threshold"

    - alert: SandboxControllerDown
      expr: |
        up{job="cvleap-sandbox-controller"} == 0
      for: 1m
      labels:
        severity: critical
        component: availability
      annotations:
        summary: "Sandbox controller is down"
        description: "The sandbox controller has been down for more than 1 minute"

  - name: cvleap.security.rules
    interval: 30s
    rules:
    - alert: UnauthorizedEgressDetected
      expr: |
        increase(cvleap_security_violations_total{type="unauthorized_egress"}[5m]) > 0
      for: 0m
      labels:
        severity: critical
        component: security
      annotations:
        summary: "Unauthorized network egress detected"
        description: "{{ $value }} unauthorized egress attempts detected in the last 5 minutes"

    - alert: SecretLeakageDetected
      expr: |
        increase(cvleap_security_violations_total{type="secret_leakage"}[5m]) > 0
      for: 0m
      labels:
        severity: critical
        component: security
      annotations:
        summary: "Potential secret leakage detected"
        description: "{{ $value }} potential secret leakage events detected"

    - alert: ExpiredSecretsNotCleaned
      expr: |
        cvleap_expired_secrets_count > 5
      for: 5m
      labels:
        severity: warning
        component: security
      annotations:
        summary: "Expired secrets not cleaned up"
        description: "{{ $value }} expired secrets are still present in the cluster"

  - name: cvleap.business.rules
    interval: 60s
    rules:
    - alert: LowSuccessRateForJobSite
      expr: |
        (
          sum(rate(cvleap_job_submissions_total{status="success"}[1h])) by (job_site) /
          sum(rate(cvleap_job_submissions_total[1h])) by (job_site)
        ) < 0.7
      for: 10m
      labels:
        severity: warning
        component: business-metrics
      annotations:
        summary: "Low success rate for job site {{ $labels.job_site }}"
        description: "Success rate for {{ $labels.job_site }} is {{ $value | humanizePercentage }}, below 70% threshold"

    - alert: NoSubmissionsInLastHour
      expr: |
        sum(rate(cvleap_job_submissions_total[1h])) == 0
      for: 30m
      labels:
        severity: warning
        component: business-metrics
      annotations:
        summary: "No job submissions in the last hour"
        description: "The platform has not processed any job submissions in the last hour"

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: cvleap-platform
  namespace: cvleap-monitoring
  labels:
    app: cvleap-platform
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cvleap-sandbox
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    honorLabels: true
  - port: terminal-metrics
    interval: 30s
    path: /metrics
    honorLabels: true
  namespaceSelector:
    matchNames:
    - cvleap

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: cvleap-loki-config
  namespace: cvleap-monitoring
data:
  loki.yaml: |
    auth_enabled: false
    
    server:
      http_listen_port: 3100
      grpc_listen_port: 9096
    
    common:
      path_prefix: /tmp/loki
      storage:
        filesystem:
          chunks_directory: /tmp/loki/chunks
          rules_directory: /tmp/loki/rules
      replication_factor: 1
      ring:
        instance_addr: 127.0.0.1
        kvstore:
          store: inmemory
    
    schema_config:
      configs:
        - from: 2020-10-24
          store: boltdb-shipper
          object_store: filesystem
          schema: v11
          index:
            prefix: index_
            period: 24h
    
    ruler:
      alertmanager_url: http://alertmanager:9093
    
    limits_config:
      enforce_metric_name: false
      reject_old_samples: true
      reject_old_samples_max_age: 168h
      ingestion_rate_mb: 16
      ingestion_burst_size_mb: 32
      per_stream_rate_limit: 8MB
      per_stream_rate_limit_burst: 16MB
    
    # CVLeap-specific log parsing
    analytics:
      reporting_enabled: false

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: cvleap-jaeger-config
  namespace: cvleap-monitoring
data:
  jaeger.yaml: |
    # Jaeger configuration for CVLeap tracing
    collector:
      zipkin:
        host-port: :9411
      otlp:
        grpc:
          host-port: :14250
        http:
          host-port: :14268
    
    storage:
      type: elasticsearch
      elasticsearch:
        server-urls: http://elasticsearch:9200
        index-prefix: cvleap-traces
        
    # CVLeap-specific sampling
    sampling:
      default_strategy:
        type: probabilistic
        param: 0.1  # Sample 10% of traces
      per_service_strategies:
        - service: "cvleap-sandbox"
          type: probabilistic
          param: 1.0  # Sample 100% of sandbox traces
        - service: "cvleap-terminal"
          type: probabilistic
          param: 0.5  # Sample 50% of terminal traces

---
# Fluent Bit configuration for log collection
apiVersion: v1
kind: ConfigMap
metadata:
  name: cvleap-fluent-bit-config
  namespace: cvleap-monitoring
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush         1
        Log_Level     info
        Daemon        off
        Parsers_File  parsers.conf
        HTTP_Server   On
        HTTP_Listen   0.0.0.0
        HTTP_Port     2020
    
    [INPUT]
        Name              tail
        Path              /var/log/containers/*cvleap*.log
        Parser            docker
        Tag               cvleap.*
        Refresh_Interval  5
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
    
    [FILTER]
        Name                kubernetes
        Match               cvleap.*
        Kube_URL            https://kubernetes.default.svc:443
        Kube_CA_File        /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File     /var/run/secrets/kubernetes.io/serviceaccount/token
        Kube_Tag_Prefix     cvleap.var.log.containers.
        Merge_Log           On
        Keep_Log            Off
        K8S-Logging.Parser  On
        K8S-Logging.Exclude Off
        Annotations         Off
        Labels              On
    
    [FILTER]
        Name    modify
        Match   cvleap.*
        Add     platform cvleap
        Add     environment ${ENVIRONMENT}
    
    [OUTPUT]
        Name            loki
        Match           cvleap.*
        Host            loki
        Port            3100
        Labels          job=cvleap, namespace=$kubernetes['namespace_name'], pod=$kubernetes['pod_name']
        Label_keys      $kubernetes['labels']
        Batch_wait      1
        Batch_size      1001024
        Line_format     json
        Remove_keys     kubernetes,stream,time
        Auto_kubernetes_labels on
