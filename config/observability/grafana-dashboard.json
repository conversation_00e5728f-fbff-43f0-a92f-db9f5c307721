{"dashboard": {"id": null, "title": "CVLeap Sandbox Platform", "tags": ["cvleap", "sandbox", "job-applications"], "timezone": "browser", "panels": [{"id": 1, "title": "Executive Summary", "type": "stat", "targets": [{"expr": "sum(rate(cvleap_job_submissions_total[24h]))", "legendFormat": "Applications/Day"}, {"expr": "sum(rate(cvleap_job_submissions_total{status=\"success\"}[24h])) / sum(rate(cvleap_job_submissions_total[24h])) * 100", "legendFormat": "Success Rate %"}, {"expr": "sum(cvleap_cost_total[24h])", "legendFormat": "Daily Cost $"}], "fieldConfig": {"defaults": {"unit": "short", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 80}, {"color": "red", "value": 90}]}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}}, {"id": 2, "title": "Runtime Performance", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(cvleap_sandbox_startup_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile startup time"}, {"expr": "histogram_quantile(0.50, rate(cvleap_sandbox_startup_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile startup time"}], "fieldConfig": {"defaults": {"unit": "s", "custom": {"drawStyle": "line", "lineInterpolation": "linear"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 3, "title": "Resource Utilization", "type": "timeseries", "targets": [{"expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"cvleap\"}[5m])) by (runtime)", "legendFormat": "CPU Usage - {{runtime}}"}, {"expr": "sum(container_memory_working_set_bytes{namespace=\"cvleap\"}) by (runtime) / 1024 / 1024", "legendFormat": "Memory Usage MB - {{runtime}}"}], "fieldConfig": {"defaults": {"unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 4, "title": "Job Site Success Rates", "type": "table", "targets": [{"expr": "sum(rate(cvleap_job_submissions_total{status=\"success\"}[24h])) by (job_site) / sum(rate(cvleap_job_submissions_total[24h])) by (job_site) * 100", "format": "table", "instant": true}], "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"job_site": "Job Site", "Value": "Success Rate %"}}}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 5, "title": "Cost Analysis", "type": "piechart", "targets": [{"expr": "sum(cvleap_cost_total[24h]) by (runtime)", "legendFormat": "{{runtime}}"}], "fieldConfig": {"defaults": {"unit": "currencyUSD"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 6, "title": "Error Rate by Runtime", "type": "timeseries", "targets": [{"expr": "sum(rate(cvleap_job_submissions_total{status=\"failed\"}[5m])) by (runtime)", "legendFormat": "Errors - {{runtime}}"}], "fieldConfig": {"defaults": {"unit": "reqps", "custom": {"drawStyle": "line", "lineInterpolation": "linear"}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}, {"id": 7, "title": "Network Egress by Domain", "type": "table", "targets": [{"expr": "sum(rate(cvleap_network_egress_bytes_total[1h])) by (destination_domain)", "format": "table", "instant": true}], "transformations": [{"id": "organize", "options": {"renameByName": {"destination_domain": "Domain", "Value": "Bytes/Hour"}}}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}}, {"id": 8, "title": "Security Events", "type": "logs", "targets": [{"expr": "{namespace=\"cvleap\"} |= \"SECURITY\" | json", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}}], "time": {"from": "now-24h", "to": "now"}, "refresh": "30s", "schemaVersion": 30, "version": 1, "links": [{"title": "CV<PERSON><PERSON><PERSON>", "url": "/jaeger", "type": "link"}, {"title": "CVLeap Logs", "url": "/loki", "type": "link"}]}}