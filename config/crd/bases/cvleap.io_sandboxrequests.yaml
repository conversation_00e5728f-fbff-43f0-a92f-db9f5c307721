apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.13.0
  name: sandboxrequests.cvleap.io
spec:
  group: cvleap.io
  names:
    kind: SandboxRequest
    listKind: SandboxRequestList
    plural: sandboxrequests
    singular: sandboxrequest
    shortNames:
    - sbr
    - sandbox
  scope: Namespaced
  versions:
  - name: v1alpha1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            type: object
            required:
            - jobId
            - userId
            - jobSite
            properties:
              jobId:
                type: string
                description: "Unique identifier for the job application"
              userId:
                type: string
                description: "User requesting the sandbox"
              runtime:
                type: string
                enum:
                - docker-in-pod
                - firecracker
                - kubevirt
                default: docker-in-pod
                description: "Preferred runtime for the sandbox"
              jobSite:
                type: object
                required:
                - domain
                - url
                properties:
                  domain:
                    type: string
                    description: "Job site domain for network policy"
                  url:
                    type: string
                    description: "Specific job URL to apply to"
                  selectors:
                    type: object
                    additionalProperties:
                      type: string
                    description: "CSS selectors for form automation"
              resources:
                type: object
                properties:
                  cpu:
                    type: string
                    default: "500m"
                    description: "CPU request/limit"
                  memory:
                    type: string
                    default: "256Mi"
                    description: "Memory request/limit"
                  timeout:
                    type: string
                    default: "300s"
                    description: "Maximum execution time"
                  storage:
                    type: string
                    default: "1Gi"
                    description: "Ephemeral storage limit"
              credentials:
                type: object
                properties:
                  sealedSecretRef:
                    type: string
                    description: "Reference to sealed secret with credentials"
                  ttl:
                    type: string
                    default: "1h"
                    description: "Credential time-to-live"
              networking:
                type: object
                properties:
                  egressDomains:
                    type: array
                    items:
                      type: string
                    description: "Additional allowed egress domains"
                  dnsPolicy:
                    type: string
                    enum:
                    - ClusterFirst
                    - None
                    default: ClusterFirst
              observability:
                type: object
                properties:
                  enableTracing:
                    type: boolean
                    default: true
                  enableMetrics:
                    type: boolean
                    default: true
                  logLevel:
                    type: string
                    enum:
                    - debug
                    - info
                    - warn
                    - error
                    default: info
          status:
            type: object
            properties:
              phase:
                type: string
                enum:
                - Pending
                - Provisioning
                - Running
                - Succeeded
                - Failed
                - Terminating
                description: "Current phase of the sandbox"
              runtime:
                type: string
                description: "Actually selected runtime"
              podName:
                type: string
                description: "Name of the created pod (for docker-in-pod)"
              vmName:
                type: string
                description: "Name of the created VM (for firecracker/kubevirt)"
              startTime:
                type: string
                format: date-time
                description: "When the sandbox started"
              completionTime:
                type: string
                format: date-time
                description: "When the sandbox completed"
              conditions:
                type: array
                items:
                  type: object
                  required:
                  - type
                  - status
                  properties:
                    type:
                      type: string
                    status:
                      type: string
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                    lastTransitionTime:
                      type: string
                      format: date-time
                    reason:
                      type: string
                    message:
                      type: string
              results:
                type: object
                properties:
                  success:
                    type: boolean
                  applicationId:
                    type: string
                  screenshots:
                    type: array
                    items:
                      type: string
                  logs:
                    type: string
                    description: "Reference to log bundle"
                  cost:
                    type: string
                    description: "Actual cost of execution"
    additionalPrinterColumns:
    - name: Phase
      type: string
      jsonPath: .status.phase
    - name: Runtime
      type: string
      jsonPath: .status.runtime
    - name: Job ID
      type: string
      jsonPath: .spec.jobId
    - name: Age
      type: date
      jsonPath: .metadata.creationTimestamp
    subresources:
      status: {}
