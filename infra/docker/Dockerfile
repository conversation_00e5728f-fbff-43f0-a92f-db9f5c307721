# Multi-stage build for terminald
FROM golang:1.21-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /build

# Copy go mod files
COPY services/terminald/go.mod services/terminald/go.sum ./
RUN go mod download

# Copy source code
COPY services/terminald/ ./
COPY pkg/ ./pkg/
COPY internal/ ./internal/
COPY cmd/ ./cmd/

# Build the binary
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o terminald ./cmd/terminald

# Final stage - based on Playwright image for browser automation
FROM mcr.microsoft.com/playwright:v1.45.0-jammy

# Install tmux and other required tools
RUN apt-get update && apt-get install -y \
    tmux \
    curl \
    jq \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN useradd -m -u 1001 -s /bin/bash cvleap

# Copy terminald binary from builder
COPY --from=builder /build/terminald /usr/local/bin/terminald
RUN chmod +x /usr/local/bin/terminald

# Create necessary directories
RUN mkdir -p /app/logs /app/data && \
    chown -R cvleap:cvleap /app

# Switch to non-root user
USER cvleap
WORKDIR /app

# Set default environment variables
ENV PORT=7000
ENV ENABLE_TERMINAL=false
ENV RING_BUFFER_SIZE=10485760
ENV TMUX_SESSION=cvleap

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Expose port
EXPOSE 7000

# Start terminald
CMD ["terminald"]
