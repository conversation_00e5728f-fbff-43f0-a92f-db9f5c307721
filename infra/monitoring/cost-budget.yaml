apiVersion: v1
kind: ConfigMap
metadata:
  name: cvleap-cost-budget
  namespace: cvleap
  labels:
    app.kubernetes.io/name: cvleap-sandbox
    app.kubernetes.io/component: monitoring
data:
  cost-budget.yaml: |
    # CVLeap Sandbox Cost Budget Configuration
    
    # Target cost per application
    target_cost_per_application: "$0.005"
    
    # Resource limits and requests
    resources:
      terminald:
        limits:
          cpu: "200m"      # 0.2 CPU cores
          memory: "128Mi"   # 128 MiB RAM
        requests:
          cpu: "50m"       # 0.05 CPU cores  
          memory: "32Mi"   # 32 MiB RAM
        idle_limits:
          cpu: "10m"       # ~0% CPU when idle
          memory: "5Mi"    # <5 MiB RSS when idle
      
      sandbox_app:
        limits:
          cpu: "1000m"     # 1 CPU core
          memory: "512Mi"  # 512 MiB RAM
        requests:
          cpu: "100m"      # 0.1 CPU cores
          memory: "128Mi"  # 128 MiB RAM
    
    # Cost calculation parameters
    cost_model:
      # AWS pricing (us-east-1, on-demand)
      cpu_cost_per_hour: "$0.04656"    # per vCPU hour
      memory_cost_per_hour: "$0.00511" # per GiB hour
      
      # Average application duration
      avg_application_duration_minutes: 5
      
      # Overhead factors
      kubernetes_overhead: 0.1  # 10% overhead
      network_cost: "$0.0001"   # per application
      storage_cost: "$0.0001"   # per application
    
    # Monitoring thresholds
    alerts:
      cpu_utilization_high: 80    # Alert if CPU > 80%
      memory_utilization_high: 85 # Alert if memory > 85%
      cost_per_app_high: "$0.007" # Alert if cost > $0.007
      idle_memory_high: "10Mi"    # Alert if idle memory > 10 MiB
    
    # Auto-scaling parameters
    autoscaling:
      enabled: false
      min_replicas: 1
      max_replicas: 10
      target_cpu_utilization: 70
      target_memory_utilization: 75
      scale_down_delay: "5m"
      scale_up_delay: "30s"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: cvleap-resource-quotas
  namespace: cvleap
data:
  resource-quota.yaml: |
    # Namespace-level resource quotas
    compute:
      requests.cpu: "10"      # 10 CPU cores total
      requests.memory: "20Gi" # 20 GiB memory total
      limits.cpu: "20"        # 20 CPU cores total
      limits.memory: "40Gi"   # 40 GiB memory total
    
    storage:
      requests.storage: "100Gi"
      persistentvolumeclaims: "10"
    
    objects:
      pods: "50"
      services: "10"
      secrets: "20"
      configmaps: "20"

---
# Prometheus monitoring rules for cost tracking
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: cvleap-cost-monitoring
  namespace: cvleap
spec:
  groups:
  - name: cvleap.cost.rules
    rules:
    - alert: HighCostPerApplication
      expr: |
        (
          rate(container_cpu_usage_seconds_total{namespace="cvleap",container="terminald"}[5m]) * 0.04656 +
          container_memory_working_set_bytes{namespace="cvleap",container="terminald"} / 1024 / 1024 / 1024 * 0.00511
        ) * 300 > 0.007  # 5 minutes * cost per second > $0.007
      for: 2m
      labels:
        severity: warning
        component: cost-monitoring
      annotations:
        summary: "High cost per application detected"
        description: "Cost per application is {{ $value | humanize }}$, exceeding budget of $0.005"
    
    - alert: HighIdleMemoryUsage
      expr: |
        container_memory_working_set_bytes{namespace="cvleap",container="terminald"} / 1024 / 1024 > 10
        and
        rate(container_cpu_usage_seconds_total{namespace="cvleap",container="terminald"}[5m]) < 0.01
      for: 5m
      labels:
        severity: warning
        component: resource-monitoring
      annotations:
        summary: "High idle memory usage in terminald"
        description: "terminald is using {{ $value | humanize }}MiB while idle (target: <5MiB)"
    
    - alert: TerminalServiceDown
      expr: up{job="terminald"} == 0
      for: 1m
      labels:
        severity: critical
        component: availability
      annotations:
        summary: "Terminal service is down"
        description: "terminald service has been down for more than 1 minute"
