# Default values for cvleap-sandbox
replicaCount: 1

image:
  repository: cvleap/sandbox
  pullPolicy: IfNotPresent
  tag: "latest"

# Terminal feature configuration
enableTerminal: false

# Service configuration
service:
  type: ClusterIP
  port: 80
  targetPort: 8000
  
  # Terminal service (only created when enableTerminal is true)
  terminal:
    port: 7000
    targetPort: 7000

# Resource limits
resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

# Security context
securityContext:
  runAsNonRoot: true
  runAsUser: 1001
  runAsGroup: 1001
  fsGroup: 1001
  capabilities:
    drop:
      - ALL
  readOnlyRootFilesystem: false  # tmux needs write access
  allowPrivilegeEscalation: false

# Pod security context
podSecurityContext:
  seccompProfile:
    type: RuntimeDefault

# Environment variables
env:
  - name: ENABLE_TERMINAL
    value: "{{ .Values.enableTerminal }}"
  - name: JWT_SECRET
    valueFrom:
      secretKeyRef:
        name: cvleap-secrets
        key: jwt-secret
  - name: RING_BUFFER_SIZE
    value: "10485760"
  - name: PORT
    value: "8000"
  - name: TERMINAL_PORT
    value: "7000"

# Probes
livenessProbe:
  httpGet:
    path: /health
    port: 8000
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /ready
    port: 8000
  initialDelaySeconds: 5
  periodSeconds: 5

# Terminal-specific probes (when enabled)
terminalProbes:
  livenessProbe:
    httpGet:
      path: /health
      port: 7000
    initialDelaySeconds: 10
    periodSeconds: 30
  readinessProbe:
    httpGet:
      path: /health
      port: 7000
    initialDelaySeconds: 5
    periodSeconds: 10

# Network policy
networkPolicy:
  enabled: true
  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              name: cvleap-api
      ports:
        - protocol: TCP
          port: 8000
        - protocol: TCP
          port: 7000
  egress:
    # Allow DNS
    - to: []
      ports:
        - protocol: UDP
          port: 53
    # Allow job sites (to be configured per environment)
    - to:
        - namespaceSelector:
            matchLabels:
              name: internet-gateway
      ports:
        - protocol: TCP
          port: 443
        - protocol: TCP
          port: 80

# Horizontal Pod Autoscaler
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

# Service account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod annotations
podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8000"
  prometheus.io/path: "/metrics"

# Pod labels
podLabels:
  app.kubernetes.io/component: sandbox

# Ingress (for terminal WebSocket)
ingress:
  enabled: false
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/websocket-services: "cvleap-sandbox-terminal"
  hosts:
    - host: terminal.cvleap.local
      paths:
        - path: /
          pathType: Prefix
          service:
            name: cvleap-sandbox-terminal
            port: 7000
  tls: []
