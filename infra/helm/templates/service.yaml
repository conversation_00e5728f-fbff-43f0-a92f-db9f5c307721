apiVersion: v1
kind: Service
metadata:
  name: {{ include "cvleap-sandbox.fullname" . }}
  labels:
    {{- include "cvleap-sandbox.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "cvleap-sandbox.selectorLabels" . | nindent 4 }}

---
{{- if .Values.enableTerminal }}
# Terminal WebSocket service (only created when terminal is enabled)
apiVersion: v1
kind: Service
metadata:
  name: {{ include "cvleap-sandbox.fullname" . }}-terminal
  labels:
    {{- include "cvleap-sandbox.labels" . | nindent 4 }}
    app.kubernetes.io/component: terminal
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.terminal.port }}
      targetPort: {{ .Values.service.terminal.targetPort }}
      protocol: TCP
      name: terminal-ws
  selector:
    {{- include "cvleap-sandbox.selectorLabels" . | nindent 4 }}
{{- end }}
