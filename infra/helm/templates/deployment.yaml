apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "cvleap-sandbox.fullname" . }}
  labels:
    {{- include "cvleap-sandbox.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "cvleap-sandbox.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "cvleap-sandbox.selectorLabels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "cvleap-sandbox.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        # Main application container
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 8000
              protocol: TCP
          env:
            {{- range .Values.env }}
            - name: {{ .name }}
              {{- if .value }}
              value: {{ .value | quote }}
              {{- else if .valueFrom }}
              valueFrom:
                {{- toYaml .valueFrom | nindent 16 }}
              {{- end }}
            {{- end }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: tmp
              mountPath: /tmp
            - name: app-data
              mountPath: /app/data
        
        {{- if .Values.enableTerminal }}
        # Terminal sidecar container
        - name: terminald
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["terminald"]
          ports:
            - name: terminal
              containerPort: 7000
              protocol: TCP
          env:
            - name: ENABLE_TERMINAL
              value: "true"
            - name: PORT
              value: "7000"
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: cvleap-secrets
                  key: jwt-secret
            - name: RING_BUFFER_SIZE
              value: "{{ .Values.env | selectattr "name" "equalto" "RING_BUFFER_SIZE" | first | default (dict "value" "10485760") | get "value" }}"
          livenessProbe:
            {{- toYaml .Values.terminalProbes.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.terminalProbes.readinessProbe | nindent 12 }}
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 32Mi
          volumeMounts:
            - name: tmp
              mountPath: /tmp
            - name: app-data
              mountPath: /app/data
        {{- end }}
      
      volumes:
        - name: tmp
          emptyDir: {}
        - name: app-data
          emptyDir: {}
      
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
