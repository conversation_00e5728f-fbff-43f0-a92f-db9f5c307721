{{- if .Values.networkPolicy.enabled }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "cvleap-sandbox.fullname" . }}-netpol
  labels:
    {{- include "cvleap-sandbox.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      {{- include "cvleap-sandbox.selectorLabels" . | nindent 6 }}
  policyTypes:
    - Ingress
    - Egress
  
  ingress:
    {{- range .Values.networkPolicy.ingress }}
    - from:
        {{- if .from }}
        {{- toYaml .from | nindent 8 }}
        {{- end }}
      ports:
        {{- if .ports }}
        {{- toYaml .ports | nindent 8 }}
        {{- end }}
    {{- end }}
  
  egress:
    # Allow DNS resolution
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    
    # Allow NTP
    - to: []
      ports:
        - protocol: UDP
          port: 123
    
    # Allow job sites and CAPTCHA solver
    {{- range .Values.networkPolicy.egress }}
    - to:
        {{- if .to }}
        {{- toYaml .to | nindent 8 }}
        {{- else }}
        # Allow specific job domains
        - namespaceSelector:
            matchLabels:
              name: internet-gateway
        {{- end }}
      ports:
        {{- if .ports }}
        {{- toYaml .ports | nindent 8 }}
        {{- else }}
        - protocol: TCP
          port: 443
        - protocol: TCP
          port: 80
        {{- end }}
    {{- end }}
    
    # Block all other egress traffic
    # (Kubernetes NetworkPolicy is default-deny when egress rules are specified)

---
# Additional NetworkPolicy for terminal-specific traffic
{{- if .Values.enableTerminal }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "cvleap-sandbox.fullname" . }}-terminal-netpol
  labels:
    {{- include "cvleap-sandbox.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      {{- include "cvleap-sandbox.selectorLabels" . | nindent 6 }}
  policyTypes:
    - Ingress
  
  ingress:
    # Allow terminal WebSocket connections from API proxy
    - from:
        - namespaceSelector:
            matchLabels:
              name: cvleap-api
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: cvleap-api-proxy
      ports:
        - protocol: TCP
          port: 7000
    
    # Allow health checks from ingress controller
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
      ports:
        - protocol: TCP
          port: 7000
{{- end }}
{{- end }}
