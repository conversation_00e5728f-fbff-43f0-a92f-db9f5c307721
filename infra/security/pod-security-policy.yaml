apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: cvleap-sandbox-psp
  labels:
    app.kubernetes.io/name: cvleap-sandbox
    app.kubernetes.io/component: security
spec:
  # Privilege and access controls
  privileged: false
  allowPrivilegeEscalation: false
  
  # Required security contexts
  requiredDropCapabilities:
    - ALL
  allowedCapabilities: []
  
  # User and group controls
  runAsUser:
    rule: 'MustRunAsNonRoot'
  runAsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1001
        max: 1001
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1001
        max: 1001
  
  # Volume controls
  volumes:
    - 'emptyDir'
    - 'configMap'
    - 'secret'
    - 'projected'
    - 'downwardAPI'
  
  # Host controls
  hostNetwork: false
  hostIPC: false
  hostPID: false
  hostPorts: []
  
  # SELinux
  seLinux:
    rule: 'RunAsAny'
  
  # Seccomp
  seccompProfiles:
    - 'runtime/default'
  
  # AppArmor (if available)
  annotations:
    apparmor.security.beta.kubernetes.io/allowedProfileNames: 'runtime/default'
    apparmor.security.beta.kubernetes.io/defaultProfileName: 'runtime/default'

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cvleap-sandbox-psp-user
rules:
- apiGroups: ['policy']
  resources: ['podsecuritypolicies']
  verbs: ['use']
  resourceNames:
  - cvleap-sandbox-psp

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cvleap-sandbox-psp-binding
roleRef:
  kind: ClusterRole
  name: cvleap-sandbox-psp-user
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: cvleap-sandbox
  namespace: cvleap
