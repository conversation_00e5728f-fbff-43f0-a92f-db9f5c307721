package types

import (
	"context"
	"io"
	"time"
)

// TerminalSession represents a PTY session with ring buffer
type TerminalSession struct {
	ID          string
	PTY         io.ReadWriteCloser
	RingBuffer  *RingBuffer
	CreatedAt   time.Time
	LastAccess  time.Time
	Clients     map[string]*WebSocketClient
}

// WebSocketClient represents a connected WebSocket client
type WebSocketClient struct {
	ID       string
	UserID   string
	IP       string
	ConnTime time.Time
	Send     chan []byte
	Done     chan struct{}
}

// RingBuffer provides a circular buffer for terminal output
type RingBuffer struct {
	data     []byte
	size     int
	head     int
	tail     int
	full     bool
	maxSize  int
}

// JWTClaims represents the JWT token claims for authentication
type JWTClaims struct {
	UserID    string `json:"user_id"`
	SessionID string `json:"session_id"`
	IP        string `json:"ip"`
	Role      string `json:"role"`
	Exp       int64  `json:"exp"`
}

// TerminalManager interface for managing terminal sessions
type TerminalManager interface {
	CreateSession(ctx context.Context, sessionID string) (*TerminalSession, error)
	GetSession(sessionID string) (*TerminalSession, error)
	CloseSession(sessionID string) error
	ListSessions() []*TerminalSession
}

// AuthService interface for JWT validation
type AuthService interface {
	ValidateToken(token string, clientIP string) (*JWTClaims, error)
	IsAdvancedUser(role string) bool
}

// WebSocketHandler interface for WebSocket management
type WebSocketHandler interface {
	HandleConnection(ctx context.Context, sessionID string, claims *JWTClaims) error
	BroadcastToSession(sessionID string, data []byte) error
	CloseClient(sessionID, clientID string) error
}

// Config holds the service configuration
type Config struct {
	Port           int    `env:"PORT" default:"7000"`
	JWTSecret      string `env:"JWT_SECRET" required:"true"`
	RingBufferSize int    `env:"RING_BUFFER_SIZE" default:"10485760"` // 10MB
	EnableTerminal bool   `env:"ENABLE_TERMINAL" default:"false"`
	TmuxSession    string `env:"TMUX_SESSION" default:"cvleap"`
}
