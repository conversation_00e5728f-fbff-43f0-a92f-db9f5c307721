# CVLeap Sandbox Terminal

On-demand terminal viewer for CVLeap's autonomous job application sandbox.

## Architecture

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   React Hook    │◄──►│  API Proxy   │◄──►│   terminald     │
│ useSandboxTerm  │    │ /api/sandbox │    │ (Go WebSocket)  │
└─────────────────┘    └──────────────┘    └─────────────────┘
                              │                       │
                              ▼                       ▼
                       ┌──────────────┐    ┌─────────────────┐
                       │ FastAPI      │    │ tmux session    │
                       │ Sidecar      │    │ + ring buffer   │
                       └──────────────┘    └─────────────────┘
```

## Components

### 1. terminald (Go microservice)
- PTY ↔ WebSocket bridge
- 10MB ring buffer for back-scroll
- JWT authentication via Sec-WebSocket-Protocol
- Idle resource usage: <5 MiB RSS, ~0% CPU

### 2. FastAPI Sidecar
- `/ready` - Health check
- `/rpc/status` - Application progress
- `/rpc/signal` - Pause/resume control

### 3. API Proxy Route
- RBAC verification
- Single-use JWT minting (60s, IP-bound)
- WebSocket redirect to pod

### 4. React Hook
- Lazy xterm.js loading
- Ring buffer replay + live streaming
- Hotkey capture (⌘K)

## Security

- NetworkPolicy: egress whitelist for job domains only
- stdin filtering (Advanced users only)
- Single-use JWT tokens
- IP-bound authentication

## Cost Budget

Target: ≤$0.005 per application (unchanged when terminal unused)

## Development

```bash
# Build terminald
cd services/terminald
go build -o terminald .

# Run tests
go test ./...

# Build container
docker build -t cvleap/terminald .
```

## Environment Variables

- `ENABLE_TERMINAL`: Enable terminal feature (default: false)
- `JWT_SECRET`: Secret for JWT validation
- `RING_BUFFER_SIZE`: Buffer size in bytes (default: 10MB)
