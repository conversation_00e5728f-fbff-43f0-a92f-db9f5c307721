"""
MLOps Model Registry
Model versioning, A/B testing, and deployment management
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import hashlib

import mlflow
import wandb
from mlflow.tracking import M<PERSON><PERSON><PERSON>lient
from mlflow.models import infer_signature
import boto3
import redis
import numpy as np

from api.utils.config import Settings


class ModelStatus(Enum):
    """Model deployment status"""
    TRAINING = "training"
    VALIDATION = "validation"
    STAGING = "staging"
    PRODUCTION = "production"
    ARCHIVED = "archived"
    FAILED = "failed"


class ExperimentType(Enum):
    """Types of ML experiments"""
    AB_TEST = "ab_test"
    CHAMPION_CHALLENGER = "champion_challenger"
    CANARY = "canary"
    SHADOW = "shadow"


@dataclass
class ModelMetadata:
    """Model metadata and metrics"""
    model_id: str
    name: str
    version: str
    framework: str
    algorithm: str
    status: ModelStatus
    created_at: float
    updated_at: float
    metrics: Dict[str, float]
    hyperparameters: Dict[str, Any]
    training_data_hash: str
    model_size_mb: float
    inference_latency_ms: float
    accuracy: float
    tags: List[str]
    description: str
    author: str


@dataclass
class ABTestConfig:
    """A/B test configuration"""
    experiment_id: str
    name: str
    description: str
    models: List[str]  # Model IDs
    traffic_split: Dict[str, float]  # Model ID -> traffic percentage
    success_metrics: List[str]
    duration_days: int
    min_sample_size: int
    confidence_level: float
    created_at: float
    status: str


class ModelRegistry:
    """MLOps model registry with versioning and A/B testing"""
    
    def __init__(self):
        self.settings = Settings()
        self.mlflow_client = None
        self.wandb_client = None
        self.redis_client = None
        self.s3_client = None
        
        # Model storage
        self.models: Dict[str, ModelMetadata] = {}
        self.experiments: Dict[str, ABTestConfig] = {}
        self.deployment_configs: Dict[str, Dict] = {}
        
        # Performance tracking
        self.model_performance: Dict[str, List[Dict]] = {}
        self.traffic_routing: Dict[str, str] = {}  # Request ID -> Model ID
        
    async def initialize(self):
        """Initialize MLOps infrastructure"""
        print("🚀 Initializing MLOps Model Registry...")
        
        try:
            # Initialize MLflow
            mlflow.set_tracking_uri(self.settings.mlflow_tracking_uri)
            self.mlflow_client = MlflowClient()
            print("✅ MLflow client initialized")
            
            # Initialize Weights & Biases
            if self.settings.wandb_api_key:
                wandb.login(key=self.settings.wandb_api_key)
                self.wandb_client = wandb
                print("✅ Weights & Biases initialized")
            
            # Initialize Redis for caching and routing
            self.redis_client = redis.Redis(
                host=self.settings.redis_host,
                port=self.settings.redis_port,
                decode_responses=True
            )
            print("✅ Redis client initialized")
            
            # Initialize S3 for model artifacts
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=self.settings.aws_access_key_id,
                aws_secret_access_key=self.settings.aws_secret_access_key,
                region_name=self.settings.aws_region
            )
            print("✅ S3 client initialized")
            
            # Load existing models and experiments
            await self._load_registry_state()
            
            print("✅ MLOps Model Registry initialized")
            
        except Exception as e:
            print(f"❌ Failed to initialize MLOps infrastructure: {e}")
            raise
    
    async def register_model(
        self,
        model: Any,
        name: str,
        version: str,
        framework: str,
        algorithm: str,
        metrics: Dict[str, float],
        hyperparameters: Dict[str, Any],
        training_data_hash: str,
        tags: List[str] = None,
        description: str = "",
        author: str = "system"
    ) -> str:
        """Register a new model version"""
        
        model_id = f"{name}:{version}"
        
        try:
            # Calculate model size
            model_size_mb = await self._calculate_model_size(model)
            
            # Measure inference latency
            inference_latency_ms = await self._measure_inference_latency(model)
            
            # Create model metadata
            metadata = ModelMetadata(
                model_id=model_id,
                name=name,
                version=version,
                framework=framework,
                algorithm=algorithm,
                status=ModelStatus.TRAINING,
                created_at=time.time(),
                updated_at=time.time(),
                metrics=metrics,
                hyperparameters=hyperparameters,
                training_data_hash=training_data_hash,
                model_size_mb=model_size_mb,
                inference_latency_ms=inference_latency_ms,
                accuracy=metrics.get('accuracy', 0.0),
                tags=tags or [],
                description=description,
                author=author
            )
            
            # Register with MLflow
            await self._register_with_mlflow(model, metadata)
            
            # Register with Weights & Biases
            await self._register_with_wandb(metadata)
            
            # Store model artifacts
            await self._store_model_artifacts(model, model_id)
            
            # Update registry
            self.models[model_id] = metadata
            await self._save_registry_state()
            
            print(f"✅ Model {model_id} registered successfully")
            return model_id
            
        except Exception as e:
            print(f"❌ Failed to register model {model_id}: {e}")
            raise
    
    async def promote_model(self, model_id: str, target_status: ModelStatus) -> bool:
        """Promote model to different deployment stage"""
        
        if model_id not in self.models:
            raise ValueError(f"Model {model_id} not found")
        
        model = self.models[model_id]
        
        try:
            # Validate promotion
            if not await self._validate_promotion(model, target_status):
                return False
            
            # Update model status
            model.status = target_status
            model.updated_at = time.time()
            
            # Update MLflow model stage
            await self._update_mlflow_stage(model_id, target_status)
            
            # Update deployment configuration
            if target_status == ModelStatus.PRODUCTION:
                await self._deploy_to_production(model_id)
            
            # Save state
            await self._save_registry_state()
            
            print(f"✅ Model {model_id} promoted to {target_status.value}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to promote model {model_id}: {e}")
            return False
    
    async def create_ab_test(
        self,
        name: str,
        description: str,
        models: List[str],
        traffic_split: Dict[str, float],
        success_metrics: List[str],
        duration_days: int = 7,
        min_sample_size: int = 1000,
        confidence_level: float = 0.95
    ) -> str:
        """Create A/B test experiment"""
        
        experiment_id = f"ab_test_{int(time.time())}"
        
        # Validate models exist
        for model_id in models:
            if model_id not in self.models:
                raise ValueError(f"Model {model_id} not found")
        
        # Validate traffic split
        if abs(sum(traffic_split.values()) - 1.0) > 0.01:
            raise ValueError("Traffic split must sum to 1.0")
        
        try:
            config = ABTestConfig(
                experiment_id=experiment_id,
                name=name,
                description=description,
                models=models,
                traffic_split=traffic_split,
                success_metrics=success_metrics,
                duration_days=duration_days,
                min_sample_size=min_sample_size,
                confidence_level=confidence_level,
                created_at=time.time(),
                status="active"
            )
            
            # Store experiment configuration
            self.experiments[experiment_id] = config
            
            # Configure traffic routing
            await self._configure_traffic_routing(experiment_id, config)
            
            # Start experiment tracking
            await self._start_experiment_tracking(experiment_id, config)
            
            await self._save_registry_state()
            
            print(f"✅ A/B test {experiment_id} created successfully")
            return experiment_id
            
        except Exception as e:
            print(f"❌ Failed to create A/B test: {e}")
            raise
    
    async def route_request(self, request_id: str, experiment_id: str = None) -> str:
        """Route request to appropriate model based on A/B test configuration"""
        
        if experiment_id and experiment_id in self.experiments:
            config = self.experiments[experiment_id]
            
            # Determine model based on traffic split
            model_id = await self._select_model_for_request(request_id, config)
            
            # Track routing decision
            self.traffic_routing[request_id] = model_id
            
            # Store in Redis for fast lookup
            await self._cache_routing_decision(request_id, model_id, experiment_id)
            
            return model_id
        
        # Default to production model
        production_models = [
            model_id for model_id, model in self.models.items()
            if model.status == ModelStatus.PRODUCTION
        ]
        
        return production_models[0] if production_models else None
    
    async def record_prediction(
        self,
        request_id: str,
        model_id: str,
        prediction: Any,
        latency_ms: float,
        features: Dict[str, Any] = None
    ):
        """Record prediction for monitoring and A/B testing"""
        
        try:
            # Create prediction record
            record = {
                'request_id': request_id,
                'model_id': model_id,
                'prediction': prediction,
                'latency_ms': latency_ms,
                'timestamp': time.time(),
                'features': features or {}
            }
            
            # Store in performance tracking
            if model_id not in self.model_performance:
                self.model_performance[model_id] = []
            
            self.model_performance[model_id].append(record)
            
            # Update real-time metrics
            await self._update_realtime_metrics(model_id, record)
            
            # Store in Redis for fast access
            await self._cache_prediction_record(request_id, record)
            
        except Exception as e:
            print(f"❌ Failed to record prediction: {e}")
    
    async def get_model_performance(self, model_id: str, hours: int = 24) -> Dict[str, Any]:
        """Get model performance metrics"""
        
        if model_id not in self.model_performance:
            return {}
        
        cutoff_time = time.time() - (hours * 3600)
        recent_records = [
            record for record in self.model_performance[model_id]
            if record['timestamp'] > cutoff_time
        ]
        
        if not recent_records:
            return {}
        
        # Calculate metrics
        latencies = [record['latency_ms'] for record in recent_records]
        
        return {
            'total_requests': len(recent_records),
            'avg_latency_ms': np.mean(latencies),
            'p95_latency_ms': np.percentile(latencies, 95),
            'p99_latency_ms': np.percentile(latencies, 99),
            'error_rate': 0.0,  # Calculate based on error tracking
            'throughput_rps': len(recent_records) / (hours * 3600)
        }
    
    async def get_experiment_results(self, experiment_id: str) -> Dict[str, Any]:
        """Get A/B test experiment results"""
        
        if experiment_id not in self.experiments:
            return {}
        
        config = self.experiments[experiment_id]
        results = {}
        
        for model_id in config.models:
            performance = await self.get_model_performance(model_id)
            results[model_id] = performance
        
        # Calculate statistical significance
        significance = await self._calculate_statistical_significance(experiment_id)
        
        return {
            'experiment_id': experiment_id,
            'config': asdict(config),
            'model_performance': results,
            'statistical_significance': significance,
            'recommendation': await self._generate_experiment_recommendation(experiment_id)
        }
    
    # Private helper methods
    async def _calculate_model_size(self, model: Any) -> float:
        """Calculate model size in MB"""
        # Implementation depends on model type
        return 10.0  # Mock value
    
    async def _measure_inference_latency(self, model: Any) -> float:
        """Measure model inference latency"""
        # Implementation depends on model type
        return 50.0  # Mock value in ms
    
    async def _register_with_mlflow(self, model: Any, metadata: ModelMetadata):
        """Register model with MLflow"""
        if not self.mlflow_client:
            return
        
        with mlflow.start_run():
            # Log metrics
            for key, value in metadata.metrics.items():
                mlflow.log_metric(key, value)
            
            # Log hyperparameters
            for key, value in metadata.hyperparameters.items():
                mlflow.log_param(key, value)
            
            # Log model
            mlflow.sklearn.log_model(
                model,
                metadata.name,
                registered_model_name=metadata.name
            )
    
    async def _register_with_wandb(self, metadata: ModelMetadata):
        """Register model with Weights & Biases"""
        if not self.wandb_client:
            return
        
        # Log to wandb
        wandb.log({
            'model_id': metadata.model_id,
            'accuracy': metadata.accuracy,
            'model_size_mb': metadata.model_size_mb,
            'inference_latency_ms': metadata.inference_latency_ms
        })
    
    async def _store_model_artifacts(self, model: Any, model_id: str):
        """Store model artifacts in S3"""
        # Implementation for storing model files
        pass
    
    async def _validate_promotion(self, model: ModelMetadata, target_status: ModelStatus) -> bool:
        """Validate model promotion"""
        # Implement validation logic
        return True
    
    async def _update_mlflow_stage(self, model_id: str, status: ModelStatus):
        """Update MLflow model stage"""
        # Implementation for MLflow stage updates
        pass
    
    async def _deploy_to_production(self, model_id: str):
        """Deploy model to production"""
        # Implementation for production deployment
        pass
    
    async def _configure_traffic_routing(self, experiment_id: str, config: ABTestConfig):
        """Configure traffic routing for A/B test"""
        # Implementation for traffic routing
        pass
    
    async def _start_experiment_tracking(self, experiment_id: str, config: ABTestConfig):
        """Start tracking A/B test experiment"""
        # Implementation for experiment tracking
        pass
    
    async def _select_model_for_request(self, request_id: str, config: ABTestConfig) -> str:
        """Select model for request based on traffic split"""
        # Hash request ID to ensure consistent routing
        hash_value = int(hashlib.md5(request_id.encode()).hexdigest(), 16)
        normalized_hash = (hash_value % 10000) / 10000.0
        
        cumulative_probability = 0.0
        for model_id, probability in config.traffic_split.items():
            cumulative_probability += probability
            if normalized_hash <= cumulative_probability:
                return model_id
        
        return config.models[0]  # Fallback
    
    async def _cache_routing_decision(self, request_id: str, model_id: str, experiment_id: str):
        """Cache routing decision in Redis"""
        if self.redis_client:
            self.redis_client.setex(
                f"routing:{request_id}",
                3600,  # 1 hour TTL
                json.dumps({'model_id': model_id, 'experiment_id': experiment_id})
            )
    
    async def _update_realtime_metrics(self, model_id: str, record: Dict):
        """Update real-time metrics"""
        # Implementation for real-time metrics updates
        pass
    
    async def _cache_prediction_record(self, request_id: str, record: Dict):
        """Cache prediction record"""
        if self.redis_client:
            self.redis_client.setex(
                f"prediction:{request_id}",
                86400,  # 24 hours TTL
                json.dumps(record, default=str)
            )
    
    async def _calculate_statistical_significance(self, experiment_id: str) -> Dict[str, float]:
        """Calculate statistical significance of A/B test"""
        # Implementation for statistical analysis
        return {'p_value': 0.05, 'confidence': 0.95}
    
    async def _generate_experiment_recommendation(self, experiment_id: str) -> str:
        """Generate recommendation based on experiment results"""
        # Implementation for generating recommendations
        return "Continue experiment for more data"
    
    async def _load_registry_state(self):
        """Load registry state from persistent storage"""
        # Implementation for loading state
        pass
    
    async def _save_registry_state(self):
        """Save registry state to persistent storage"""
        # Implementation for saving state
        pass
