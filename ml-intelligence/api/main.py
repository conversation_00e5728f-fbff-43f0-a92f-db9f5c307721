"""
CVLeap ML Intelligence API Gateway
Production-ready FastAPI server with <150ms p95 latency
"""

import asyncio
import time
from contextlib import asynccontextmanager
from typing import Dict, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from prometheus_client import Counter, Histogram, generate_latest, CONTENT_TYPE_LATEST
from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import JaegerExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor

from api.middleware.auth import AuthMiddleware
from api.middleware.rate_limiter import RateLimiterMiddleware
from api.middleware.monitoring import MonitoringMiddleware
from api.models.resume_improvement import ResumeImprovementService
from api.models.ats_scoring import ATSScoringService
from api.models.job_ranking import JobRankingService
from api.models.form_completion import FormCompletionService
from api.schemas.requests import (
    ResumeImprovementRequest,
    ATSScoringRequest,
    JobRankingRequest,
    FormCompletionRequest,
)
from api.schemas.responses import (
    ResumeImprovementResponse,
    ATSScoringResponse,
    JobRankingResponse,
    FormCompletionResponse,
    HealthResponse,
)
from api.utils.cache import CacheManager
from api.utils.config import Settings

# Metrics
REQUEST_COUNT = Counter('cvleap_ml_requests_total', 'Total ML API requests', ['method', 'endpoint', 'status'])
REQUEST_DURATION = Histogram('cvleap_ml_request_duration_seconds', 'Request duration', ['endpoint'])
MODEL_INFERENCE_TIME = Histogram('cvleap_ml_inference_duration_seconds', 'Model inference time', ['model'])

# Global services
settings = Settings()
cache_manager = CacheManager()
tracer = trace.get_tracer(__name__)

# Service instances
resume_service: Optional[ResumeImprovementService] = None
ats_service: Optional[ATSScoringService] = None
job_ranking_service: Optional[JobRankingService] = None
form_completion_service: Optional[FormCompletionService] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    # Startup
    print("🚀 Starting CVLeap ML Intelligence API...")
    
    # Initialize OpenTelemetry
    trace.set_tracer_provider(TracerProvider())
    jaeger_exporter = JaegerExporter(
        agent_host_name=settings.jaeger_host,
        agent_port=settings.jaeger_port,
    )
    span_processor = BatchSpanProcessor(jaeger_exporter)
    trace.get_tracer_provider().add_span_processor(span_processor)
    
    # Initialize services
    global resume_service, ats_service, job_ranking_service, form_completion_service
    
    print("📚 Loading ML models...")
    resume_service = ResumeImprovementService()
    ats_service = ATSScoringService()
    job_ranking_service = JobRankingService()
    form_completion_service = FormCompletionService()
    
    await asyncio.gather(
        resume_service.initialize(),
        ats_service.initialize(),
        job_ranking_service.initialize(),
        form_completion_service.initialize(),
    )
    
    print("✅ All services initialized successfully")
    
    yield
    
    # Shutdown
    print("🛑 Shutting down CVLeap ML Intelligence API...")
    await asyncio.gather(
        resume_service.cleanup(),
        ats_service.cleanup(),
        job_ranking_service.cleanup(),
        form_completion_service.cleanup(),
    )
    print("✅ Cleanup completed")


# Create FastAPI app
app = FastAPI(
    title="CVLeap ML Intelligence API",
    description="Production ML services for résumé optimization, ATS scoring, job ranking, and form completion",
    version="1.0.0",
    docs_url="/docs" if settings.environment != "production" else None,
    redoc_url="/redoc" if settings.environment != "production" else None,
    lifespan=lifespan,
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(AuthMiddleware)
app.add_middleware(RateLimiterMiddleware)
app.add_middleware(MonitoringMiddleware)

# Instrument with OpenTelemetry
FastAPIInstrumentor.instrument_app(app)


@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Add processing time header and metrics"""
    start_time = time.time()
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    
    # Record metrics
    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).inc()
    
    REQUEST_DURATION.labels(endpoint=request.url.path).observe(process_time)
    
    return response


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler with structured logging"""
    import traceback
    
    error_id = f"error_{int(time.time() * 1000)}"
    
    # Log error with context
    print(f"❌ Error {error_id}: {str(exc)}")
    print(f"📍 Path: {request.url.path}")
    print(f"🔍 Traceback: {traceback.format_exc()}")
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "error_id": error_id,
            "message": "An unexpected error occurred. Please try again later.",
        }
    )


# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint for load balancers"""
    return HealthResponse(
        status="healthy",
        timestamp=int(time.time()),
        services={
            "resume_improvement": resume_service.is_healthy() if resume_service else False,
            "ats_scoring": ats_service.is_healthy() if ats_service else False,
            "job_ranking": job_ranking_service.is_healthy() if job_ranking_service else False,
            "form_completion": form_completion_service.is_healthy() if form_completion_service else False,
        }
    )


# Metrics endpoint
@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)


# Résumé Improvement Endpoints
@app.post("/v1/resume/improve", response_model=ResumeImprovementResponse)
async def improve_resume(
    request: ResumeImprovementRequest,
    background_tasks: BackgroundTasks,
):
    """Improve résumé content using GPT-4o or DeepSeek-Coder"""
    with tracer.start_as_current_span("resume_improvement") as span:
        span.set_attribute("content_length", len(request.content))
        span.set_attribute("target_role", request.target_role or "general")
        
        # Check cache first
        cache_key = f"resume_improve:{hash(request.content)}:{request.target_role}"
        cached_result = await cache_manager.get(cache_key)
        if cached_result:
            span.set_attribute("cache_hit", True)
            return ResumeImprovementResponse.parse_obj(cached_result)
        
        # Perform inference
        start_time = time.time()
        result = await resume_service.improve_resume(request)
        inference_time = time.time() - start_time
        
        MODEL_INFERENCE_TIME.labels(model="resume_improvement").observe(inference_time)
        span.set_attribute("inference_time", inference_time)
        
        # Cache result
        background_tasks.add_task(
            cache_manager.set,
            cache_key,
            result.dict(),
            ttl=3600  # 1 hour
        )
        
        return result


@app.post("/v1/resume/ats-score", response_model=ATSScoringResponse)
async def score_ats_compatibility(
    request: ATSScoringRequest,
    background_tasks: BackgroundTasks,
):
    """Score ATS compatibility using BERT-based semantic analysis"""
    with tracer.start_as_current_span("ats_scoring") as span:
        span.set_attribute("resume_length", len(request.resume_text))
        span.set_attribute("job_description_length", len(request.job_description))
        span.set_attribute("ats_system", request.ats_system or "generic")
        
        # Check cache
        cache_key = f"ats_score:{hash(request.resume_text)}:{hash(request.job_description)}"
        cached_result = await cache_manager.get(cache_key)
        if cached_result:
            span.set_attribute("cache_hit", True)
            return ATSScoringResponse.parse_obj(cached_result)
        
        # Perform inference
        start_time = time.time()
        result = await ats_service.score_compatibility(request)
        inference_time = time.time() - start_time
        
        MODEL_INFERENCE_TIME.labels(model="ats_scoring").observe(inference_time)
        span.set_attribute("inference_time", inference_time)
        span.set_attribute("ats_score", result.overall_score)
        
        # Cache result
        background_tasks.add_task(
            cache_manager.set,
            cache_key,
            result.dict(),
            ttl=1800  # 30 minutes
        )
        
        return result


@app.post("/v1/jobs/rank", response_model=JobRankingResponse)
async def rank_jobs(
    request: JobRankingRequest,
    background_tasks: BackgroundTasks,
):
    """Rank jobs by interview propensity using gradient boosting"""
    with tracer.start_as_current_span("job_ranking") as span:
        span.set_attribute("resume_id", request.resume_id)
        span.set_attribute("num_jobs", len(request.job_ids))
        
        # Check cache
        cache_key = f"job_rank:{request.resume_id}:{hash(str(sorted(request.job_ids)))}"
        cached_result = await cache_manager.get(cache_key)
        if cached_result:
            span.set_attribute("cache_hit", True)
            return JobRankingResponse.parse_obj(cached_result)
        
        # Perform inference
        start_time = time.time()
        result = await job_ranking_service.rank_jobs(request)
        inference_time = time.time() - start_time
        
        MODEL_INFERENCE_TIME.labels(model="job_ranking").observe(inference_time)
        span.set_attribute("inference_time", inference_time)
        
        # Cache result
        background_tasks.add_task(
            cache_manager.set,
            cache_key,
            result.dict(),
            ttl=600  # 10 minutes
        )
        
        return result


@app.post("/v1/forms/autocomplete", response_model=FormCompletionResponse)
async def autocomplete_form(
    request: FormCompletionRequest,
    background_tasks: BackgroundTasks,
):
    """Provide form autocompletion hints for sandbox automation"""
    with tracer.start_as_current_span("form_completion") as span:
        span.set_attribute("num_fields", len(request.form_fields))
        span.set_attribute("context_length", len(request.resume_context))
        
        # Check cache
        cache_key = f"form_complete:{hash(str(request.form_fields))}:{hash(request.resume_context)}"
        cached_result = await cache_manager.get(cache_key)
        if cached_result:
            span.set_attribute("cache_hit", True)
            return FormCompletionResponse.parse_obj(cached_result)
        
        # Perform inference
        start_time = time.time()
        result = await form_completion_service.complete_form(request)
        inference_time = time.time() - start_time
        
        MODEL_INFERENCE_TIME.labels(model="form_completion").observe(inference_time)
        span.set_attribute("inference_time", inference_time)
        
        # Cache result
        background_tasks.add_task(
            cache_manager.set,
            cache_key,
            result.dict(),
            ttl=300  # 5 minutes
        )
        
        return result


# Batch processing endpoints for efficiency
@app.post("/v1/batch/ats-score")
async def batch_ats_scoring(requests: List[ATSScoringRequest]):
    """Batch ATS scoring for improved throughput"""
    results = await ats_service.batch_score_compatibility(requests)
    return {"results": results}


@app.post("/v1/batch/job-rank")
async def batch_job_ranking(requests: List[JobRankingRequest]):
    """Batch job ranking for improved throughput"""
    results = await job_ranking_service.batch_rank_jobs(requests)
    return {"results": results}


if __name__ == "__main__":
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.environment == "development",
        workers=1 if settings.environment == "development" else 4,
        loop="uvloop",
        http="httptools",
        access_log=settings.environment == "development",
    )
