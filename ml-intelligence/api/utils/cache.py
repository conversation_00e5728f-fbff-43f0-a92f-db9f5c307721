"""
High-performance caching system for ML inference
Redis-based caching with compression and TTL management
"""

import asyncio
import json
import pickle
import gzip
import hashlib
from typing import Any, Optional, Union, Dict
import time

import redis.asyncio as redis
from pydantic import BaseModel

from api.utils.config import Settings


class CacheStats(BaseModel):
    """Cache statistics model"""
    hits: int = 0
    misses: int = 0
    hit_rate: float = 0.0
    total_requests: int = 0
    avg_response_time_ms: float = 0.0


class CacheManager:
    """High-performance cache manager with Redis backend"""
    
    def __init__(self):
        self.settings = Settings()
        self.redis_client: Optional[redis.Redis] = None
        self.stats = CacheStats()
        self.compression_threshold = 1024  # Compress values > 1KB
        self.default_ttl = 3600  # 1 hour default TTL
        
        # Cache key prefixes for different services
        self.key_prefixes = {
            "resume_improvement": "ri:",
            "ats_scoring": "ats:",
            "job_ranking": "jr:",
            "form_completion": "fc:",
            "embeddings": "emb:",
            "features": "feat:"
        }
    
    async def initialize(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.Redis(
                host=self.settings.redis_host,
                port=self.settings.redis_port,
                password=self.settings.redis_password,
                db=self.settings.redis_db,
                decode_responses=False,  # We handle encoding ourselves
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test connection
            await self.redis_client.ping()
            print("✅ Redis cache connection established")
            
        except Exception as e:
            print(f"⚠️ Redis connection failed: {e}")
            self.redis_client = None
    
    async def get(self, key: str, service: str = "default") -> Optional[Any]:
        """Get value from cache with automatic decompression"""
        if not self.redis_client:
            return None
        
        start_time = time.time()
        
        try:
            # Add service prefix to key
            cache_key = self._build_cache_key(key, service)
            
            # Get from Redis
            cached_data = await self.redis_client.get(cache_key)
            
            if cached_data is None:
                self.stats.misses += 1
                return None
            
            # Decompress if needed
            if cached_data.startswith(b'GZIP:'):
                cached_data = gzip.decompress(cached_data[5:])
            
            # Deserialize
            try:
                # Try JSON first (faster)
                result = json.loads(cached_data.decode('utf-8'))
            except (json.JSONDecodeError, UnicodeDecodeError):
                # Fallback to pickle
                result = pickle.loads(cached_data)
            
            # Update stats
            self.stats.hits += 1
            response_time = (time.time() - start_time) * 1000
            self._update_response_time(response_time)
            
            return result
            
        except Exception as e:
            print(f"Cache get error for key {key}: {e}")
            self.stats.misses += 1
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None, 
        service: str = "default",
        compress: bool = True
    ) -> bool:
        """Set value in cache with automatic compression"""
        if not self.redis_client:
            return False
        
        try:
            # Add service prefix to key
            cache_key = self._build_cache_key(key, service)
            
            # Serialize value
            try:
                # Try JSON first (faster and more compatible)
                serialized_data = json.dumps(value, default=str).encode('utf-8')
            except (TypeError, ValueError):
                # Fallback to pickle for complex objects
                serialized_data = pickle.dumps(value)
            
            # Compress if data is large enough and compression is enabled
            if compress and len(serialized_data) > self.compression_threshold:
                compressed_data = gzip.compress(serialized_data)
                # Only use compression if it actually reduces size
                if len(compressed_data) < len(serialized_data):
                    serialized_data = b'GZIP:' + compressed_data
            
            # Set TTL
            ttl = ttl or self.default_ttl
            
            # Store in Redis
            await self.redis_client.setex(cache_key, ttl, serialized_data)
            
            return True
            
        except Exception as e:
            print(f"Cache set error for key {key}: {e}")
            return False
    
    async def delete(self, key: str, service: str = "default") -> bool:
        """Delete key from cache"""
        if not self.redis_client:
            return False
        
        try:
            cache_key = self._build_cache_key(key, service)
            result = await self.redis_client.delete(cache_key)
            return result > 0
            
        except Exception as e:
            print(f"Cache delete error for key {key}: {e}")
            return False
    
    async def exists(self, key: str, service: str = "default") -> bool:
        """Check if key exists in cache"""
        if not self.redis_client:
            return False
        
        try:
            cache_key = self._build_cache_key(key, service)
            result = await self.redis_client.exists(cache_key)
            return result > 0
            
        except Exception as e:
            print(f"Cache exists error for key {key}: {e}")
            return False
    
    async def get_many(self, keys: list, service: str = "default") -> Dict[str, Any]:
        """Get multiple values from cache"""
        if not self.redis_client or not keys:
            return {}
        
        try:
            # Build cache keys
            cache_keys = [self._build_cache_key(key, service) for key in keys]
            
            # Get all values
            cached_values = await self.redis_client.mget(cache_keys)
            
            result = {}
            for i, (original_key, cached_data) in enumerate(zip(keys, cached_values)):
                if cached_data is not None:
                    try:
                        # Decompress if needed
                        if cached_data.startswith(b'GZIP:'):
                            cached_data = gzip.decompress(cached_data[5:])
                        
                        # Deserialize
                        try:
                            value = json.loads(cached_data.decode('utf-8'))
                        except (json.JSONDecodeError, UnicodeDecodeError):
                            value = pickle.loads(cached_data)
                        
                        result[original_key] = value
                        self.stats.hits += 1
                    except Exception as e:
                        print(f"Error deserializing cached value for key {original_key}: {e}")
                        self.stats.misses += 1
                else:
                    self.stats.misses += 1
            
            return result
            
        except Exception as e:
            print(f"Cache get_many error: {e}")
            return {}
    
    async def set_many(
        self, 
        key_value_pairs: Dict[str, Any], 
        ttl: Optional[int] = None, 
        service: str = "default"
    ) -> bool:
        """Set multiple values in cache"""
        if not self.redis_client or not key_value_pairs:
            return False
        
        try:
            # Prepare pipeline for batch operations
            pipe = self.redis_client.pipeline()
            
            ttl = ttl or self.default_ttl
            
            for key, value in key_value_pairs.items():
                cache_key = self._build_cache_key(key, service)
                
                # Serialize value
                try:
                    serialized_data = json.dumps(value, default=str).encode('utf-8')
                except (TypeError, ValueError):
                    serialized_data = pickle.dumps(value)
                
                # Compress if needed
                if len(serialized_data) > self.compression_threshold:
                    compressed_data = gzip.compress(serialized_data)
                    if len(compressed_data) < len(serialized_data):
                        serialized_data = b'GZIP:' + compressed_data
                
                pipe.setex(cache_key, ttl, serialized_data)
            
            # Execute pipeline
            await pipe.execute()
            return True
            
        except Exception as e:
            print(f"Cache set_many error: {e}")
            return False
    
    async def clear_service_cache(self, service: str) -> int:
        """Clear all cache entries for a specific service"""
        if not self.redis_client:
            return 0
        
        try:
            prefix = self.key_prefixes.get(service, f"{service}:")
            pattern = f"{prefix}*"
            
            # Get all keys matching pattern
            keys = []
            async for key in self.redis_client.scan_iter(match=pattern):
                keys.append(key)
            
            if keys:
                # Delete all keys
                deleted_count = await self.redis_client.delete(*keys)
                return deleted_count
            
            return 0
            
        except Exception as e:
            print(f"Error clearing cache for service {service}: {e}")
            return 0
    
    async def get_cache_info(self) -> Dict[str, Any]:
        """Get cache information and statistics"""
        if not self.redis_client:
            return {"status": "disconnected"}
        
        try:
            # Get Redis info
            redis_info = await self.redis_client.info()
            
            # Calculate hit rate
            total_requests = self.stats.hits + self.stats.misses
            hit_rate = (self.stats.hits / total_requests) if total_requests > 0 else 0.0
            
            return {
                "status": "connected",
                "redis_version": redis_info.get("redis_version", "unknown"),
                "used_memory": redis_info.get("used_memory_human", "unknown"),
                "connected_clients": redis_info.get("connected_clients", 0),
                "total_commands_processed": redis_info.get("total_commands_processed", 0),
                "cache_stats": {
                    "hits": self.stats.hits,
                    "misses": self.stats.misses,
                    "hit_rate": hit_rate,
                    "total_requests": total_requests,
                    "avg_response_time_ms": self.stats.avg_response_time_ms
                }
            }
            
        except Exception as e:
            print(f"Error getting cache info: {e}")
            return {"status": "error", "error": str(e)}
    
    def _build_cache_key(self, key: str, service: str) -> str:
        """Build cache key with service prefix"""
        prefix = self.key_prefixes.get(service, f"{service}:")
        return f"{prefix}{key}"
    
    def _update_response_time(self, response_time_ms: float):
        """Update average response time"""
        total_requests = self.stats.hits + self.stats.misses
        if total_requests == 1:
            self.stats.avg_response_time_ms = response_time_ms
        else:
            # Exponential moving average
            alpha = 0.1
            self.stats.avg_response_time_ms = (
                alpha * response_time_ms + 
                (1 - alpha) * self.stats.avg_response_time_ms
            )
    
    async def health_check(self) -> bool:
        """Check if cache is healthy"""
        if not self.redis_client:
            return False
        
        try:
            await self.redis_client.ping()
            return True
        except Exception:
            return False
    
    async def cleanup(self):
        """Cleanup cache connections"""
        if self.redis_client:
            await self.redis_client.close()
            print("✅ Cache connections closed")


# Utility functions for cache key generation
def generate_cache_key(*args, **kwargs) -> str:
    """Generate a consistent cache key from arguments"""
    # Create a string representation of all arguments
    key_parts = []
    
    # Add positional arguments
    for arg in args:
        if isinstance(arg, (str, int, float, bool)):
            key_parts.append(str(arg))
        else:
            # For complex objects, use their hash
            key_parts.append(str(hash(str(arg))))
    
    # Add keyword arguments (sorted for consistency)
    for key, value in sorted(kwargs.items()):
        if isinstance(value, (str, int, float, bool)):
            key_parts.append(f"{key}:{value}")
        else:
            key_parts.append(f"{key}:{hash(str(value))}")
    
    # Create hash of the combined key
    combined_key = "|".join(key_parts)
    return hashlib.md5(combined_key.encode()).hexdigest()


def cache_key_for_request(request_data: Any, model_version: str = "v1.0") -> str:
    """Generate cache key for ML request"""
    if hasattr(request_data, 'dict'):
        # Pydantic model
        data_dict = request_data.dict()
    elif isinstance(request_data, dict):
        data_dict = request_data
    else:
        data_dict = {"data": str(request_data)}
    
    # Add model version to ensure cache invalidation on model updates
    data_dict["_model_version"] = model_version
    
    # Create deterministic hash
    data_str = json.dumps(data_dict, sort_keys=True, default=str)
    return hashlib.sha256(data_str.encode()).hexdigest()[:16]  # Use first 16 chars


# Decorator for automatic caching
def cached_inference(service: str, ttl: int = 3600):
    """Decorator for automatic caching of inference results"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Get cache manager from first argument (should be service instance)
            if args and hasattr(args[0], 'cache_manager'):
                cache_manager = args[0].cache_manager
                
                # Generate cache key
                cache_key = generate_cache_key(*args[1:], **kwargs)
                
                # Try to get from cache
                cached_result = await cache_manager.get(cache_key, service)
                if cached_result is not None:
                    return cached_result
                
                # Execute function
                result = await func(*args, **kwargs)
                
                # Cache result
                await cache_manager.set(cache_key, result, ttl, service)
                
                return result
            else:
                # No cache manager available, execute function directly
                return await func(*args, **kwargs)
        
        return wrapper
    return decorator
