"""
Interview Propensity Ranking Service
Gradient-boosted model for job ranking based on historical interview outcomes
"""

import asyncio
import time
import pickle
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import numpy as np
import pandas as pd

import xgboost as xgb
import lightgbm as lgb
from sklearn.ensemble import <PERSON>rad<PERSON><PERSON><PERSON>tingRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_squared_error, r2_score
import joblib

from api.schemas.requests import InterviewRankingRequest
from api.schemas.responses import (
    InterviewRankingResponse,
    JobRanking,
    RankingFactors,
    ConfidenceLevel
)
from api.utils.config import Settings
from api.utils.feature_engineering import FeatureEngineer


@dataclass
class ModelMetrics:
    """Model performance metrics"""
    rmse: float
    r2_score: float
    mae: float
    accuracy_at_k: Dict[int, float]  # Accuracy at top-k predictions


class InterviewRankingService:
    """Service for ranking job opportunities based on interview propensity"""
    
    def __init__(self):
        self.settings = Settings()
        self.feature_engineer = FeatureEngineer()
        
        # Model components
        self.xgb_model = None
        self.lgb_model = None
        self.ensemble_model = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        
        # Feature importance tracking
        self.feature_importance = {}
        self.feature_names = []
        
        # Model performance
        self.model_metrics = None
        self.last_training_time = None
        
        # Service state
        self.is_initialized = False
        self.models_loaded = False
    
    async def initialize(self):
        """Initialize the interview ranking service"""
        print("🚀 Initializing Interview Ranking Service...")
        
        try:
            # Load pre-trained models if available
            await self._load_models()
            
            # Initialize feature engineering
            await self.feature_engineer.initialize()
            
            # Load historical data for training if models not available
            if not self.models_loaded:
                await self._train_models()
            
            self.is_initialized = True
            print("✅ Interview Ranking Service initialized")
            
        except Exception as e:
            print(f"❌ Failed to initialize Interview Ranking Service: {e}")
            raise
    
    async def rank_jobs(self, request: InterviewRankingRequest) -> InterviewRankingResponse:
        """Rank job opportunities based on interview propensity"""
        if not self.is_initialized:
            raise RuntimeError("Service not initialized")
        
        start_time = time.time()
        
        try:
            # Extract features for each job
            job_features = []
            job_ids = []
            
            for job in request.jobs:
                features = await self._extract_job_features(
                    job, 
                    request.resume_data,
                    request.user_profile
                )
                job_features.append(features)
                job_ids.append(job.job_id)
            
            # Convert to DataFrame for processing
            features_df = pd.DataFrame(job_features)
            
            # Predict interview propensity scores
            scores = await self._predict_scores(features_df)
            
            # Generate rankings
            rankings = await self._generate_rankings(
                job_ids, 
                scores, 
                features_df,
                request.jobs
            )
            
            # Calculate confidence levels
            confidence_scores = await self._calculate_confidence(scores, features_df)
            
            processing_time = time.time() - start_time
            
            return InterviewRankingResponse(
                rankings=rankings,
                total_jobs=len(request.jobs),
                processing_time_ms=int(processing_time * 1000),
                model_version="v2.1.0",
                confidence_threshold=0.7,
                ranking_factors=await self._get_ranking_factors()
            )
            
        except Exception as e:
            print(f"❌ Error in job ranking: {e}")
            raise
    
    async def _extract_job_features(
        self, 
        job: Any, 
        resume_data: Dict, 
        user_profile: Dict
    ) -> Dict[str, float]:
        """Extract features for a single job opportunity"""
        
        features = {}
        
        # Job characteristics
        features['job_level'] = self._encode_job_level(job.get('level', 'mid'))
        features['company_size'] = self._encode_company_size(job.get('company_size', 'medium'))
        features['industry_match'] = self._calculate_industry_match(
            job.get('industry'), 
            resume_data.get('industries', [])
        )
        features['location_preference'] = self._calculate_location_preference(
            job.get('location'), 
            user_profile.get('preferred_locations', [])
        )
        features['remote_option'] = 1.0 if job.get('remote_friendly', False) else 0.0
        
        # Skill matching
        job_skills = set(job.get('required_skills', []))
        resume_skills = set(resume_data.get('skills', []))
        
        features['skill_match_ratio'] = len(job_skills & resume_skills) / max(len(job_skills), 1)
        features['skill_overlap_count'] = len(job_skills & resume_skills)
        features['missing_skills_count'] = len(job_skills - resume_skills)
        
        # Experience matching
        features['experience_match'] = self._calculate_experience_match(
            job.get('experience_required', 0),
            resume_data.get('total_experience', 0)
        )
        
        # Education matching
        features['education_match'] = self._calculate_education_match(
            job.get('education_required'),
            resume_data.get('education_level')
        )
        
        # Salary expectations
        features['salary_match'] = self._calculate_salary_match(
            job.get('salary_range'),
            user_profile.get('salary_expectation')
        )
        
        # Company attractiveness
        features['company_rating'] = job.get('company_rating', 3.5) / 5.0
        features['company_growth'] = job.get('company_growth_rate', 0.1)
        
        # Job posting characteristics
        features['posting_age_days'] = job.get('posting_age_days', 7)
        features['application_count'] = min(job.get('application_count', 50), 1000) / 1000
        features['job_description_length'] = min(len(job.get('description', '')), 5000) / 5000
        
        # Historical success factors
        features['similar_job_success_rate'] = await self._get_similar_job_success_rate(job)
        features['company_interview_rate'] = await self._get_company_interview_rate(job.get('company_id'))
        
        return features
    
    async def _predict_scores(self, features_df: pd.DataFrame) -> np.ndarray:
        """Predict interview propensity scores using ensemble model"""
        
        # Preprocess features
        features_scaled = self.scaler.transform(features_df)
        
        # Get predictions from each model
        xgb_pred = self.xgb_model.predict(features_scaled) if self.xgb_model else np.zeros(len(features_df))
        lgb_pred = self.lgb_model.predict(features_scaled) if self.lgb_model else np.zeros(len(features_df))
        
        # Ensemble prediction (weighted average)
        ensemble_weights = [0.6, 0.4]  # XGBoost gets higher weight
        scores = ensemble_weights[0] * xgb_pred + ensemble_weights[1] * lgb_pred
        
        # Apply sigmoid to get probabilities
        scores = 1 / (1 + np.exp(-scores))
        
        return scores
    
    async def _generate_rankings(
        self, 
        job_ids: List[str], 
        scores: np.ndarray,
        features_df: pd.DataFrame,
        jobs: List[Any]
    ) -> List[JobRanking]:
        """Generate ranked list of job opportunities"""
        
        rankings = []
        
        # Sort by score (descending)
        sorted_indices = np.argsort(scores)[::-1]
        
        for rank, idx in enumerate(sorted_indices, 1):
            job_id = job_ids[idx]
            score = float(scores[idx])
            job = jobs[idx]
            
            # Calculate ranking factors
            factors = await self._calculate_ranking_factors(features_df.iloc[idx], job)
            
            # Determine confidence level
            confidence = self._get_confidence_level(score, features_df.iloc[idx])
            
            ranking = JobRanking(
                job_id=job_id,
                rank=rank,
                score=score,
                confidence=confidence,
                factors=factors,
                recommendation=self._generate_recommendation(score, factors)
            )
            
            rankings.append(ranking)
        
        return rankings
    
    async def _calculate_ranking_factors(self, features: pd.Series, job: Any) -> RankingFactors:
        """Calculate detailed ranking factors for a job"""
        
        return RankingFactors(
            skill_match=float(features.get('skill_match_ratio', 0)),
            experience_match=float(features.get('experience_match', 0)),
            location_preference=float(features.get('location_preference', 0)),
            company_attractiveness=float(features.get('company_rating', 0)),
            salary_alignment=float(features.get('salary_match', 0)),
            industry_fit=float(features.get('industry_match', 0)),
            growth_potential=float(features.get('company_growth', 0)),
            competition_level=1.0 - float(features.get('application_count', 0))
        )
    
    def _get_confidence_level(self, score: float, features: pd.Series) -> ConfidenceLevel:
        """Determine confidence level based on score and feature quality"""
        
        # Calculate feature completeness
        feature_completeness = (features.notna().sum() / len(features))
        
        # Adjust confidence based on score and feature quality
        if score > 0.8 and feature_completeness > 0.9:
            return ConfidenceLevel.HIGH
        elif score > 0.6 and feature_completeness > 0.7:
            return ConfidenceLevel.MEDIUM
        else:
            return ConfidenceLevel.LOW
    
    def _generate_recommendation(self, score: float, factors: RankingFactors) -> str:
        """Generate personalized recommendation for the job"""
        
        if score > 0.8:
            return "Highly recommended - Strong match across multiple factors"
        elif score > 0.6:
            if factors.skill_match > 0.8:
                return "Good match - Strong skill alignment"
            elif factors.experience_match > 0.8:
                return "Good match - Experience level fits well"
            else:
                return "Moderate match - Consider applying"
        else:
            return "Lower priority - May require skill development"
    
    async def _load_models(self):
        """Load pre-trained models from disk"""
        try:
            model_path = self.settings.model_path / "interview_ranking"
            
            if (model_path / "xgb_model.pkl").exists():
                with open(model_path / "xgb_model.pkl", 'rb') as f:
                    self.xgb_model = pickle.load(f)
                print("✅ XGBoost model loaded")
            
            if (model_path / "lgb_model.pkl").exists():
                with open(model_path / "lgb_model.pkl", 'rb') as f:
                    self.lgb_model = pickle.load(f)
                print("✅ LightGBM model loaded")
            
            if (model_path / "scaler.pkl").exists():
                self.scaler = joblib.load(model_path / "scaler.pkl")
                print("✅ Feature scaler loaded")
            
            if (model_path / "feature_names.pkl").exists():
                with open(model_path / "feature_names.pkl", 'rb') as f:
                    self.feature_names = pickle.load(f)
                print("✅ Feature names loaded")
            
            self.models_loaded = True
            
        except Exception as e:
            print(f"⚠️ Could not load pre-trained models: {e}")
            self.models_loaded = False
    
    async def _train_models(self):
        """Train models on historical data"""
        print("🔄 Training interview ranking models...")
        
        try:
            # Load training data (mock data for demo)
            training_data = await self._load_training_data()
            
            if training_data is None or len(training_data) < 100:
                print("⚠️ Insufficient training data, using default models")
                await self._create_default_models()
                return
            
            # Prepare features and targets
            X = training_data.drop(['interview_success', 'job_id'], axis=1)
            y = training_data['interview_success']
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train XGBoost
            self.xgb_model = xgb.XGBRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            )
            self.xgb_model.fit(X_train_scaled, y_train)
            
            # Train LightGBM
            self.lgb_model = lgb.LGBMRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            )
            self.lgb_model.fit(X_train_scaled, y_train)
            
            # Evaluate models
            await self._evaluate_models(X_test_scaled, y_test)
            
            # Save models
            await self._save_models()
            
            self.models_loaded = True
            self.last_training_time = time.time()
            
            print("✅ Models trained successfully")
            
        except Exception as e:
            print(f"❌ Model training failed: {e}")
            await self._create_default_models()
    
    async def _create_default_models(self):
        """Create simple default models when training data is unavailable"""
        print("🔄 Creating default models...")
        
        # Create simple rule-based models
        self.xgb_model = None  # Will use rule-based scoring
        self.lgb_model = None
        self.models_loaded = True
        
        print("✅ Default models created")
    
    # Utility methods for feature engineering
    def _encode_job_level(self, level: str) -> float:
        level_map = {'entry': 0.2, 'junior': 0.4, 'mid': 0.6, 'senior': 0.8, 'lead': 1.0}
        return level_map.get(level.lower(), 0.6)
    
    def _encode_company_size(self, size: str) -> float:
        size_map = {'startup': 0.2, 'small': 0.4, 'medium': 0.6, 'large': 0.8, 'enterprise': 1.0}
        return size_map.get(size.lower(), 0.6)
    
    def _calculate_industry_match(self, job_industry: str, resume_industries: List[str]) -> float:
        if not job_industry or not resume_industries:
            return 0.5
        return 1.0 if job_industry.lower() in [ind.lower() for ind in resume_industries] else 0.3
    
    def _calculate_location_preference(self, job_location: str, preferred_locations: List[str]) -> float:
        if not job_location or not preferred_locations:
            return 0.5
        return 1.0 if job_location.lower() in [loc.lower() for loc in preferred_locations] else 0.2
    
    def _calculate_experience_match(self, required_exp: int, actual_exp: int) -> float:
        if required_exp == 0:
            return 1.0
        ratio = actual_exp / required_exp
        return min(1.0, max(0.0, 1.0 - abs(1.0 - ratio) * 0.5))
    
    def _calculate_education_match(self, required_edu: str, actual_edu: str) -> float:
        if not required_edu or not actual_edu:
            return 0.7
        
        edu_levels = {'high_school': 1, 'associate': 2, 'bachelor': 3, 'master': 4, 'phd': 5}
        req_level = edu_levels.get(required_edu.lower(), 3)
        actual_level = edu_levels.get(actual_edu.lower(), 3)
        
        return 1.0 if actual_level >= req_level else max(0.3, actual_level / req_level)
    
    def _calculate_salary_match(self, salary_range: Dict, expectation: int) -> float:
        if not salary_range or not expectation:
            return 0.7
        
        min_salary = salary_range.get('min', 0)
        max_salary = salary_range.get('max', 0)
        
        if min_salary <= expectation <= max_salary:
            return 1.0
        elif expectation < min_salary:
            return max(0.3, expectation / min_salary)
        else:
            return max(0.3, max_salary / expectation)
    
    async def _get_similar_job_success_rate(self, job: Any) -> float:
        # Mock implementation - in production, query historical data
        return 0.65
    
    async def _get_company_interview_rate(self, company_id: str) -> float:
        # Mock implementation - in production, query company-specific data
        return 0.45
    
    async def _load_training_data(self) -> Optional[pd.DataFrame]:
        # Mock implementation - in production, load from database
        return None
    
    async def _evaluate_models(self, X_test: np.ndarray, y_test: np.ndarray):
        """Evaluate model performance"""
        # Implementation for model evaluation
        pass
    
    async def _save_models(self):
        """Save trained models to disk"""
        # Implementation for model saving
        pass
    
    async def _calculate_confidence(self, scores: np.ndarray, features_df: pd.DataFrame) -> List[float]:
        """Calculate confidence scores for predictions"""
        return [0.8] * len(scores)  # Mock implementation
    
    async def _get_ranking_factors(self) -> RankingFactors:
        """Get global ranking factors"""
        return RankingFactors(
            skill_match=0.3,
            experience_match=0.25,
            location_preference=0.15,
            company_attractiveness=0.1,
            salary_alignment=0.1,
            industry_fit=0.05,
            growth_potential=0.03,
            competition_level=0.02
        )
