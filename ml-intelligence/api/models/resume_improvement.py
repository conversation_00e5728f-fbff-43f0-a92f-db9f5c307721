"""
Résumé Improvement Service using GPT-4o and DeepSeek-Coder
Production-ready service with fallback models and caching
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any
import re
from dataclasses import dataclass

import openai
import httpx
from transformers import pipeline
import torch
from sentence_transformers import SentenceTransformer

from api.schemas.requests import ResumeImprovementRequest
from api.schemas.responses import (
    ResumeImprovementResponse,
    ImprovementSuggestion,
    ResumeMetrics,
    ConfidenceLevel,
    SuggestionType
)
from api.utils.config import Settings
from api.utils.prompts import ResumeImprovementPrompts
from api.utils.text_analysis import TextAnalyzer


@dataclass
class ModelConfig:
    """Model configuration"""
    name: str
    max_tokens: int
    temperature: float
    timeout: float
    retry_attempts: int


class ResumeImprovementService:
    """Service for improving résumé content using LLMs"""
    
    def __init__(self):
        self.settings = Settings()
        self.text_analyzer = TextAnalyzer()
        self.prompts = ResumeImprovementPrompts()
        
        # Model configurations
        self.gpt4_config = ModelConfig(
            name="gpt-4o",
            max_tokens=4000,
            temperature=0.3,
            timeout=30.0,
            retry_attempts=3
        )
        
        self.deepseek_config = ModelConfig(
            name="deepseek-coder",
            max_tokens=4000,
            temperature=0.3,
            timeout=20.0,
            retry_attempts=2
        )
        
        # Initialize clients
        self.openai_client = None
        self.deepseek_client = None
        self.sentence_transformer = None
        self.grammar_checker = None
        
        # Service state
        self.is_initialized = False
        self.health_status = {"gpt4": False, "deepseek": False, "embeddings": False}
    
    async def initialize(self):
        """Initialize the service and load models"""
        print("🚀 Initializing Résumé Improvement Service...")
        
        try:
            # Initialize OpenAI client
            self.openai_client = openai.AsyncOpenAI(
                api_key=self.settings.openai_api_key,
                timeout=self.gpt4_config.timeout
            )
            
            # Test GPT-4o connection
            try:
                await self._test_gpt4_connection()
                self.health_status["gpt4"] = True
                print("✅ GPT-4o connection established")
            except Exception as e:
                print(f"⚠️ GPT-4o connection failed: {e}")
            
            # Initialize DeepSeek client
            self.deepseek_client = httpx.AsyncClient(
                base_url=self.settings.deepseek_base_url,
                headers={"Authorization": f"Bearer {self.settings.deepseek_api_key}"},
                timeout=self.deepseek_config.timeout
            )
            
            # Test DeepSeek connection
            try:
                await self._test_deepseek_connection()
                self.health_status["deepseek"] = True
                print("✅ DeepSeek-Coder connection established")
            except Exception as e:
                print(f"⚠️ DeepSeek-Coder connection failed: {e}")
            
            # Load sentence transformer for embeddings
            try:
                self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')
                self.health_status["embeddings"] = True
                print("✅ Sentence transformer loaded")
            except Exception as e:
                print(f"⚠️ Sentence transformer loading failed: {e}")
            
            # Initialize grammar checker
            try:
                self.grammar_checker = pipeline(
                    "text2text-generation",
                    model="grammarly/coedit-large",
                    device=0 if torch.cuda.is_available() else -1
                )
                print("✅ Grammar checker loaded")
            except Exception as e:
                print(f"⚠️ Grammar checker loading failed: {e}")
                self.grammar_checker = None
            
            self.is_initialized = True
            print("✅ Résumé Improvement Service initialized successfully")
            
        except Exception as e:
            print(f"❌ Failed to initialize Résumé Improvement Service: {e}")
            raise
    
    async def improve_resume(self, request: ResumeImprovementRequest) -> ResumeImprovementResponse:
        """Main method to improve résumé content"""
        start_time = time.time()
        
        # Analyze original content
        original_metrics = self.text_analyzer.analyze_resume(request.content)
        
        # Try GPT-4o first, fallback to DeepSeek
        improved_content = None
        model_used = None
        suggestions = []
        
        if self.health_status["gpt4"]:
            try:
                improved_content, suggestions = await self._improve_with_gpt4(request)
                model_used = "gpt-4o"
            except Exception as e:
                print(f"⚠️ GPT-4o failed, falling back to DeepSeek: {e}")
        
        if improved_content is None and self.health_status["deepseek"]:
            try:
                improved_content, suggestions = await self._improve_with_deepseek(request)
                model_used = "deepseek-coder"
            except Exception as e:
                print(f"❌ DeepSeek also failed: {e}")
        
        if improved_content is None:
            # Fallback to rule-based improvements
            improved_content, suggestions = await self._improve_with_rules(request)
            model_used = "rule-based"
        
        # Analyze improved content
        improved_metrics = self.text_analyzer.analyze_resume(improved_content)
        
        # Calculate improvement score
        improvement_score = self._calculate_improvement_score(original_metrics, improved_metrics)
        
        # Determine confidence level
        confidence = self._determine_confidence(model_used, improvement_score, suggestions)
        
        # Extract keywords added
        keywords_added = self._extract_keywords_added(request.content, improved_content)
        
        # Identify modified sections
        sections_modified = self._identify_modified_sections(request.content, improved_content)
        
        processing_time = (time.time() - start_time) * 1000
        
        return ResumeImprovementResponse(
            success=True,
            timestamp=int(time.time()),
            processing_time_ms=processing_time,
            model_version=f"{model_used}-v1.0",
            improved_content=improved_content,
            original_length=len(request.content),
            improved_length=len(improved_content),
            overall_improvement_score=improvement_score,
            suggestions=suggestions,
            metrics=improved_metrics,
            confidence=confidence,
            model_used=model_used,
            keywords_added=keywords_added,
            sections_modified=sections_modified
        )
    
    async def _improve_with_gpt4(self, request: ResumeImprovementRequest) -> tuple[str, List[ImprovementSuggestion]]:
        """Improve résumé using GPT-4o"""
        
        # Build context-aware prompt
        prompt = self.prompts.build_improvement_prompt(
            content=request.content,
            target_role=request.target_role,
            industry=request.industry,
            experience_level=request.experience_level,
            job_description=request.job_description,
            improvement_focus=request.improvement_focus,
            tone=request.tone
        )
        
        # Make API call with retry logic
        for attempt in range(self.gpt4_config.retry_attempts):
            try:
                response = await self.openai_client.chat.completions.create(
                    model=self.gpt4_config.name,
                    messages=[
                        {"role": "system", "content": self.prompts.SYSTEM_PROMPT},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=self.gpt4_config.max_tokens,
                    temperature=self.gpt4_config.temperature,
                    response_format={"type": "json_object"}
                )
                
                # Parse response
                result = json.loads(response.choices[0].message.content)
                improved_content = result.get("improved_content", "")
                suggestions_data = result.get("suggestions", [])
                
                # Convert to suggestion objects
                suggestions = [
                    ImprovementSuggestion(
                        type=SuggestionType(s.get("type", "content")),
                        original_text=s.get("original_text", ""),
                        improved_text=s.get("improved_text", ""),
                        explanation=s.get("explanation", ""),
                        confidence=s.get("confidence", 0.8),
                        section=s.get("section"),
                        impact_score=s.get("impact_score", 0.5)
                    )
                    for s in suggestions_data
                ]
                
                return improved_content, suggestions
                
            except Exception as e:
                if attempt == self.gpt4_config.retry_attempts - 1:
                    raise e
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
        
        raise Exception("GPT-4o failed after all retry attempts")
    
    async def _improve_with_deepseek(self, request: ResumeImprovementRequest) -> tuple[str, List[ImprovementSuggestion]]:
        """Improve résumé using DeepSeek-Coder"""
        
        prompt = self.prompts.build_improvement_prompt(
            content=request.content,
            target_role=request.target_role,
            industry=request.industry,
            experience_level=request.experience_level,
            job_description=request.job_description,
            improvement_focus=request.improvement_focus,
            tone=request.tone
        )
        
        payload = {
            "model": self.deepseek_config.name,
            "messages": [
                {"role": "system", "content": self.prompts.SYSTEM_PROMPT},
                {"role": "user", "content": prompt}
            ],
            "max_tokens": self.deepseek_config.max_tokens,
            "temperature": self.deepseek_config.temperature,
            "stream": False
        }
        
        for attempt in range(self.deepseek_config.retry_attempts):
            try:
                response = await self.deepseek_client.post("/v1/chat/completions", json=payload)
                response.raise_for_status()
                
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                
                # Parse JSON response
                parsed_result = json.loads(content)
                improved_content = parsed_result.get("improved_content", "")
                suggestions_data = parsed_result.get("suggestions", [])
                
                suggestions = [
                    ImprovementSuggestion(
                        type=SuggestionType(s.get("type", "content")),
                        original_text=s.get("original_text", ""),
                        improved_text=s.get("improved_text", ""),
                        explanation=s.get("explanation", ""),
                        confidence=s.get("confidence", 0.7),
                        section=s.get("section"),
                        impact_score=s.get("impact_score", 0.5)
                    )
                    for s in suggestions_data
                ]
                
                return improved_content, suggestions
                
            except Exception as e:
                if attempt == self.deepseek_config.retry_attempts - 1:
                    raise e
                await asyncio.sleep(1.5 ** attempt)
        
        raise Exception("DeepSeek-Coder failed after all retry attempts")
    
    async def _improve_with_rules(self, request: ResumeImprovementRequest) -> tuple[str, List[ImprovementSuggestion]]:
        """Fallback rule-based improvement"""
        
        content = request.content
        suggestions = []
        
        # Apply grammar corrections if available
        if self.grammar_checker:
            try:
                corrected = self.grammar_checker(content, max_length=len(content) + 100)[0]['generated_text']
                if corrected != content:
                    suggestions.append(ImprovementSuggestion(
                        type=SuggestionType.GRAMMAR,
                        original_text=content[:100] + "...",
                        improved_text=corrected[:100] + "...",
                        explanation="Grammar and style corrections applied",
                        confidence=0.6,
                        impact_score=0.3
                    ))
                    content = corrected
            except Exception as e:
                print(f"Grammar correction failed: {e}")
        
        # Apply rule-based improvements
        improved_content = self._apply_rule_based_improvements(content)
        
        if improved_content != content:
            suggestions.append(ImprovementSuggestion(
                type=SuggestionType.CONTENT,
                original_text="Original content",
                improved_text="Rule-based improvements",
                explanation="Applied formatting and structure improvements",
                confidence=0.5,
                impact_score=0.4
            ))
        
        return improved_content, suggestions
    
    def _apply_rule_based_improvements(self, content: str) -> str:
        """Apply rule-based text improvements"""
        
        # Fix common formatting issues
        content = re.sub(r'\s+', ' ', content)  # Multiple spaces
        content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)  # Multiple newlines
        
        # Improve bullet points
        content = re.sub(r'^[-*•]\s*', '• ', content, flags=re.MULTILINE)
        
        # Capitalize section headers
        lines = content.split('\n')
        improved_lines = []
        
        for line in lines:
            stripped = line.strip()
            if stripped and len(stripped) < 50 and not any(char.islower() for char in stripped):
                # Likely a section header
                improved_lines.append(stripped.title())
            else:
                improved_lines.append(line)
        
        return '\n'.join(improved_lines)
    
    def _calculate_improvement_score(self, original: ResumeMetrics, improved: ResumeMetrics) -> float:
        """Calculate overall improvement score"""
        
        metrics = [
            'readability_score',
            'keyword_density',
            'action_verb_ratio',
            'quantification_score',
            'formatting_score'
        ]
        
        total_improvement = 0
        for metric in metrics:
            original_val = getattr(original, metric)
            improved_val = getattr(improved, metric)
            improvement = (improved_val - original_val) / max(original_val, 0.1)
            total_improvement += max(0, improvement)  # Only count positive improvements
        
        return min(1.0, total_improvement / len(metrics))
    
    def _determine_confidence(self, model_used: str, improvement_score: float, suggestions: List) -> ConfidenceLevel:
        """Determine confidence level based on model and results"""
        
        base_confidence = {
            "gpt-4o": 0.9,
            "deepseek-coder": 0.7,
            "rule-based": 0.4
        }.get(model_used, 0.3)
        
        # Adjust based on improvement score and number of suggestions
        adjusted_confidence = base_confidence * (0.5 + 0.5 * improvement_score)
        if len(suggestions) > 5:
            adjusted_confidence *= 1.1
        
        if adjusted_confidence >= 0.8:
            return ConfidenceLevel.VERY_HIGH
        elif adjusted_confidence >= 0.6:
            return ConfidenceLevel.HIGH
        elif adjusted_confidence >= 0.4:
            return ConfidenceLevel.MEDIUM
        else:
            return ConfidenceLevel.LOW
    
    def _extract_keywords_added(self, original: str, improved: str) -> List[str]:
        """Extract keywords that were added during improvement"""
        
        original_words = set(re.findall(r'\b\w+\b', original.lower()))
        improved_words = set(re.findall(r'\b\w+\b', improved.lower()))
        
        added_words = improved_words - original_words
        
        # Filter for meaningful keywords (length > 3, not common words)
        common_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'man', 'men', 'put', 'say', 'she', 'too', 'use'}
        
        keywords = [word for word in added_words if len(word) > 3 and word not in common_words]
        
        return sorted(keywords)[:10]  # Return top 10
    
    def _identify_modified_sections(self, original: str, improved: str) -> List[str]:
        """Identify which sections were modified"""
        
        # Simple heuristic based on common section headers
        sections = ['summary', 'experience', 'education', 'skills', 'projects', 'certifications']
        modified_sections = []
        
        for section in sections:
            original_section = self._extract_section(original, section)
            improved_section = self._extract_section(improved, section)
            
            if original_section != improved_section:
                modified_sections.append(section)
        
        return modified_sections
    
    def _extract_section(self, content: str, section_name: str) -> str:
        """Extract a specific section from résumé content"""
        
        pattern = rf'(?i){section_name}.*?(?=\n[A-Z][A-Z\s]*\n|\Z)'
        match = re.search(pattern, content, re.DOTALL)
        
        return match.group(0) if match else ""
    
    async def _test_gpt4_connection(self):
        """Test GPT-4o connection"""
        response = await self.openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=5
        )
        return response.choices[0].message.content
    
    async def _test_deepseek_connection(self):
        """Test DeepSeek connection"""
        response = await self.deepseek_client.post(
            "/v1/chat/completions",
            json={
                "model": "deepseek-coder",
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 5
            }
        )
        response.raise_for_status()
        return response.json()
    
    def is_healthy(self) -> bool:
        """Check if service is healthy"""
        return self.is_initialized and (self.health_status["gpt4"] or self.health_status["deepseek"])
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.deepseek_client:
            await self.deepseek_client.aclose()
        print("✅ Résumé Improvement Service cleaned up")
