"""
Form Autocompletion Service
Context-aware ML models for intelligent form filling in sandbox automation
"""

import asyncio
import time
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

import numpy as np
import pandas as pd
from sentence_transformers import SentenceTransformer
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import spacy
from fuzzywuzzy import fuzz, process

from api.schemas.requests import FormAutocompletionRequest, FormField
from api.schemas.responses import (
    FormAutocompletionResponse,
    FieldSuggestion,
    AutofillConfidence,
    FormContext
)
from api.utils.config import Settings


class FieldType(Enum):
    """Types of form fields"""
    TEXT = "text"
    EMAIL = "email"
    PHONE = "phone"
    DATE = "date"
    SELECT = "select"
    TEXTAREA = "textarea"
    CHECKBOX = "checkbox"
    RADIO = "radio"
    FILE = "file"
    URL = "url"
    NUMBER = "number"


@dataclass
class FieldPattern:
    """Pattern for field recognition"""
    field_type: FieldType
    patterns: List[str]
    confidence_threshold: float
    extraction_method: str


class FormAutocompletionService:
    """Service for intelligent form autocompletion"""
    
    def __init__(self):
        self.settings = Settings()
        
        # ML models
        self.sentence_transformer = None
        self.nlp = None
        self.tfidf_vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        
        # Field patterns and mappings
        self.field_patterns = self._initialize_field_patterns()
        self.common_field_mappings = self._initialize_field_mappings()
        
        # Context understanding
        self.context_embeddings = {}
        self.field_embeddings = {}
        
        # Service state
        self.is_initialized = False
        
    async def initialize(self):
        """Initialize the form autocompletion service"""
        print("🚀 Initializing Form Autocompletion Service...")
        
        try:
            # Load sentence transformer for semantic similarity
            self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')
            print("✅ Sentence transformer loaded")
            
            # Load spaCy for NLP processing
            try:
                self.nlp = spacy.load("en_core_web_sm")
                print("✅ spaCy model loaded")
            except OSError:
                print("⚠️ spaCy model not found, using basic NLP")
                self.nlp = None
            
            # Pre-compute embeddings for common field types
            await self._precompute_embeddings()
            
            self.is_initialized = True
            print("✅ Form Autocompletion Service initialized")
            
        except Exception as e:
            print(f"❌ Failed to initialize Form Autocompletion Service: {e}")
            raise
    
    async def autocomplete_form(self, request: FormAutocompletionRequest) -> FormAutocompletionResponse:
        """Generate autocompletion suggestions for form fields"""
        if not self.is_initialized:
            raise RuntimeError("Service not initialized")
        
        start_time = time.time()
        
        try:
            # Analyze form context
            form_context = await self._analyze_form_context(request.form_fields, request.page_context)
            
            # Generate suggestions for each field
            suggestions = []
            for field in request.form_fields:
                suggestion = await self._generate_field_suggestion(
                    field, 
                    request.user_data,
                    form_context,
                    request.page_context
                )
                suggestions.append(suggestion)
            
            processing_time = time.time() - start_time
            
            return FormAutocompletionResponse(
                suggestions=suggestions,
                form_context=form_context,
                processing_time_ms=int(processing_time * 1000),
                confidence_score=self._calculate_overall_confidence(suggestions),
                automation_ready=self._is_automation_ready(suggestions)
            )
            
        except Exception as e:
            print(f"❌ Error in form autocompletion: {e}")
            raise
    
    async def _analyze_form_context(self, fields: List[FormField], page_context: Dict) -> FormContext:
        """Analyze the overall context of the form"""
        
        # Extract form purpose from page context
        form_purpose = await self._identify_form_purpose(fields, page_context)
        
        # Identify form type (job application, contact, registration, etc.)
        form_type = await self._classify_form_type(fields, page_context)
        
        # Analyze field relationships
        field_relationships = await self._analyze_field_relationships(fields)
        
        # Determine required vs optional fields
        required_fields = [f.field_id for f in fields if f.required]
        
        return FormContext(
            form_type=form_type,
            form_purpose=form_purpose,
            total_fields=len(fields),
            required_fields=len(required_fields),
            estimated_completion_time=self._estimate_completion_time(fields),
            complexity_score=self._calculate_complexity_score(fields),
            field_relationships=field_relationships
        )
    
    async def _generate_field_suggestion(
        self, 
        field: FormField, 
        user_data: Dict,
        form_context: FormContext,
        page_context: Dict
    ) -> FieldSuggestion:
        """Generate suggestion for a single form field"""
        
        # Identify field type and purpose
        field_type = await self._identify_field_type(field)
        field_purpose = await self._identify_field_purpose(field, form_context)
        
        # Extract relevant data from user profile
        suggested_value = await self._extract_suggested_value(
            field, field_type, field_purpose, user_data
        )
        
        # Calculate confidence
        confidence = await self._calculate_field_confidence(
            field, suggested_value, field_type, user_data
        )
        
        # Generate alternatives
        alternatives = await self._generate_alternatives(
            field, field_type, field_purpose, user_data
        )
        
        return FieldSuggestion(
            field_id=field.field_id,
            suggested_value=suggested_value,
            confidence=confidence,
            field_type=field_type.value,
            alternatives=alternatives,
            reasoning=self._generate_reasoning(field, suggested_value, confidence),
            validation_status=await self._validate_suggestion(field, suggested_value)
        )
    
    async def _identify_field_type(self, field: FormField) -> FieldType:
        """Identify the type of form field"""
        
        # Check HTML input type first
        if field.input_type:
            type_mapping = {
                'email': FieldType.EMAIL,
                'tel': FieldType.PHONE,
                'date': FieldType.DATE,
                'url': FieldType.URL,
                'number': FieldType.NUMBER,
                'file': FieldType.FILE,
                'checkbox': FieldType.CHECKBOX,
                'radio': FieldType.RADIO
            }
            if field.input_type in type_mapping:
                return type_mapping[field.input_type]
        
        # Analyze field attributes
        field_text = f"{field.label} {field.placeholder} {field.name}".lower()
        
        # Use pattern matching
        for pattern in self.field_patterns:
            for regex in pattern.patterns:
                if re.search(regex, field_text):
                    return pattern.field_type
        
        # Use semantic similarity
        if self.sentence_transformer:
            field_embedding = self.sentence_transformer.encode([field_text])
            
            best_match = None
            best_similarity = 0
            
            for field_type, embedding in self.field_embeddings.items():
                similarity = cosine_similarity(field_embedding, embedding.reshape(1, -1))[0][0]
                if similarity > best_similarity:
                    best_similarity = similarity
                    best_match = field_type
            
            if best_similarity > 0.7:
                return FieldType(best_match)
        
        return FieldType.TEXT
    
    async def _identify_field_purpose(self, field: FormField, form_context: FormContext) -> str:
        """Identify the specific purpose of a field"""
        
        field_text = f"{field.label} {field.placeholder} {field.name}".lower()
        
        # Common field purposes
        purpose_patterns = {
            'first_name': ['first.?name', 'given.?name', 'fname'],
            'last_name': ['last.?name', 'family.?name', 'surname', 'lname'],
            'full_name': ['full.?name', 'name', '^name$'],
            'email': ['email', 'e-mail'],
            'phone': ['phone', 'telephone', 'mobile', 'cell'],
            'address': ['address', 'street'],
            'city': ['city', 'town'],
            'state': ['state', 'province', 'region'],
            'zip': ['zip', 'postal', 'postcode'],
            'country': ['country', 'nation'],
            'company': ['company', 'organization', 'employer'],
            'title': ['title', 'position', 'role', 'job.?title'],
            'experience': ['experience', 'years'],
            'salary': ['salary', 'compensation', 'pay'],
            'resume': ['resume', 'cv', 'curriculum'],
            'cover_letter': ['cover.?letter', 'motivation'],
            'linkedin': ['linkedin', 'profile'],
            'website': ['website', 'portfolio', 'url'],
            'message': ['message', 'comment', 'note', 'additional']
        }
        
        for purpose, patterns in purpose_patterns.items():
            for pattern in patterns:
                if re.search(pattern, field_text):
                    return purpose
        
        return 'unknown'
    
    async def _extract_suggested_value(
        self, 
        field: FormField, 
        field_type: FieldType, 
        field_purpose: str,
        user_data: Dict
    ) -> str:
        """Extract suggested value from user data"""
        
        # Direct mapping from user data
        if field_purpose in user_data:
            return str(user_data[field_purpose])
        
        # Handle composite fields
        if field_purpose == 'full_name':
            first_name = user_data.get('first_name', '')
            last_name = user_data.get('last_name', '')
            return f"{first_name} {last_name}".strip()
        
        # Handle address fields
        if field_purpose == 'address':
            return user_data.get('street_address', '')
        
        # Handle experience fields
        if field_purpose == 'experience':
            return str(user_data.get('years_experience', ''))
        
        # Handle file uploads
        if field_type == FieldType.FILE:
            if 'resume' in field.label.lower():
                return user_data.get('resume_file_path', '')
            elif 'cover' in field.label.lower():
                return user_data.get('cover_letter_path', '')
        
        # Handle select fields
        if field_type == FieldType.SELECT and field.options:
            return await self._select_best_option(field.options, field_purpose, user_data)
        
        # Fuzzy matching for similar field names
        if user_data:
            best_match = process.extractOne(
                field_purpose, 
                user_data.keys(), 
                scorer=fuzz.ratio,
                score_cutoff=70
            )
            if best_match:
                return str(user_data[best_match[0]])
        
        return ''
    
    async def _select_best_option(self, options: List[str], field_purpose: str, user_data: Dict) -> str:
        """Select the best option from a dropdown/select field"""
        
        if not options:
            return ''
        
        # Direct match
        user_value = user_data.get(field_purpose, '').lower()
        for option in options:
            if option.lower() == user_value:
                return option
        
        # Fuzzy matching
        if user_value:
            best_match = process.extractOne(
                user_value,
                options,
                scorer=fuzz.ratio,
                score_cutoff=60
            )
            if best_match:
                return best_match[0]
        
        # Semantic matching for experience levels, education, etc.
        if field_purpose in ['experience', 'education', 'job_level']:
            return await self._semantic_option_matching(options, field_purpose, user_data)
        
        return options[0] if options else ''
    
    async def _semantic_option_matching(self, options: List[str], field_purpose: str, user_data: Dict) -> str:
        """Use semantic similarity to match options"""
        
        if not self.sentence_transformer:
            return options[0] if options else ''
        
        user_context = self._build_user_context(field_purpose, user_data)
        if not user_context:
            return options[0] if options else ''
        
        # Encode user context and options
        user_embedding = self.sentence_transformer.encode([user_context])
        option_embeddings = self.sentence_transformer.encode(options)
        
        # Find best match
        similarities = cosine_similarity(user_embedding, option_embeddings)[0]
        best_idx = np.argmax(similarities)
        
        return options[best_idx]
    
    def _build_user_context(self, field_purpose: str, user_data: Dict) -> str:
        """Build context string for semantic matching"""
        
        context_parts = []
        
        if field_purpose == 'experience':
            years = user_data.get('years_experience', 0)
            context_parts.append(f"{years} years experience")
            
        elif field_purpose == 'education':
            education = user_data.get('education_level', '')
            context_parts.append(education)
            
        elif field_purpose == 'job_level':
            title = user_data.get('current_title', '')
            experience = user_data.get('years_experience', 0)
            context_parts.extend([title, f"{experience} years"])
        
        return ' '.join(context_parts)
    
    async def _calculate_field_confidence(
        self, 
        field: FormField, 
        suggested_value: str, 
        field_type: FieldType,
        user_data: Dict
    ) -> AutofillConfidence:
        """Calculate confidence level for field suggestion"""
        
        confidence_score = 0.5  # Base confidence
        
        # Boost confidence for exact matches
        if suggested_value and suggested_value in str(user_data.values()):
            confidence_score += 0.3
        
        # Boost confidence for validated field types
        if field_type != FieldType.TEXT:
            confidence_score += 0.1
        
        # Boost confidence for required fields with values
        if field.required and suggested_value:
            confidence_score += 0.1
        
        # Reduce confidence for empty suggestions
        if not suggested_value:
            confidence_score -= 0.3
        
        # Validate the suggestion
        if await self._validate_field_value(field_type, suggested_value):
            confidence_score += 0.1
        else:
            confidence_score -= 0.2
        
        confidence_score = max(0.0, min(1.0, confidence_score))
        
        if confidence_score >= 0.8:
            return AutofillConfidence.HIGH
        elif confidence_score >= 0.6:
            return AutofillConfidence.MEDIUM
        else:
            return AutofillConfidence.LOW
    
    async def _validate_field_value(self, field_type: FieldType, value: str) -> bool:
        """Validate if a value is appropriate for the field type"""
        
        if not value:
            return True  # Empty values are generally acceptable
        
        if field_type == FieldType.EMAIL:
            return re.match(r'^[^@]+@[^@]+\.[^@]+$', value) is not None
        
        elif field_type == FieldType.PHONE:
            # Basic phone validation
            return re.match(r'^[\d\s\-\+\(\)]+$', value) is not None
        
        elif field_type == FieldType.URL:
            return value.startswith(('http://', 'https://')) or '.' in value
        
        elif field_type == FieldType.NUMBER:
            try:
                float(value)
                return True
            except ValueError:
                return False
        
        elif field_type == FieldType.DATE:
            # Basic date format validation
            return re.match(r'^\d{1,2}[/-]\d{1,2}[/-]\d{4}$', value) is not None
        
        return True
    
    # Utility methods
    def _initialize_field_patterns(self) -> List[FieldPattern]:
        """Initialize field recognition patterns"""
        return [
            FieldPattern(FieldType.EMAIL, [r'email', r'e-mail', r'mail'], 0.8, 'regex'),
            FieldPattern(FieldType.PHONE, [r'phone', r'tel', r'mobile', r'cell'], 0.8, 'regex'),
            FieldPattern(FieldType.DATE, [r'date', r'birth', r'dob'], 0.8, 'regex'),
            FieldPattern(FieldType.URL, [r'website', r'url', r'link', r'portfolio'], 0.8, 'regex'),
            FieldPattern(FieldType.FILE, [r'upload', r'file', r'attach', r'resume', r'cv'], 0.8, 'regex'),
            FieldPattern(FieldType.TEXTAREA, [r'message', r'comment', r'description', r'cover'], 0.7, 'regex'),
        ]
    
    def _initialize_field_mappings(self) -> Dict[str, str]:
        """Initialize common field mappings"""
        return {
            'fname': 'first_name',
            'lname': 'last_name',
            'email': 'email',
            'phone': 'phone',
            'company': 'company',
            'title': 'current_title',
            'experience': 'years_experience'
        }
    
    async def _precompute_embeddings(self):
        """Pre-compute embeddings for common field types"""
        if not self.sentence_transformer:
            return
        
        field_examples = {
            'email': 'email address electronic mail',
            'phone': 'phone number telephone mobile',
            'name': 'first name last name full name',
            'address': 'street address location',
            'company': 'company organization employer',
            'title': 'job title position role',
            'experience': 'years experience work history',
            'education': 'education degree school university',
            'salary': 'salary compensation pay wage',
            'date': 'date birth birthday'
        }
        
        for field_type, example in field_examples.items():
            embedding = self.sentence_transformer.encode([example])
            self.field_embeddings[field_type] = embedding[0]
    
    def _calculate_overall_confidence(self, suggestions: List[FieldSuggestion]) -> float:
        """Calculate overall confidence for the form"""
        if not suggestions:
            return 0.0
        
        confidence_values = {
            AutofillConfidence.HIGH: 1.0,
            AutofillConfidence.MEDIUM: 0.7,
            AutofillConfidence.LOW: 0.3
        }
        
        total_confidence = sum(confidence_values[s.confidence] for s in suggestions)
        return total_confidence / len(suggestions)
    
    def _is_automation_ready(self, suggestions: List[FieldSuggestion]) -> bool:
        """Determine if form is ready for automation"""
        high_confidence_count = sum(1 for s in suggestions if s.confidence == AutofillConfidence.HIGH)
        return high_confidence_count >= len(suggestions) * 0.7
    
    async def _identify_form_purpose(self, fields: List[FormField], page_context: Dict) -> str:
        """Identify the purpose of the form"""
        # Implementation for form purpose identification
        return "job_application"
    
    async def _classify_form_type(self, fields: List[FormField], page_context: Dict) -> str:
        """Classify the type of form"""
        # Implementation for form type classification
        return "application"
    
    async def _analyze_field_relationships(self, fields: List[FormField]) -> Dict[str, List[str]]:
        """Analyze relationships between fields"""
        # Implementation for field relationship analysis
        return {}
    
    def _estimate_completion_time(self, fields: List[FormField]) -> int:
        """Estimate time to complete form in seconds"""
        return len(fields) * 15  # 15 seconds per field average
    
    def _calculate_complexity_score(self, fields: List[FormField]) -> float:
        """Calculate form complexity score"""
        return min(1.0, len(fields) / 20)  # Normalize by 20 fields
    
    async def _generate_alternatives(self, field: FormField, field_type: FieldType, field_purpose: str, user_data: Dict) -> List[str]:
        """Generate alternative suggestions"""
        return []  # Implementation for alternatives
    
    def _generate_reasoning(self, field: FormField, suggested_value: str, confidence: AutofillConfidence) -> str:
        """Generate reasoning for the suggestion"""
        if confidence == AutofillConfidence.HIGH:
            return f"High confidence match based on field pattern and user data"
        elif confidence == AutofillConfidence.MEDIUM:
            return f"Moderate confidence based on field analysis"
        else:
            return f"Low confidence suggestion, manual review recommended"
    
    async def _validate_suggestion(self, field: FormField, suggested_value: str) -> str:
        """Validate the suggestion"""
        return "valid" if suggested_value else "empty"
