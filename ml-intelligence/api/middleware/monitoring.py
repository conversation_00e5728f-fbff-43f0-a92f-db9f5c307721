"""
Production monitoring middleware with OpenTelemetry and custom metrics
Real-time performance tracking and alerting
"""

import time
import asyncio
from typing import Dict, Any, Optional
import json
import uuid
from datetime import datetime

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from prometheus_client import Counter, Histogram, Gauge, Info
from opentelemetry import trace, metrics
from opentelemetry.trace import Status, StatusCode
import structlog

from api.utils.config import Settings


# Prometheus metrics
REQUEST_COUNT = Counter(
    'ml_api_requests_total',
    'Total API requests',
    ['method', 'endpoint', 'status_code', 'model_version']
)

REQUEST_DURATION = Histogram(
    'ml_api_request_duration_seconds',
    'Request duration in seconds',
    ['endpoint', 'model_type'],
    buckets=[0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
)

MODEL_INFERENCE_TIME = Histogram(
    'ml_model_inference_duration_seconds',
    'Model inference time in seconds',
    ['model_name', 'model_version'],
    buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.0]
)

ACTIVE_REQUESTS = Gauge(
    'ml_api_active_requests',
    'Number of active requests',
    ['endpoint']
)

MODEL_ACCURACY = Gauge(
    'ml_model_accuracy',
    'Model accuracy metrics',
    ['model_name', 'metric_type']
)

CACHE_HIT_RATE = Gauge(
    'ml_cache_hit_rate',
    'Cache hit rate percentage',
    ['service']
)

ERROR_RATE = Counter(
    'ml_api_errors_total',
    'Total API errors',
    ['endpoint', 'error_type', 'error_code']
)

# OpenTelemetry setup
tracer = trace.get_tracer(__name__)
meter = metrics.get_meter(__name__)

# Structured logging
logger = structlog.get_logger()


class MonitoringMiddleware(BaseHTTPMiddleware):
    """Comprehensive monitoring middleware for ML API"""
    
    def __init__(self, app, settings: Optional[Settings] = None):
        super().__init__(app)
        self.settings = settings or Settings()
        self.performance_thresholds = {
            'request_duration_warning': 1.0,  # seconds
            'request_duration_critical': 5.0,  # seconds
            'inference_time_warning': 0.5,    # seconds
            'inference_time_critical': 2.0,   # seconds
            'error_rate_warning': 0.05,       # 5%
            'error_rate_critical': 0.10,      # 10%
        }
        
        # Request tracking
        self.active_requests: Dict[str, Dict[str, Any]] = {}
        
        # Performance tracking
        self.request_times = []
        self.error_counts = {}
        
    async def dispatch(self, request: Request, call_next):
        """Main middleware dispatch method"""
        
        # Generate request ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Extract request information
        method = request.method
        path = request.url.path
        endpoint = self._normalize_endpoint(path)
        
        # Start timing
        start_time = time.time()
        
        # Track active request
        self.active_requests[request_id] = {
            'method': method,
            'endpoint': endpoint,
            'start_time': start_time,
            'user_agent': request.headers.get('user-agent', ''),
            'ip_address': self._get_client_ip(request)
        }
        
        # Update active requests gauge
        ACTIVE_REQUESTS.labels(endpoint=endpoint).inc()
        
        # Start OpenTelemetry span
        with tracer.start_as_current_span(
            f"{method} {endpoint}",
            attributes={
                "http.method": method,
                "http.url": str(request.url),
                "http.route": endpoint,
                "request.id": request_id,
                "user.ip": self._get_client_ip(request)
            }
        ) as span:
            
            try:
                # Process request
                response = await call_next(request)
                
                # Calculate duration
                duration = time.time() - start_time
                
                # Extract model information from response headers
                model_version = response.headers.get('X-Model-Version', 'unknown')
                model_type = self._extract_model_type(endpoint)
                
                # Record metrics
                REQUEST_COUNT.labels(
                    method=method,
                    endpoint=endpoint,
                    status_code=response.status_code,
                    model_version=model_version
                ).inc()
                
                REQUEST_DURATION.labels(
                    endpoint=endpoint,
                    model_type=model_type
                ).observe(duration)
                
                # Update span with response information
                span.set_attributes({
                    "http.status_code": response.status_code,
                    "response.duration_ms": duration * 1000,
                    "model.version": model_version,
                    "model.type": model_type
                })
                
                # Check performance thresholds
                await self._check_performance_thresholds(endpoint, duration, response.status_code)
                
                # Log request
                await self._log_request(request, response, duration, request_id)
                
                # Set response headers
                response.headers["X-Request-ID"] = request_id
                response.headers["X-Response-Time"] = f"{duration:.3f}s"
                
                # Mark span as successful
                span.set_status(Status(StatusCode.OK))
                
                return response
                
            except Exception as e:
                # Calculate duration for error case
                duration = time.time() - start_time
                
                # Record error metrics
                error_type = type(e).__name__
                ERROR_RATE.labels(
                    endpoint=endpoint,
                    error_type=error_type,
                    error_code='500'
                ).inc()
                
                # Update span with error information
                span.set_status(Status(StatusCode.ERROR, str(e)))
                span.set_attributes({
                    "error.type": error_type,
                    "error.message": str(e),
                    "response.duration_ms": duration * 1000
                })
                
                # Log error
                await self._log_error(request, e, duration, request_id)
                
                # Re-raise exception
                raise
                
            finally:
                # Clean up active request tracking
                if request_id in self.active_requests:
                    del self.active_requests[request_id]
                
                # Update active requests gauge
                ACTIVE_REQUESTS.labels(endpoint=endpoint).dec()
    
    def _normalize_endpoint(self, path: str) -> str:
        """Normalize endpoint path for metrics"""
        # Replace dynamic segments with placeholders
        if path.startswith('/v1/'):
            parts = path.split('/')
            if len(parts) >= 3:
                return f"/{parts[1]}/{parts[2]}"
        
        return path
    
    def _extract_model_type(self, endpoint: str) -> str:
        """Extract model type from endpoint"""
        if 'resume' in endpoint:
            return 'resume_improvement'
        elif 'ats' in endpoint:
            return 'ats_scoring'
        elif 'jobs' in endpoint:
            return 'job_ranking'
        elif 'forms' in endpoint:
            return 'form_completion'
        else:
            return 'unknown'
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address"""
        # Check for forwarded headers first
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        # Fallback to direct client
        if hasattr(request.client, 'host'):
            return request.client.host
        
        return 'unknown'
    
    async def _check_performance_thresholds(self, endpoint: str, duration: float, status_code: int):
        """Check performance thresholds and trigger alerts"""
        
        # Check request duration
        if duration > self.performance_thresholds['request_duration_critical']:
            await self._trigger_alert(
                'critical',
                f"Request duration exceeded critical threshold: {duration:.3f}s for {endpoint}",
                {
                    'endpoint': endpoint,
                    'duration': duration,
                    'threshold': self.performance_thresholds['request_duration_critical']
                }
            )
        elif duration > self.performance_thresholds['request_duration_warning']:
            await self._trigger_alert(
                'warning',
                f"Request duration exceeded warning threshold: {duration:.3f}s for {endpoint}",
                {
                    'endpoint': endpoint,
                    'duration': duration,
                    'threshold': self.performance_thresholds['request_duration_warning']
                }
            )
        
        # Check for errors
        if status_code >= 500:
            await self._trigger_alert(
                'error',
                f"Server error {status_code} for {endpoint}",
                {
                    'endpoint': endpoint,
                    'status_code': status_code
                }
            )
    
    async def _trigger_alert(self, severity: str, message: str, context: Dict[str, Any]):
        """Trigger alert for monitoring systems"""
        
        alert_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'severity': severity,
            'message': message,
            'context': context,
            'service': 'ml-intelligence-api'
        }
        
        # Log alert
        logger.warning("Performance alert triggered", **alert_data)
        
        # In production, send to alerting system (PagerDuty, Slack, etc.)
        if self.settings.environment == 'production':
            await self._send_to_alerting_system(alert_data)
    
    async def _send_to_alerting_system(self, alert_data: Dict[str, Any]):
        """Send alert to external alerting system"""
        # Placeholder for alerting system integration
        # In production, integrate with PagerDuty, Slack, etc.
        pass
    
    async def _log_request(self, request: Request, response: Response, duration: float, request_id: str):
        """Log request details"""
        
        log_data = {
            'request_id': request_id,
            'method': request.method,
            'path': request.url.path,
            'status_code': response.status_code,
            'duration_ms': duration * 1000,
            'user_agent': request.headers.get('user-agent', ''),
            'ip_address': self._get_client_ip(request),
            'content_length': response.headers.get('content-length', 0)
        }
        
        # Add model information if available
        if 'X-Model-Version' in response.headers:
            log_data['model_version'] = response.headers['X-Model-Version']
        
        logger.info("Request processed", **log_data)
    
    async def _log_error(self, request: Request, error: Exception, duration: float, request_id: str):
        """Log error details"""
        
        log_data = {
            'request_id': request_id,
            'method': request.method,
            'path': request.url.path,
            'error_type': type(error).__name__,
            'error_message': str(error),
            'duration_ms': duration * 1000,
            'user_agent': request.headers.get('user-agent', ''),
            'ip_address': self._get_client_ip(request)
        }
        
        logger.error("Request failed", **log_data)
    
    async def get_metrics_summary(self) -> Dict[str, Any]:
        """Get current metrics summary"""
        
        # Calculate recent performance metrics
        recent_requests = len([
            req for req in self.active_requests.values()
            if time.time() - req['start_time'] < 300  # Last 5 minutes
        ])
        
        return {
            'active_requests': len(self.active_requests),
            'recent_requests_5min': recent_requests,
            'performance_thresholds': self.performance_thresholds,
            'timestamp': datetime.utcnow().isoformat()
        }


# Utility functions for model performance tracking
async def track_model_inference(model_name: str, model_version: str, inference_time: float):
    """Track model inference performance"""
    MODEL_INFERENCE_TIME.labels(
        model_name=model_name,
        model_version=model_version
    ).observe(inference_time)


async def update_model_accuracy(model_name: str, metric_type: str, accuracy_value: float):
    """Update model accuracy metrics"""
    MODEL_ACCURACY.labels(
        model_name=model_name,
        metric_type=metric_type
    ).set(accuracy_value)


async def update_cache_hit_rate(service: str, hit_rate: float):
    """Update cache hit rate metrics"""
    CACHE_HIT_RATE.labels(service=service).set(hit_rate)


# Health check for monitoring
async def monitoring_health_check() -> Dict[str, Any]:
    """Health check for monitoring systems"""
    
    try:
        # Check if metrics are being collected
        current_time = time.time()
        
        return {
            'status': 'healthy',
            'timestamp': current_time,
            'metrics_collection': 'active',
            'tracing': 'active'
        }
        
    except Exception as e:
        return {
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }
