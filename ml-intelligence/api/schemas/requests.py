"""
Request schemas for CVLeap ML Intelligence API
Pydantic models with validation and OpenAPI documentation
"""

from typing import Dict, List, Optional, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, validator, root_validator


class ExperienceLevel(str, Enum):
    """Experience level enumeration"""
    ENTRY = "entry"
    MID = "mid"
    SENIOR = "senior"
    EXECUTIVE = "executive"


class Industry(str, Enum):
    """Industry enumeration"""
    TECHNOLOGY = "technology"
    FINANCE = "finance"
    HEALTHCARE = "healthcare"
    EDUCATION = "education"
    RETAIL = "retail"
    MANUFACTURING = "manufacturing"
    CONSULTING = "consulting"
    GOVERNMENT = "government"
    NONPROFIT = "nonprofit"
    OTHER = "other"


class ATSSystem(str, Enum):
    """ATS system enumeration"""
    WORKDAY = "workday"
    GREENHOUSE = "greenhouse"
    LEVER = "lever"
    BAMBOO_HR = "bamboo_hr"
    ICIMS = "icims"
    TALEO = "taleo"
    SUCCESSFACTORS = "successfactors"
    GENERIC = "generic"


class FormFieldType(str, Enum):
    """Form field type enumeration"""
    TEXT = "text"
    EMAIL = "email"
    PHONE = "phone"
    NUMBER = "number"
    DATE = "date"
    SELECT = "select"
    TEXTAREA = "textarea"
    CHECKBOX = "checkbox"
    RADIO = "radio"
    FILE = "file"


# Résumé Improvement Request
class ResumeImprovementRequest(BaseModel):
    """Request for résumé content improvement"""
    
    content: str = Field(
        ...,
        min_length=50,
        max_length=50000,
        description="Original résumé content to improve",
        example="Software engineer with 5 years of experience in web development..."
    )
    
    target_role: Optional[str] = Field(
        None,
        max_length=200,
        description="Target job role for optimization",
        example="Senior Machine Learning Engineer"
    )
    
    industry: Optional[Industry] = Field(
        None,
        description="Target industry for role-specific optimization"
    )
    
    experience_level: Optional[ExperienceLevel] = Field(
        None,
        description="Experience level for appropriate tone and content"
    )
    
    company_name: Optional[str] = Field(
        None,
        max_length=100,
        description="Target company name for customization",
        example="Google"
    )
    
    job_description: Optional[str] = Field(
        None,
        max_length=20000,
        description="Job description for targeted optimization"
    )
    
    improvement_focus: Optional[List[str]] = Field(
        default_factory=list,
        description="Specific areas to focus on",
        example=["technical_skills", "leadership", "achievements"]
    )
    
    preserve_sections: Optional[List[str]] = Field(
        default_factory=list,
        description="Sections to preserve without modification",
        example=["contact_info", "education"]
    )
    
    tone: Optional[str] = Field(
        "professional",
        description="Desired tone for the content",
        example="professional"
    )
    
    max_length: Optional[int] = Field(
        None,
        ge=100,
        le=10000,
        description="Maximum length for improved content"
    )

    @validator('improvement_focus')
    def validate_improvement_focus(cls, v):
        valid_focuses = {
            "technical_skills", "soft_skills", "achievements", "leadership",
            "education", "certifications", "projects", "experience",
            "keywords", "formatting", "grammar", "clarity"
        }
        for focus in v:
            if focus not in valid_focuses:
                raise ValueError(f"Invalid improvement focus: {focus}")
        return v


# ATS Scoring Request
class ATSScoringRequest(BaseModel):
    """Request for ATS compatibility scoring"""
    
    resume_text: str = Field(
        ...,
        min_length=50,
        max_length=50000,
        description="Résumé text content for ATS analysis"
    )
    
    job_description: str = Field(
        ...,
        min_length=50,
        max_length=20000,
        description="Job description to match against"
    )
    
    ats_system: Optional[ATSSystem] = Field(
        ATSSystem.GENERIC,
        description="Specific ATS system for targeted analysis"
    )
    
    include_suggestions: bool = Field(
        True,
        description="Include improvement suggestions in response"
    )
    
    detailed_analysis: bool = Field(
        False,
        description="Include detailed section-by-section analysis"
    )
    
    keywords_only: bool = Field(
        False,
        description="Focus only on keyword matching analysis"
    )


# Job Ranking Request
class JobPreferences(BaseModel):
    """User preferences for job ranking"""
    
    location: Optional[str] = Field(
        None,
        description="Preferred location or 'remote'",
        example="San Francisco, CA"
    )
    
    salary_min: Optional[int] = Field(
        None,
        ge=0,
        description="Minimum salary requirement",
        example=120000
    )
    
    salary_max: Optional[int] = Field(
        None,
        ge=0,
        description="Maximum salary expectation",
        example=200000
    )
    
    company_size: Optional[str] = Field(
        None,
        description="Preferred company size",
        example="startup"
    )
    
    industry_preferences: Optional[List[Industry]] = Field(
        default_factory=list,
        description="Preferred industries"
    )
    
    work_type: Optional[str] = Field(
        None,
        description="Work arrangement preference",
        example="remote"
    )
    
    visa_sponsorship: Optional[bool] = Field(
        None,
        description="Requires visa sponsorship"
    )

    @validator('salary_max')
    def validate_salary_range(cls, v, values):
        if v is not None and 'salary_min' in values and values['salary_min'] is not None:
            if v < values['salary_min']:
                raise ValueError('salary_max must be greater than salary_min')
        return v


class JobRankingRequest(BaseModel):
    """Request for job ranking by interview propensity"""
    
    resume_id: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="Unique identifier for the résumé",
        example="user-123-resume-v2"
    )
    
    job_ids: List[str] = Field(
        ...,
        min_items=1,
        max_items=100,
        description="List of job IDs to rank",
        example=["job-1", "job-2", "job-3"]
    )
    
    preferences: Optional[JobPreferences] = Field(
        None,
        description="User preferences for ranking adjustment"
    )
    
    include_explanations: bool = Field(
        True,
        description="Include SHAP explanations for rankings"
    )
    
    market_context: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional market context for ranking"
    )


# Form Completion Request
class FormField(BaseModel):
    """Individual form field definition"""
    
    name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="Field name or identifier",
        example="first_name"
    )
    
    type: FormFieldType = Field(
        ...,
        description="Field input type"
    )
    
    label: Optional[str] = Field(
        None,
        max_length=200,
        description="Human-readable field label",
        example="First Name"
    )
    
    placeholder: Optional[str] = Field(
        None,
        max_length=200,
        description="Field placeholder text",
        example="Enter your first name"
    )
    
    required: bool = Field(
        False,
        description="Whether the field is required"
    )
    
    options: Optional[List[str]] = Field(
        None,
        description="Options for select/radio fields",
        example=["Option 1", "Option 2", "Option 3"]
    )
    
    validation_pattern: Optional[str] = Field(
        None,
        description="Regex pattern for validation",
        example="^[A-Za-z]+$"
    )
    
    max_length: Optional[int] = Field(
        None,
        ge=1,
        description="Maximum field length"
    )


class FormCompletionRequest(BaseModel):
    """Request for form autocompletion"""
    
    form_fields: List[FormField] = Field(
        ...,
        min_items=1,
        max_items=50,
        description="List of form fields to complete"
    )
    
    resume_context: str = Field(
        ...,
        min_length=50,
        max_length=50000,
        description="Résumé content for context extraction"
    )
    
    form_url: Optional[str] = Field(
        None,
        description="URL of the form for additional context",
        example="https://company.com/careers/apply"
    )
    
    company_name: Optional[str] = Field(
        None,
        max_length=100,
        description="Company name for context",
        example="TechCorp Inc."
    )
    
    job_title: Optional[str] = Field(
        None,
        max_length=200,
        description="Job title for context",
        example="Senior Software Engineer"
    )
    
    confidence_threshold: float = Field(
        0.7,
        ge=0.0,
        le=1.0,
        description="Minimum confidence threshold for suggestions"
    )
    
    include_alternatives: bool = Field(
        False,
        description="Include alternative suggestions for each field"
    )


# Batch processing requests
class BatchATSScoringRequest(BaseModel):
    """Batch request for multiple ATS scoring operations"""
    
    requests: List[ATSScoringRequest] = Field(
        ...,
        min_items=1,
        max_items=20,
        description="List of ATS scoring requests"
    )
    
    parallel_processing: bool = Field(
        True,
        description="Process requests in parallel"
    )


class BatchJobRankingRequest(BaseModel):
    """Batch request for multiple job ranking operations"""
    
    requests: List[JobRankingRequest] = Field(
        ...,
        min_items=1,
        max_items=10,
        description="List of job ranking requests"
    )
    
    parallel_processing: bool = Field(
        True,
        description="Process requests in parallel"
    )
