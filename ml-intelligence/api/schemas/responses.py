"""
Response schemas for CVLeap ML Intelligence API
Pydantic models for structured API responses
"""

from typing import Dict, List, Optional, Any, Union
from enum import Enum
from pydantic import BaseModel, Field


class ConfidenceLevel(str, Enum):
    """Confidence level enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


class SuggestionType(str, Enum):
    """Suggestion type enumeration"""
    CONTENT = "content"
    FORMAT = "format"
    KEYWORD = "keyword"
    STRUCTURE = "structure"
    GRAMMAR = "grammar"
    STYLE = "style"


# Base response model
class BaseResponse(BaseModel):
    """Base response model with common fields"""
    
    success: bool = Field(True, description="Whether the request was successful")
    timestamp: int = Field(..., description="Unix timestamp of response")
    processing_time_ms: float = Field(..., description="Processing time in milliseconds")
    model_version: str = Field(..., description="Model version used for inference")


# Résumé Improvement Response
class ImprovementSuggestion(BaseModel):
    """Individual improvement suggestion"""
    
    type: SuggestionType = Field(..., description="Type of suggestion")
    original_text: str = Field(..., description="Original text")
    improved_text: str = Field(..., description="Improved text")
    explanation: str = Field(..., description="Explanation for the improvement")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    section: Optional[str] = Field(None, description="Section where improvement applies")
    impact_score: float = Field(..., ge=0.0, le=1.0, description="Expected impact of change")


class ResumeMetrics(BaseModel):
    """Résumé quality metrics"""
    
    readability_score: float = Field(..., ge=0.0, le=100.0, description="Flesch reading ease score")
    keyword_density: float = Field(..., ge=0.0, le=1.0, description="Relevant keyword density")
    action_verb_ratio: float = Field(..., ge=0.0, le=1.0, description="Ratio of action verbs")
    quantification_score: float = Field(..., ge=0.0, le=1.0, description="Use of quantifiable achievements")
    length_score: float = Field(..., ge=0.0, le=1.0, description="Appropriate length score")
    formatting_score: float = Field(..., ge=0.0, le=1.0, description="Formatting quality score")


class ResumeImprovementResponse(BaseResponse):
    """Response for résumé improvement request"""
    
    improved_content: str = Field(..., description="Improved résumé content")
    original_length: int = Field(..., description="Original content length")
    improved_length: int = Field(..., description="Improved content length")
    
    overall_improvement_score: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Overall improvement score"
    )
    
    suggestions: List[ImprovementSuggestion] = Field(
        default_factory=list,
        description="List of specific improvements made"
    )
    
    metrics: ResumeMetrics = Field(..., description="Quality metrics")
    
    confidence: ConfidenceLevel = Field(..., description="Overall confidence level")
    
    model_used: str = Field(..., description="Model used (GPT-4o or DeepSeek-Coder)")
    
    keywords_added: List[str] = Field(
        default_factory=list,
        description="Keywords added for optimization"
    )
    
    sections_modified: List[str] = Field(
        default_factory=list,
        description="Sections that were modified"
    )


# ATS Scoring Response
class ATSIssue(BaseModel):
    """Individual ATS compatibility issue"""
    
    issue_type: str = Field(..., description="Type of ATS issue")
    severity: str = Field(..., description="Issue severity (low, medium, high, critical)")
    description: str = Field(..., description="Description of the issue")
    location: Optional[str] = Field(None, description="Where the issue occurs")
    suggestion: str = Field(..., description="How to fix the issue")
    impact_score: float = Field(..., ge=0.0, le=1.0, description="Impact on ATS parsing")


class ATSScoreBreakdown(BaseModel):
    """Detailed ATS score breakdown"""
    
    keyword_match: float = Field(..., ge=0.0, le=1.0, description="Keyword matching score")
    format_compatibility: float = Field(..., ge=0.0, le=1.0, description="Format compatibility")
    structure_score: float = Field(..., ge=0.0, le=1.0, description="Document structure score")
    readability: float = Field(..., ge=0.0, le=1.0, description="ATS readability score")
    contact_info: float = Field(..., ge=0.0, le=1.0, description="Contact information score")
    section_headers: float = Field(..., ge=0.0, le=1.0, description="Section header score")
    date_formats: float = Field(..., ge=0.0, le=1.0, description="Date format consistency")
    file_format: float = Field(..., ge=0.0, le=1.0, description="File format compatibility")


class KeywordAnalysis(BaseModel):
    """Keyword matching analysis"""
    
    matched_keywords: List[str] = Field(default_factory=list, description="Keywords found in résumé")
    missing_keywords: List[str] = Field(default_factory=list, description="Important missing keywords")
    keyword_frequency: Dict[str, int] = Field(default_factory=dict, description="Keyword frequency map")
    semantic_matches: List[str] = Field(default_factory=list, description="Semantic keyword matches")
    match_percentage: float = Field(..., ge=0.0, le=1.0, description="Overall keyword match percentage")


class ATSScoringResponse(BaseResponse):
    """Response for ATS compatibility scoring"""
    
    overall_score: float = Field(..., ge=0.0, le=1.0, description="Overall ATS compatibility score")
    
    score_breakdown: ATSScoreBreakdown = Field(..., description="Detailed score breakdown")
    
    compatibility_grade: str = Field(..., description="Letter grade (A, B, C, D, F)")
    
    issues: List[ATSIssue] = Field(default_factory=list, description="List of ATS issues found")
    
    keyword_analysis: KeywordAnalysis = Field(..., description="Keyword matching analysis")
    
    recommendations: List[str] = Field(
        default_factory=list,
        description="Top recommendations for improvement"
    )
    
    ats_system_specific: Dict[str, Any] = Field(
        default_factory=dict,
        description="ATS system-specific analysis"
    )
    
    estimated_parse_success: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Estimated parsing success probability"
    )


# Job Ranking Response
class JobRankingExplanation(BaseModel):
    """Explanation for job ranking using SHAP values"""
    
    feature_importance: Dict[str, float] = Field(
        default_factory=dict,
        description="Feature importance scores"
    )
    
    top_positive_factors: List[str] = Field(
        default_factory=list,
        description="Top factors increasing interview likelihood"
    )
    
    top_negative_factors: List[str] = Field(
        default_factory=list,
        description="Top factors decreasing interview likelihood"
    )
    
    explanation_text: str = Field(..., description="Human-readable explanation")


class RankedJob(BaseModel):
    """Individual ranked job result"""
    
    job_id: str = Field(..., description="Job identifier")
    rank: int = Field(..., ge=1, description="Rank position (1 = highest)")
    interview_probability: float = Field(..., ge=0.0, le=1.0, description="Interview probability")
    match_score: float = Field(..., ge=0.0, le=1.0, description="Overall match score")
    confidence: ConfidenceLevel = Field(..., description="Prediction confidence")
    
    explanation: Optional[JobRankingExplanation] = Field(
        None,
        description="Explanation for ranking"
    )
    
    key_strengths: List[str] = Field(
        default_factory=list,
        description="Key strengths for this position"
    )
    
    potential_concerns: List[str] = Field(
        default_factory=list,
        description="Potential concerns or gaps"
    )
    
    recommendation: str = Field(..., description="Application recommendation")


class JobRankingResponse(BaseResponse):
    """Response for job ranking request"""
    
    ranked_jobs: List[RankedJob] = Field(..., description="Jobs ranked by interview propensity")
    
    total_jobs_analyzed: int = Field(..., description="Total number of jobs analyzed")
    
    average_match_score: float = Field(..., ge=0.0, le=1.0, description="Average match score")
    
    market_insights: Dict[str, Any] = Field(
        default_factory=dict,
        description="Market insights and trends"
    )
    
    model_performance: Dict[str, float] = Field(
        default_factory=dict,
        description="Model performance metrics"
    )


# Form Completion Response
class FieldSuggestion(BaseModel):
    """Suggestion for form field completion"""
    
    field_name: str = Field(..., description="Form field name")
    suggested_value: str = Field(..., description="Suggested field value")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence in suggestion")
    source_section: Optional[str] = Field(None, description="Source section in résumé")
    extraction_method: str = Field(..., description="Method used for extraction")
    
    alternatives: List[str] = Field(
        default_factory=list,
        description="Alternative suggestions"
    )
    
    validation_status: str = Field(..., description="Validation status (valid, invalid, uncertain)")
    
    notes: Optional[str] = Field(None, description="Additional notes or context")


class FormCompletionResponse(BaseResponse):
    """Response for form autocompletion request"""
    
    field_suggestions: List[FieldSuggestion] = Field(
        ...,
        description="Suggestions for each form field"
    )
    
    completion_rate: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Percentage of fields that could be completed"
    )
    
    overall_confidence: float = Field(
        ..., 
        ge=0.0, 
        le=1.0, 
        description="Overall confidence in suggestions"
    )
    
    missing_information: List[str] = Field(
        default_factory=list,
        description="Information not found in résumé"
    )
    
    extraction_summary: Dict[str, Any] = Field(
        default_factory=dict,
        description="Summary of information extracted"
    )
    
    form_analysis: Dict[str, Any] = Field(
        default_factory=dict,
        description="Analysis of the form structure"
    )


# Health Check Response
class HealthResponse(BaseModel):
    """Health check response"""
    
    status: str = Field(..., description="Overall health status")
    timestamp: int = Field(..., description="Unix timestamp")
    
    services: Dict[str, bool] = Field(
        default_factory=dict,
        description="Individual service health status"
    )
    
    version: str = Field("1.0.0", description="API version")
    
    uptime_seconds: Optional[int] = Field(None, description="Uptime in seconds")


# Error Response
class ErrorResponse(BaseModel):
    """Error response model"""
    
    success: bool = Field(False, description="Always false for errors")
    error_code: str = Field(..., description="Error code")
    error_message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: int = Field(..., description="Unix timestamp")
    request_id: Optional[str] = Field(None, description="Request ID for tracking")


# Batch Response Models
class BatchATSScoringResponse(BaseModel):
    """Batch ATS scoring response"""
    
    results: List[ATSScoringResponse] = Field(..., description="Individual ATS scoring results")
    total_processed: int = Field(..., description="Total requests processed")
    success_count: int = Field(..., description="Number of successful requests")
    error_count: int = Field(..., description="Number of failed requests")
    processing_time_ms: float = Field(..., description="Total processing time")


class BatchJobRankingResponse(BaseModel):
    """Batch job ranking response"""
    
    results: List[JobRankingResponse] = Field(..., description="Individual job ranking results")
    total_processed: int = Field(..., description="Total requests processed")
    success_count: int = Field(..., description="Number of successful requests")
    error_count: int = Field(..., description="Number of failed requests")
    processing_time_ms: float = Field(..., description="Total processing time")
