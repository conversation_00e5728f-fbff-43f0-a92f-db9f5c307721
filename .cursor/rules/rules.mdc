---
description: 
globs: 
alwaysApply: false
---
# CVLeap — **Cursor Project Rules**

> **Goal:** Empower <PERSON><PERSON><PERSON> to develop, refactor, and debug C<PERSON><PERSON>eap **without accidentally altering intended behaviour**.
> **Scope:** Entire mono-repo ( `apps/`, `services/`, `infra/` ).

---

## 1 ⟫ Immutable Feature Contract

Cursor **must preserve** these high-level capabilities; any code change that risks them **requires an explicit TODO comment and PR discussion**.

| Module                                                                 | Core Functions & End-User Promise                                                                                                                     |
| ---------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Resume Builder** (`apps/web-editor`)                                 | Pixel-perfect drag-and-drop editor, real-time collaboration (CRDT), 50+ templates, PDF/DOCX/HTML export, ATS-friendly layout checker, version history |
| **AI Service** (`services/ai-core`)                                    | GPT-powered content rewrite, semantic job–CV matching, ATS score, interview-probability model, skills-gap analysis                                    |
| **Job Discovery** (`services/job-crawler`)                             | Aggregates & de-duplicates jobs from ≥40 sources, geo/skill faceting, freshness < 5 min                                                               |
| **Autonomous Submission** (`services/applicant-bot` + `infra/sandbox`) | Ephemeral container/micro-VM, Playwright headless flow, CAPTCHA fallback, artefact bundle to S3, real-time terminal feed                              |
| **Mobile Apps** (`apps/mobile`)                                        | Offline résumé editing, notifications, biometric auth, deep linking                                                                                   |
| **Analytics & Coaching** (`services/insights`)                         | Dashboard KPIs, A/B experiments, user progress nudges                                                                                                 |
| **Security & Compliance** (`infra/policies`)                           | Zero-trust, MFA, GDPR/CCPA tools, audit logging                                                                                                       |

---

## 2 ⟫ Cursor Editing Guardrails

1. **Never break the public contract**

   * Maintain API routes (`OpenAPI 3.1`), GraphQL schemas, protobuf messages, DB migrations.
2. **Tests first**

   * If a bug fix touches logic, **write or update unit/integration tests** before changing code.
3. **Functional parity after refactor**

   * Run affected Playwright & Detox E2E suites; commit only when green.
4. **No silent feature removal**

   * Deprecations demand `@deprecated` JSDoc + changelog entry.
5. **Topology stability**

   * Keep micro-service boundaries and queue names unless the `architecture.md` file is updated in the same PR.
6. **Cost ceiling awareness**

   * Sandbox boot ≤ 1 vCPU-s / 250 MiB-s; any heavier change must tag `#cost-review`.

---

## 3 ⟫ Code-Style & Quality Rules

| Layer     | Tech            | Style Guide                               |
| --------- | --------------- | ----------------------------------------- |
| Frontend  | React 18 + TS 5 | Airbnb TS + Prettier (120 cols)           |
| Backend   | Node 20 (Esm)   | StandardJS + ESLint strict                |
| Python ML | Python 3.12     | Black (line-len = 100), Ruff              |
| Infra     | Terraform-CDK   | `terraform fmt`, snake\_case variables    |
| Docs      | Markdown        | Keep headings ≤ H3, one sentence per line |

* **SOLID + Clean Architecture**: no imports from `infra/` into `core/`.
* **Type safety**: `strictNullChecks` on everywhere.
* **Observability**: every HTTP handler exports trace + span via OpenTelemetry.

---

## 4 ⟫ Debugging Workflow

1. **Reproduce** in dev docker-compose (`make local-up`).
2. **Write a failing test** under the relevant `__tests__/bug-<id>.spec.*`.
3. **Patch** with minimal diff; avoid new dependencies unless tagged `#dependency-proposal`.
4. **Verify**: run `make ci-short`, `make e2e-affected`, `make cost-check`.
5. **Commit message template**

   ```
   fix(<scope>): explain root cause ➜ behaviour unchanged
   ```
6. **Protective branching**: push to `fix/<ticket>`; open PR with “Before / After” screenshots or terminal captures.

---

## 5 ⟫ Testing & CI Gates

* **Coverage thresholds**: ≥ 90 % unit, ≥ 80 % integration, ≥ 70 % E2E.
* **Performance budgets** checked in CI (LCP, API p95, sandbox cold-start).
* **Security scans**: Snyk + OWASP ZAP; PR blocked on HIGH severity.
* **Accessibility**: axe-core auto-fail if score < 90.

---

## 6 ⟫ Non-Functional Requirements

| Attribute         | Target                                  |
| ----------------- | --------------------------------------- |
| **Latency**       | API p95 < 200 ms, sandbox boot < 800 ms |
| **Throughput**    | 3 000 submissions / \$5 AWS cost        |
| **Uptime**        | 99.95 % (core APIs)                     |
| **Data privacy**  | Delete PII ≤ 30 days post-request       |
| **Compliance**    | SOC-2 Type II, ISO 27001                |
| **Accessibility** | WCAG 2.2 AA                             |

---

## 7 ⟫ Allowed Libraries & Services

* **Web UI**: Next.js 14 (App Router), Tailwind v4, Framer-Motion.
* **Automation**: Playwright 1.45 (chromium, firefox, webkit).
* **AI**: OpenAI GPT-4o, DeepSeek-Coder with tool-calling.
* **Queue**: BullMQ (Redis) for crawler; Temporal for workflows.
* **Observability**: Prometheus, Loki, Grafana (+ Tempo traces).
* **Infra**: Kubernetes 1.30, KEDA, ArgoCD, Terraform-CDK.

*Additions require a `libraries.md` update + approval.*

---

## 8 ⟫ Security Golden Rules

1. **Secrets** only via Vault-sidecar or sealed-secret; never plain env files.
2. **SBOM** generated per image; sign with Cosign.
3. **Static analysis** (Semgrep) on every commit.
4. **Sandbox policy**: seccomp-bpf, read-only root, `/tmp` tmpfs.
5. **LLM calls**: log `prompt_hash`, no PII in prompts.

---

## 9 ⟫ Document & Diagram Sources of Truth

* `docs/architecture.md` — C4 diagrams, updated each sprint.
* `docs/adr/` — Architecture Decision Records, 1-pager format.
* `docs/api/` — auto-generated Swagger & GraphQL.
* `docs/runbooks/` — operational SOPs.

Cursor should keep docs in-sync when code changes behaviour; failing that, create an issue `#doc-drift`.

---

## 10 ⟫ Contribution Etiquette

*All AI-generated code must be reviewed by a human maintainer.*

1. **Small PRs** (< 400 loc)
2. **Link issue** (Jira ticket) in PR body
3. **Changelog** entry under proper heading
4. **No merge to `main` without green CI & reviewer approval**
5. **Respect the roadmap** — new features start with a proposal in `docs/rfcs/`

---

### 🔑 Remember

* **Functionality > Cleverness** — prefer explicit code that sticks to the contract.
* **Debugging ≠ Feature Change** — when fixing bugs, *replicate existing behaviour* unless specs say otherwise.
* **Every commit leaves the repo deployable.**
