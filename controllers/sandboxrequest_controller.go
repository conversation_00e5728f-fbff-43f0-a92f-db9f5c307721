package controllers

import (
	"context"
	"fmt"
	"time"

	"github.com/go-logr/logr"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	cvleapv1alpha1 "github.com/cvleap/platform/api/v1alpha1"
)

// SandboxRequestReconciler reconciles a SandboxRequest object
type SandboxRequestReconciler struct {
	client.Client
	Log    logr.Logger
	Scheme *runtime.Scheme
}

//+kubebuilder:rbac:groups=cvleap.io,resources=sandboxrequests,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=cvleap.io,resources=sandboxrequests/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=cvleap.io,resources=sandboxrequests/finalizers,verbs=update
//+kubebuilder:rbac:groups="",resources=pods,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups="",resources=secrets,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=networking.k8s.io,resources=networkpolicies,verbs=get;list;watch;create;update;patch;delete

func (r *SandboxRequestReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	log := r.Log.WithValues("sandboxrequest", req.NamespacedName)

	// Fetch the SandboxRequest instance
	var sandboxRequest cvleapv1alpha1.SandboxRequest
	if err := r.Get(ctx, req.NamespacedName, &sandboxRequest); err != nil {
		if errors.IsNotFound(err) {
			log.Info("SandboxRequest resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		log.Error(err, "Failed to get SandboxRequest")
		return ctrl.Result{}, err
	}

	// Handle deletion
	if sandboxRequest.DeletionTimestamp != nil {
		return r.handleDeletion(ctx, &sandboxRequest)
	}

	// Add finalizer if not present
	if !controllerutil.ContainsFinalizer(&sandboxRequest, "cvleap.io/sandbox-finalizer") {
		controllerutil.AddFinalizer(&sandboxRequest, "cvleap.io/sandbox-finalizer")
		return ctrl.Result{}, r.Update(ctx, &sandboxRequest)
	}

	// Handle different phases
	switch sandboxRequest.Status.Phase {
	case "":
		return r.handlePending(ctx, &sandboxRequest)
	case "Pending":
		return r.handleProvisioning(ctx, &sandboxRequest)
	case "Provisioning":
		return r.handleRunning(ctx, &sandboxRequest)
	case "Running":
		return r.checkCompletion(ctx, &sandboxRequest)
	case "Succeeded", "Failed":
		return r.handleCleanup(ctx, &sandboxRequest)
	default:
		log.Info("Unknown phase", "phase", sandboxRequest.Status.Phase)
		return ctrl.Result{RequeueAfter: time.Minute}, nil
	}
}

func (r *SandboxRequestReconciler) handlePending(ctx context.Context, sr *cvleapv1alpha1.SandboxRequest) (ctrl.Result, error) {
	log := r.Log.WithValues("sandboxrequest", sr.Name, "phase", "pending")

	// Select runtime based on requirements and availability
	selectedRuntime := r.selectRuntime(sr)

	// Update status
	sr.Status.Phase = "Pending"
	sr.Status.Runtime = selectedRuntime
	sr.Status.StartTime = &metav1.Time{Time: time.Now()}

	// Add condition
	condition := metav1.Condition{
		Type:               "RuntimeSelected",
		Status:             metav1.ConditionTrue,
		LastTransitionTime: metav1.Now(),
		Reason:             "RuntimeSelection",
		Message:            fmt.Sprintf("Selected runtime: %s", selectedRuntime),
	}
	sr.Status.Conditions = append(sr.Status.Conditions, condition)

	if err := r.Status().Update(ctx, sr); err != nil {
		log.Error(err, "Failed to update status")
		return ctrl.Result{}, err
	}

	return ctrl.Result{Requeue: true}, nil
}

func (r *SandboxRequestReconciler) handleProvisioning(ctx context.Context, sr *cvleapv1alpha1.SandboxRequest) (ctrl.Result, error) {
	log := r.Log.WithValues("sandboxrequest", sr.Name, "phase", "provisioning")

	// Create NetworkPolicy first
	if err := r.createNetworkPolicy(ctx, sr); err != nil {
		log.Error(err, "Failed to create NetworkPolicy")
		return ctrl.Result{}, err
	}

	// Create runtime-specific resources
	switch sr.Status.Runtime {
	case "docker-in-pod":
		return r.createDockerPod(ctx, sr)
	case "firecracker":
		return r.createFirecrackerVM(ctx, sr)
	case "kubevirt":
		return r.createKubeVirtVM(ctx, sr)
	default:
		return ctrl.Result{}, fmt.Errorf("unsupported runtime: %s", sr.Status.Runtime)
	}
}

func (r *SandboxRequestReconciler) selectRuntime(sr *cvleapv1alpha1.SandboxRequest) string {
	// Runtime selection logic
	if sr.Spec.Runtime != "" {
		return sr.Spec.Runtime
	}

	// Default to docker-in-pod for fastest startup
	return "docker-in-pod"
}

func (r *SandboxRequestReconciler) createNetworkPolicy(ctx context.Context, sr *cvleapv1alpha1.SandboxRequest) error {
	// Create NetworkPolicy for zero-trust networking
	networkPolicy := &networkingv1.NetworkPolicy{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("sandbox-%s", sr.Name),
			Namespace: sr.Namespace,
		},
		Spec: networkingv1.NetworkPolicySpec{
			PodSelector: metav1.LabelSelector{
				MatchLabels: map[string]string{
					"cvleap.io/sandbox": sr.Name,
				},
			},
			PolicyTypes: []networkingv1.PolicyType{
				networkingv1.PolicyTypeIngress,
				networkingv1.PolicyTypeEgress,
			},
			Ingress: []networkingv1.NetworkPolicyIngressRule{
				{
					From: []networkingv1.NetworkPolicyPeer{
						{
							NamespaceSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{
									"name": "cvleap-api",
								},
							},
						},
					},
				},
			},
			Egress: []networkingv1.NetworkPolicyEgressRule{
				// Allow DNS
				{
					To: []networkingv1.NetworkPolicyPeer{},
					Ports: []networkingv1.NetworkPolicyPort{
						{Port: &[]int32{53}[0], Protocol: &[]corev1.Protocol{corev1.ProtocolUDP}[0]},
						{Port: &[]int32{53}[0], Protocol: &[]corev1.Protocol{corev1.ProtocolTCP}[0]},
					},
				},
				// Allow job site domain
				{
					To: []networkingv1.NetworkPolicyPeer{
						{
							NamespaceSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{
									"name": "internet-gateway",
								},
							},
						},
					},
					Ports: []networkingv1.NetworkPolicyPort{
						{Port: &[]int32{443}[0], Protocol: &[]corev1.Protocol{corev1.ProtocolTCP}[0]},
						{Port: &[]int32{80}[0], Protocol: &[]corev1.Protocol{corev1.ProtocolTCP}[0]},
					},
				},
			},
		},
	}

	// Set owner reference
	if err := controllerutil.SetControllerReference(sr, networkPolicy, r.Scheme); err != nil {
		return err
	}

	return r.Create(ctx, networkPolicy)
}

func (r *SandboxRequestReconciler) createDockerPod(ctx context.Context, sr *cvleapv1alpha1.SandboxRequest) (ctrl.Result, error) {
	log := r.Log.WithValues("sandboxrequest", sr.Name, "runtime", "docker-in-pod")

	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("sandbox-%s", sr.Name),
			Namespace: sr.Namespace,
			Labels: map[string]string{
				"cvleap.io/sandbox": sr.Name,
				"cvleap.io/runtime": "docker-in-pod",
				"cvleap.io/job-id":  sr.Spec.JobId,
			},
		},
		Spec: corev1.PodSpec{
			RestartPolicy: corev1.RestartPolicyNever,
			SecurityContext: &corev1.PodSecurityContext{
				RunAsNonRoot: &[]bool{true}[0],
				RunAsUser:    &[]int64{1001}[0],
				FSGroup:      &[]int64{1001}[0],
			},
			Containers: []corev1.Container{
				{
					Name:  "sandbox",
					Image: "cvleap/sandbox:latest",
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							corev1.ResourceCPU:    parseQuantity(sr.Spec.Resources.CPU),
							corev1.ResourceMemory: parseQuantity(sr.Spec.Resources.Memory),
						},
						Limits: corev1.ResourceList{
							corev1.ResourceCPU:    parseQuantity(sr.Spec.Resources.CPU),
							corev1.ResourceMemory: parseQuantity(sr.Spec.Resources.Memory),
						},
					},
					Env: []corev1.EnvVar{
						{Name: "JOB_ID", Value: sr.Spec.JobId},
						{Name: "JOB_URL", Value: sr.Spec.JobSite.URL},
						{Name: "ENABLE_TERMINAL", Value: "true"},
					},
					SecurityContext: &corev1.SecurityContext{
						AllowPrivilegeEscalation: &[]bool{false}[0],
						ReadOnlyRootFilesystem:   &[]bool{false}[0],
						Capabilities: &corev1.Capabilities{
							Drop: []corev1.Capability{"ALL"},
						},
					},
				},
			},
		},
	}

	// Set owner reference
	if err := controllerutil.SetControllerReference(sr, pod, r.Scheme); err != nil {
		return ctrl.Result{}, err
	}

	if err := r.Create(ctx, pod); err != nil {
		if !errors.IsAlreadyExists(err) {
			log.Error(err, "Failed to create Pod")
			return ctrl.Result{}, err
		}
	}

	// Update status
	sr.Status.Phase = "Provisioning"
	sr.Status.PodName = pod.Name

	return ctrl.Result{RequeueAfter: time.Second * 5}, r.Status().Update(ctx, sr)
}

func (r *SandboxRequestReconciler) createFirecrackerVM(ctx context.Context, sr *cvleapv1alpha1.SandboxRequest) (ctrl.Result, error) {
	// TODO: Implement Firecracker VM creation
	// This would integrate with a Firecracker operator or direct API calls
	return ctrl.Result{}, fmt.Errorf("firecracker runtime not yet implemented")
}

func (r *SandboxRequestReconciler) createKubeVirtVM(ctx context.Context, sr *cvleapv1alpha1.SandboxRequest) (ctrl.Result, error) {
	// TODO: Implement KubeVirt VM creation
	// This would create VirtualMachine and VirtualMachineInstance resources
	return ctrl.Result{}, fmt.Errorf("kubevirt runtime not yet implemented")
}

func (r *SandboxRequestReconciler) handleRunning(ctx context.Context, sr *cvleapv1alpha1.SandboxRequest) (ctrl.Result, error) {
	// Check if pod is running
	if sr.Status.PodName != "" {
		var pod corev1.Pod
		if err := r.Get(ctx, types.NamespacedName{Name: sr.Status.PodName, Namespace: sr.Namespace}, &pod); err != nil {
			return ctrl.Result{}, err
		}

		if pod.Status.Phase == corev1.PodRunning {
			sr.Status.Phase = "Running"
			return ctrl.Result{RequeueAfter: time.Second * 10}, r.Status().Update(ctx, sr)
		}
	}

	return ctrl.Result{RequeueAfter: time.Second * 5}, nil
}

func (r *SandboxRequestReconciler) checkCompletion(ctx context.Context, sr *cvleapv1alpha1.SandboxRequest) (ctrl.Result, error) {
	// Check if sandbox has completed
	if sr.Status.PodName != "" {
		var pod corev1.Pod
		if err := r.Get(ctx, types.NamespacedName{Name: sr.Status.PodName, Namespace: sr.Namespace}, &pod); err != nil {
			return ctrl.Result{}, err
		}

		if pod.Status.Phase == corev1.PodSucceeded {
			sr.Status.Phase = "Succeeded"
			sr.Status.CompletionTime = &metav1.Time{Time: time.Now()}
			return ctrl.Result{}, r.Status().Update(ctx, sr)
		} else if pod.Status.Phase == corev1.PodFailed {
			sr.Status.Phase = "Failed"
			sr.Status.CompletionTime = &metav1.Time{Time: time.Now()}
			return ctrl.Result{}, r.Status().Update(ctx, sr)
		}
	}

	return ctrl.Result{RequeueAfter: time.Second * 10}, nil
}

func (r *SandboxRequestReconciler) handleCleanup(ctx context.Context, sr *cvleapv1alpha1.SandboxRequest) (ctrl.Result, error) {
	// Cleanup will be handled by owner references and finalizers
	return ctrl.Result{}, nil
}

func (r *SandboxRequestReconciler) handleDeletion(ctx context.Context, sr *cvleapv1alpha1.SandboxRequest) (ctrl.Result, error) {
	// Perform cleanup tasks
	// Remove finalizer
	controllerutil.RemoveFinalizer(sr, "cvleap.io/sandbox-finalizer")
	return ctrl.Result{}, r.Update(ctx, sr)
}

// SetupWithManager sets up the controller with the Manager.
func (r *SandboxRequestReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&cvleapv1alpha1.SandboxRequest{}).
		Owns(&corev1.Pod{}).
		Owns(&networkingv1.NetworkPolicy{}).
		Complete(r)
}

// Helper function to parse resource quantities
func parseQuantity(s string) resource.Quantity {
	if s == "" {
		return resource.MustParse("0")
	}
	return resource.MustParse(s)
}
